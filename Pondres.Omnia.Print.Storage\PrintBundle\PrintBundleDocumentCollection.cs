﻿using Pondres.Omnia.Print.Contracts.PrintBundle;
using System;
using System.Collections.Generic;

namespace Pondres.Omnia.Print.Storage.PrintBundle;

/// <summary>
/// This is needed because we need to pass the <see cref="PrintBundleQueuedDocumentEqualityComparer"/>. Otherwise we lose it during deserialization
/// Maybe could be cleaner
/// </summary>
public class PrintBundleQueuedDocumentHashSet : HashSet<PrintBundleQueuedDocument>
{
    public PrintBundleQueuedDocumentHashSet(IEnumerable<PrintBundleQueuedDocument> documents) :
        base(documents, new PrintBundleQueuedDocumentEqualityComparer())
    {

    }

    public PrintBundleQueuedDocumentHashSet():
        base(new PrintBundleQueuedDocumentEqualityComparer())
    {
        
    }
}

public class PrintBundleDocumentCollection
{
    public Guid Id { get; set; }
    
    public PrintBundleQueuedDocumentHashSet Documents { get; set; } = [];
    public bool Started { get; set; }
}

