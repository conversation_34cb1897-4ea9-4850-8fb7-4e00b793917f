﻿using System.Xml.Serialization;

namespace Pondres.Omnia.Print.Integrations.Navision.Model.Bundle;

public class NavisionPrintDataRule
{
    [XmlElement("Data_Regel_ID")]
    public int DataRuleId { get; set; } // TaskNumber
    [XmlElement("Type_regel")]
    public int Type { get; set; } // Operation or Article
    [XmlElement("Code")]
    public string Code { get; set; } = string.Empty;    // ArticleCode or OperationCode
    [XmlElement("Aantal")]
    public int Amount { get; set; } // DocumentCount, SheetCount (or PageCount) --> De sum van PrintBundleDetails.Documents 
    [XmlElement("Bewerkingscode")]
    public string OperationCode { get; set; } = string.Empty;   // Only filled when ArticleCode
    [XmlElement("Bew_Omschrijving")]
    public string OperationDescription { get; set; } = string.Empty;    // For now always empty
}
