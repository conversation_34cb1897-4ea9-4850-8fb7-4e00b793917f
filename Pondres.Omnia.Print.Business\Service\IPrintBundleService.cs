﻿using Pondres.Omnia.Print.Business.Model;
using Pondres.Omnia.Print.Contracts.Api.Bundle;
using Pondres.Omnia.Print.Contracts.Api.Common;
using Pondres.Omnia.Print.Contracts.PrintBundle;
using Pondres.Omnia.Print.Storage.Entities.PrintBundle;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Service;

public interface IPrintBundleService
{
    Task<PrintBundleDetails> GetBundleDetailsAsync(Guid printBundleId);

    Task<PagedList<PrintBundleListItem>> GetBundlesByFilterAsync(PrintBundleDefaultFilter filter);

    Task<PagedList<PrintBundleListItem>> GetBundlesByBatchFilterAsync(PrintBundleBatchFilter filter);

    Task<PagedList<PrintBundleListItem>> GetPrintBundlesByIdsAsync(IEnumerable<string> printBundleIds);

    Task<(bool Created, PrintBundleDetailsEntity Bundle)> GetOrCreatePrintBundleForRequestAsync(PrintBundleCreationRequest request);

    Task RegisterSecondaryBundleOnPrimaryAsync(string primaryBundleId, string secondaryBundleId);
    Task AssignDocumentGroupToBundlesAsync(PrintDocumentGroup group);
    Task UpdateWithReleaseRequestAsync(PrintBundleReleaseRequest request, string? newBatchName);
}