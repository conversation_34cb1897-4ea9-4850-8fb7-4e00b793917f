﻿using Pondres.Omnia.Print.Common.Extensions;
using Pondres.Omnia.Print.Contracts.Api.Enums;
using Pondres.Omnia.Print.Integrations.Navision.Model;
using Pondres.Omnia.Print.Integrations.Navision.Model.Bundle;
using Pondres.Omnia.Print.Integrations.Navision.Model.Common;

namespace Pondres.Omnia.Print.Integrations.Navision.Mappings;

public static class NavisionDataMappings
{
    public static List<string> ExcludedEndLocations =>
    [
        "",
        " ",
        NavisionEndLocation.FULFILMENTSPECIAAL.ToString(),
        NavisionEndLocation.GIFTS.ToString(),
        NavisionEndLocation.BOXED.ToString(),

        NavisionEndLocation.BOX.ToString(),
        NavisionEndLocation.PIGEONZONDERGIFT.ToString(),
        NavisionEndLocation.SPECIAAL.ToString(),
        NavisionEndLocation.PIGEONMETGIFT.ToString(),
        NavisionEndLocation.DEELVERPAKKINGMETGIFT.ToString(),
        NavisionEndLocation.DEELVERPAKKING.ToString(),

        "1BOXED",
        "1BOX"
    ];

    private static NavisionPrinterTypeAndPrintModeOperationCode? GetPrinterTypeAndPrintModeOperationCode(string printerType, string printMode)
    {
        var typeFC = PrinterType.FC.ToString().ToLowerInvariant();
        var typeZW = PrinterType.ZW.ToString().ToLowerInvariant();
        var typeFOOD = PrinterType.Food.ToString().ToLowerInvariant();
        var typeFLATBED = PrinterType.Flatbed.ToString().ToLowerInvariant();
        var typeCFFC = EnumExtensions.GetEnumDescription(PrinterType.CFFC).ToString().ToLowerInvariant();

        var modeDuplex = NavisionPrintMode.DUPLEX.ToString().ToLowerInvariant();
        var modeSimplex = NavisionPrintMode.SIMPLEX.ToString().ToLowerInvariant();

        var operationCodeDictionary = new Dictionary<(string, string), NavisionPrinterTypeAndPrintModeOperationCode>
        {
            { (typeFC, modeDuplex), NavisionPrinterTypeAndPrintModeOperationCode.KLEUA3DZ },
            { (typeFC, modeSimplex), NavisionPrinterTypeAndPrintModeOperationCode.KLEURA3EZ },
            { (typeZW, modeDuplex), NavisionPrinterTypeAndPrintModeOperationCode.ZWARTA3DZ },
            { (typeZW, modeSimplex), NavisionPrinterTypeAndPrintModeOperationCode.ZWARTA3EZ },
            { (typeCFFC, modeDuplex), NavisionPrinterTypeAndPrintModeOperationCode.IMPIKAKLEUR12DZ },
            { (typeCFFC, modeSimplex), NavisionPrinterTypeAndPrintModeOperationCode.IMPIKAKLEUR13EZ },
            { (typeFOOD, modeDuplex ), NavisionPrinterTypeAndPrintModeOperationCode.FOODKLEURDZ },
            { (typeFLATBED, modeDuplex), NavisionPrinterTypeAndPrintModeOperationCode.FLATBEDKLEURDZ },
        };

        return operationCodeDictionary.TryGetValue((printerType, printMode), out var value) ? value : null;
    }

    private static NavisionPackagingAndAttachmentsOperationCode GetPackagingAndAttachmentsOperationCode(string endLocation, string packingType)
    {
        var fulfillment = NavisionEndLocation.FULFILMENT.ToString().ToLowerInvariant();
        var machinalCouverteren = EnumExtensions.GetEnumDescription(NavisionEndLocation.MACHINAALCOUVERTEREN).ToString().Replace(" ", "").ToLowerInvariant();
        var handMatigCouverteren = EnumExtensions.GetEnumDescription(NavisionEndLocation.HANDMATIGCOUVERTEREN).ToString().Replace(" ", "").ToLowerInvariant();

        var allPacking = EnumExtensions.GetEnumDescription(PackingType.All).ToString().ToLowerInvariant();
        var c4 = PackingType.C4.ToString().ToLowerInvariant();
        var c5 = PackingType.C5.ToString().ToLowerInvariant();
        var c6 = PackingType.C6.ToString().ToLowerInvariant();
        var kabinet = PackingType.Kabinet.ToString().ToLowerInvariant();
        var overig = PackingType.Overig.ToString().ToLowerInvariant();

        var operationCodeDictionary = new Dictionary<(string, string), NavisionPackagingAndAttachmentsOperationCode>
            {
                { (fulfillment, allPacking), NavisionPackagingAndAttachmentsOperationCode.COUVHANDDIV },
                { (machinalCouverteren, c4), NavisionPackagingAndAttachmentsOperationCode.C4M },
                { (machinalCouverteren, c5), NavisionPackagingAndAttachmentsOperationCode.C5M },
                { (machinalCouverteren, c6), NavisionPackagingAndAttachmentsOperationCode.C6M },
                { (machinalCouverteren, kabinet), NavisionPackagingAndAttachmentsOperationCode.COUVKABM },
                { (handMatigCouverteren, c4), NavisionPackagingAndAttachmentsOperationCode.C4H },
                { (handMatigCouverteren, c5), NavisionPackagingAndAttachmentsOperationCode.C5H },
                { (handMatigCouverteren, c6), NavisionPackagingAndAttachmentsOperationCode.C6H },
                { (handMatigCouverteren, kabinet), NavisionPackagingAndAttachmentsOperationCode.CKABHSYNC },
                { (handMatigCouverteren, overig), NavisionPackagingAndAttachmentsOperationCode.COUVHANDDIV }
            };

        return operationCodeDictionary.TryGetValue((endLocation.Replace(" ", ""), packingType), out var value) ? value : NavisionPackagingAndAttachmentsOperationCode.COUVHANDDIV;
    }

    public static List<NavisionPrintDataRule> GetPackagingDataRules(SendBundleInfoToNavisionRequest request)
    {
        var dataRules = new List<NavisionPrintDataRule>();
        NavisionPackagingAndAttachmentsOperationCode? operationCode = null;

        if (request.BundleMetadata?.PackingType != null && !string.IsNullOrWhiteSpace(request.BundleMetadata.EndLocation))
        {
            operationCode = GetPackagingAndAttachmentsOperationCode(
                request.BundleMetadata.EndLocation.ToLowerInvariant(),
                request.BundleMetadata.PackingType.Value.ToString().ToLowerInvariant());
        }

        if (request.BundleMetadata != null && (!string.IsNullOrWhiteSpace(request.BundleMetadata.Packaging) || request.BundleMetadata.Attachments.Count != 0))
        {
            var navisionPrintDataCode = operationCode ?? NavisionPackagingAndAttachmentsOperationCode.COUVHANDDIV;

            var operationDataRule = new NavisionPrintDataRule
            {
                DataRuleId = request.TaskNumber,
                Type = (int)NavisionPrintDataRuleType.Operation,
                Code = navisionPrintDataCode.ToString(),
                Amount = request.DocumentCount,
                OperationCode = string.Empty,
                OperationDescription = GetOperationDescription(navisionPrintDataCode)
            };

            dataRules.Add(operationDataRule);
        }

        if (request.BundleMetadata != null && !string.IsNullOrWhiteSpace(request.BundleMetadata.Packaging))
        {
            var packagingDataRule = new NavisionPrintDataRule
            {
                DataRuleId = request.TaskNumber,
                Type = (int)NavisionPrintDataRuleType.Article,
                Code = request.BundleMetadata.Packaging,
                Amount = request.DocumentCount,
                OperationCode = operationCode?.ToString() ?? string.Empty,
                OperationDescription = GetOperationDescription(operationCode)
            };

            dataRules.Add(packagingDataRule);
        }

        if (request.BundleMetadata != null && request.BundleMetadata.Attachments.Count != 0)
        {
            var attachmentsDataRules = GetAttachmentsDataRules(request);
            dataRules.AddRange(attachmentsDataRules);
        }

        return dataRules;
    }

    public static List<NavisionPrintDataRule> GetAttachmentsDataRules(SendBundleInfoToNavisionRequest request)
    {
        var dataRules = new List<NavisionPrintDataRule>();
        NavisionPackagingAndAttachmentsOperationCode? operationCode = null;
        if (request.BundleMetadata.PackingType != null && !string.IsNullOrWhiteSpace(request.BundleMetadata.EndLocation))
        {
            operationCode = GetPackagingAndAttachmentsOperationCode(
                request.BundleMetadata.EndLocation.ToLowerInvariant(),
                request.BundleMetadata.PackingType.Value.ToString().ToLowerInvariant());
        }

        foreach (var attachmentGroup in request.BundleMetadata.Attachments.Where(x => !x.ExcludeForNavision).GroupBy(x => x.ArticleCode))
        {
            var newDataRule = new NavisionPrintDataRule
            {
                DataRuleId = request.TaskNumber,
                Type = (int)NavisionPrintDataRuleType.Article,
                Code = attachmentGroup.Key,
                Amount = attachmentGroup.Count() * request.DocumentCount,
                OperationCode = operationCode?.ToString() ?? string.Empty,
                OperationDescription = string.Empty
            };

            dataRules.Add(newDataRule);
        }

        return dataRules;
    }

    public static List<NavisionPrintDataRule> GetSheetArticleCodeDataRules(SendBundleInfoToNavisionRequest request)
    {
        var dataRules = new List<NavisionPrintDataRule>();
        NavisionPrinterTypeAndPrintModeOperationCode? operationCode = null;
        if (!string.IsNullOrWhiteSpace(request.BundleMetadata.PrinterType) && !string.IsNullOrWhiteSpace(request.BundleMetadata.PrintMode))
        {
            operationCode = GetPrinterTypeAndPrintModeOperationCode(
                request.BundleMetadata.PrinterType.ToLowerInvariant(),
                request.BundleMetadata.PrintMode.ToLowerInvariant());
        }

        var operationDataRule = new NavisionPrintDataRule
        {
            DataRuleId = request.TaskNumber,
            Type = (int)NavisionPrintDataRuleType.Operation,
            Code = operationCode?.ToString() ?? string.Empty,
            Amount = request.SheetCount,
            OperationCode = string.Empty,
            OperationDescription = string.Empty
        };

        dataRules.Add(operationDataRule);

        if (!string.IsNullOrWhiteSpace(request.BundleMetadata.SheetArticleCode))
        {
            var sheetArticleCodeDataRule = new NavisionPrintDataRule
            {
                DataRuleId = request.TaskNumber,
                Type = (int)NavisionPrintDataRuleType.Article,
                Code = request.BundleMetadata.SheetArticleCode,
                Amount = request.SheetCount,
                OperationCode = operationCode?.ToString() ?? string.Empty,
                OperationDescription = string.Empty
            };

            dataRules.Add(sheetArticleCodeDataRule);
        }

        return dataRules;
    }

    private static string GetOperationDescription(NavisionPackagingAndAttachmentsOperationCode? operationCode)
    {
        if (operationCode == NavisionPackagingAndAttachmentsOperationCode.C6H || operationCode == NavisionPackagingAndAttachmentsOperationCode.COUVHANDDIV)
        {
            return "Couverteren handmatig diversen";
        }

        return string.Empty;
    }
}
