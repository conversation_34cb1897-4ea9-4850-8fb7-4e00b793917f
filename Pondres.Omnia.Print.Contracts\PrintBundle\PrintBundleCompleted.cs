﻿using System;

namespace Pondres.Omnia.Print.Contracts.PrintBundle
{
    public class PrintBundleCompleted
    {
        public string Customer { get; set; } = string.Empty;
        public string CompletedBy { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public Guid PrintBundleId { get; set; }
        public string Status { get; set; } = string.Empty;
        public DateTimeOffset Timestamp { get; set; }
        public string BatchName { get; set; } = string.Empty;
        public string SheetArticleCode { get; set; } = string.Empty;
        public int SheetPageCount { get; set; }
        public long DocumentCount { get; set; }
        public long TotalPageCount { get; set; }
    }
}