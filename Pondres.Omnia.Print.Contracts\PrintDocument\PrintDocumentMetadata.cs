﻿using Pondres.Omnia.Print.Contracts.Api.Enums;

namespace Pondres.Omnia.Print.Contracts.PrintDocument
{
    public class PrintDocumentMetadata
    {
        public string DocumentId { get; set; } = string.Empty;
        public string? PrimaryDocumentId { get; set; }
        public PrinterType? PrinterType { get; set; }
        public int PageCount { get; set; }
        public int Quantity { get; set; }
        public string? CustomerDocumentReference { get; set; }
        public string? ReleaseSchedule { get; set; }
        public string? DocumentStatus { get; set; }
    }
}
