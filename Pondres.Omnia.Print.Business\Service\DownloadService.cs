﻿using Microsoft.Extensions.Logging;
using <PERSON>;
using Polly.Retry;
using System;
using System.Net.Http;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Service;

public class DownloadService : IDownloadService
{
    private readonly HttpClient httpClient;
    private readonly ILogger<DownloadService> logger;
    private readonly AsyncRetryPolicy retryPolicy;

    public DownloadService(
        ILogger<DownloadService> logger,
        HttpClient httpClient)
    {
        this.logger = logger;
        this.httpClient = httpClient;

        retryPolicy = Policy.Handle<Exception>().WaitAndRetryAsync(
            retryCount: 3,
            sleepDurationProvider: x => TimeSpan.FromSeconds(x),
            onRetry: (exception, waitTime) =>
            {
                logger.LogWarning(exception, "Exception during file download, waiting {waitTime} for retry", waitTime);
            });
    }

    public async Task<string> GetTextAsync(Uri uri)
    {
        logger.LogDebug("Downloading file");

        return await retryPolicy.ExecuteAsync(async () => await httpClient.GetStringAsync(uri));
    }
}