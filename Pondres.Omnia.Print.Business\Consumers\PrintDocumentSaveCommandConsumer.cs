﻿using MassTransit;
using Pondres.Omnia.Print.Business.Handler;
using Pondres.Omnia.Print.Contracts.PrintDocument;
using Serilog;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Consumers;

public class PrintDocumentSaveCommandConsumer : IConsumer<PrintDocumentSaveCommand>
{
    private readonly IPrintDocumentSaveHandler printDocumentSaveHandler;
    private readonly ILogger logger;

    public PrintDocumentSaveCommandConsumer(IPrintDocumentSaveHandler printDocumentSaveHandler, ILogger logger)
    {
        this.printDocumentSaveHandler = printDocumentSaveHandler;
        this.logger = logger;
    }

    public async Task Consume(ConsumeContext<PrintDocumentSaveCommand> context)
    {
        logger.Information("PrintDocumentSaveCommandConsumer received PrintDocumentSaveCommand for PrintBundle {PrintBundleId}", context.Message.PrintBundleId);
        
        await printDocumentSaveHandler.CreateOrGetPrintDocumentAsync(context.Message);
    }
}