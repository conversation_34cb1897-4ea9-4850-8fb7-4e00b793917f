﻿using DotNet.Testcontainers.Containers;
using Microsoft.Extensions.Logging;
using Serilog;
using Testcontainers.Azurite;
using Testcontainers.RabbitMq;
using Testcontainers.Redis;

namespace Pondres.Omnia.Print.IntegrationTests.Fixtures;

public sealed class ContainersFixture : IDisposable
{
    public RedisContainer Redis { get; }
    public RabbitMqContainer RabbitMq { get; }
    public AzuriteContainer Azurite { get; }

    private readonly IContainer[] containers;

    public ContainersFixture()
    {
        var logger = LoggerFactory.Create(builder => builder.AddSerilog()).CreateLogger("TestContainers");

        Redis = new RedisBuilder().WithLogger(logger).Build();
        RabbitMq = new RabbitMqBuilder().WithLogger(logger).WithImage("masstransit/rabbitmq").Build();
        Azurite = new AzuriteBuilder().WithLogger(logger).WithImage("mcr.microsoft.com/azure-storage/azurite:3.34.0").Build();

        containers = [<PERSON>is, RabbitMq, Azurite];
    }

    public async void Dispose() => await DisposeAllAsync();

    public async Task DisposeAllAsync() => await Task.WhenAll(containers.Select(x => x.DisposeAsync().AsTask()));

    public async Task StartAllAsync() => await Task.WhenAll(containers.Select(x => x.StartAsync()));
}
