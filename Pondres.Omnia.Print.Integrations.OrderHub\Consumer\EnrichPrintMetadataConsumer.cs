﻿using MassTransit;
using Pondres.Omnia.OrderHub.Contracts.OrderTask;
using Pondres.Omnia.Print.Integrations.OrderHub.Handler;
using Serilog;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Integrations.OrderHub.Consumer;

public class EnrichPrintMetadataConsumer : 
    IConsumer<OrderTaskBatchStartCommand>
{
    private readonly IPrintMetadataEnrichmentHandler enrichmentHandler;
    private readonly ILogger logger;

    public EnrichPrintMetadataConsumer(IPrintMetadataEnrichmentHandler enrichmentHandler, ILogger logger)
    {
        this.enrichmentHandler = enrichmentHandler;
        this.logger = logger;
    }

    public async Task Consume(ConsumeContext<OrderTaskBatchStartCommand> context)
    {
        logger.Information("EnrichPrintMetadataConsumer received OrderTaskBatchStartCommand message for Order {OrderId}", context.Message.Metadata.OrderId);
        await enrichmentHandler.HandleEnrichmentRequestAsync(context, context.Message);
    }
}