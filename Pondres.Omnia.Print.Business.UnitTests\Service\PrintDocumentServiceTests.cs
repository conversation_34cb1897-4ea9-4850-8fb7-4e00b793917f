﻿using FluentAssertions;
using Moq;
using Moq.AutoMock;
using Solude.StorageBase.Model;
using Pondres.Omnia.Print.Business.Service;
using Pondres.Omnia.Print.Contracts.PrintDocument;
using Pondres.Omnia.Print.Storage.Entities.PrintDocument;
using Pondres.Omnia.Print.Storage.Repositories;
using System;
using System.Threading.Tasks;
using Xunit;

namespace Pondres.Omnia.Print.Business.UnitTests.Service;

public class PrintDocumentServiceTests
{
    private readonly AutoMocker mocker;
    private readonly PrintDocumentService service;

    public PrintDocumentServiceTests()
    {
        mocker = new AutoMocker(MockBehavior.Strict);

        service = mocker.CreateInstance<PrintDocumentService>();
    }

    [Fact]
    public async Task AssignSequenceIdToEntityAsync_AssignsSequenceId_Correctly()
    {
        // Arrange
        var printDocumentId = Guid.NewGuid().ToString();
        var printBundleId = Guid.NewGuid();

        PrintDocumentBundleRegistrationEntity existingBundle = new()
        {
            BundleId = printBundleId,
            BatchName = "batchName",
            CreatedOn = DateTime.Now
        };

        PrintDocumentEntity documentEntity = new()
        {
            Id = printDocumentId,
            LastBundle = existingBundle,
            Bundles = [existingBundle]
        };

        var cosmosResponse = new CosmosResponse<PrintDocumentEntity>(string.Empty, documentEntity);

        PrintDocumentAssignSequenceId message = new()
        {
            SequenceId = 1,
            PrintBundleId = printBundleId,
            DocumentId = printDocumentId
        };

        mocker.GetMock<IPrintDocumentRepository>()
            .Setup(x => x.GetSingleAsync(printDocumentId, false))
            .ReturnsAsync(cosmosResponse);

        mocker.GetMock<IPrintDocumentRepository>()
            .Setup(x => x.ReplaceConsistentAsync(cosmosResponse, It.IsAny<Func<PrintDocumentEntity, bool>>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await service.AssignSequenceIdToEntityAsync(message);

        // Assert
        result.LastBundle.Should().NotBeNull();

        result.LastBundle!.SequenceId.Should().Be(1);
        result.Bundles[0].SequenceId.Should().Be(1);

        mocker.VerifyAll();
    }
}
