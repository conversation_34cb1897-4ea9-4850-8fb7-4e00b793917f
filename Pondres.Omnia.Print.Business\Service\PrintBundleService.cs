﻿using MassTransit;
using Microsoft.Extensions.Options;
using Pondres.Omnia.Print.Business.Extensions;
using Pondres.Omnia.Print.Business.Helper;
using Pondres.Omnia.Print.Business.Model;
using Pondres.Omnia.Print.Business.Storage;
using Pondres.Omnia.Print.Contracts.Api.Bundle;
using Pondres.Omnia.Print.Contracts.Api.Common;
using Pondres.Omnia.Print.Contracts.Common;
using Pondres.Omnia.Print.Contracts.PrintBundle;
using Pondres.Omnia.Print.Integrations.Navision.Model.Common;
using Pondres.Omnia.Print.Storage.Entities.PrintBundle;
using Pondres.Omnia.Print.Storage.Entities.PrintBundleStatus;
using Pondres.Omnia.Print.Storage.Repositories;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Service;

public class PrintBundleService(
    IPrintBundleDetailsRepository printBundleRepository,
    IPrintBundleBasicRepository printBundleBasicRepository,
    IPrintScheduleProvider printScheduleProvider,
    IPrintBundleStatusHistoryRepository printBundleStatusHistoryRepository,
    IBus bus,
    IOptions<PrintBundleConfiguration> printBundleConfiguration) : IPrintBundleService
{
    private readonly IRequestClient<PrintBundleCreationRequest> printBundleCreationRequestClient =
        bus.CreateRequestClient<PrintBundleCreationRequest>(timeout: TimeSpan.FromMinutes(10));

    private static readonly ConcurrentDictionary<string, Guid> bundleHashToIdCorrelations = new();

    public async Task AssignDocumentGroupToBundlesAsync(PrintDocumentGroup group)
    {
        var storageAccountName = group.Primary.CreationModel.FileInformation.StorageAccountName;
        var storageContainerName = group.Primary.CreationModel.FileInformation.StorageContainerName;

        var primaryPrintBundleId = await GetNextSheduledBundleAsync(
                customer: group.Primary.CreationModel.OrderMetadata.Customer,
                storageAccountName: storageAccountName,
                storageContainerName: storageContainerName,
                metadata: group.Primary.Metadata,
                bundleHash: group.Primary.BundleHash,
                hasSecondaryBundles: group.Secondaries.Count > 0,
                primaryBundleId: null);

        group.Primary.BundleId = primaryPrintBundleId;

        foreach (var secondaryDocument in group.Secondaries)
        {
            var secondaryPrintBundleId = await GetNextSheduledBundleAsync(
                customer: secondaryDocument.CreationModel.OrderMetadata.Customer,
                storageAccountName: storageAccountName,
                storageContainerName: storageContainerName,
                metadata: secondaryDocument.Metadata,
                bundleHash: secondaryDocument.BundleHash,
                hasSecondaryBundles: true,
                primaryBundleId: primaryPrintBundleId);

            secondaryDocument.BundleId = secondaryPrintBundleId;
        }
    }

    public async Task<PrintBundleDetails> GetBundleDetailsAsync(Guid printBundleId)
    {
        var entity = await printBundleRepository.GetSingleAsync(printBundleId.ToString(), ignoreCache: true);

        var bundleStatusEntities = await printBundleStatusHistoryRepository.GetAllByBundleIdAsync(entity.Resource.Id);

        return entity.Resource.ToDetailsDto(bundleStatusEntities);
    }

    public async Task<PagedList<PrintBundleListItem>> GetBundlesByFilterAsync(PrintBundleDefaultFilter filter)
    {
        var result = await printBundleRepository.GetByFilterAsync(filter);

        var printBundles = result.Entities.Select(x => x.ToListItemDto()).ToList();

        return new PagedList<PrintBundleListItem>
        {
            ContinuationToken = result.ContinuationToken,
            Items = printBundles
        };
    }

    private async Task<Guid> GetNextSheduledBundleAsync(
        string customer,
        string storageAccountName,
        string storageContainerName,
        PrintDocumentCreationMetadata metadata,
        string bundleHash,
        Guid? primaryBundleId,
        bool hasSecondaryBundles)
    {
        var schedule = await printScheduleProvider.GetApplicableScheduleAsync(metadata.ReleaseSchedule);

        DateTimeOffset? nextBundlePlannedOn = null;
        if (schedule != null)
        {
            var nextReleaseInfo = schedule.CalculateNextRelease(DateTimeOffset.Now);

            nextBundlePlannedOn = nextReleaseInfo.ReleaseOn;
        }

        var futureBundles = (await printBundleBasicRepository.GetFutureBundlesForHashAsync(bundleHash, nextBundlePlannedOn, primaryBundleId.ToString())).ToList();

        var futureBundle = futureBundles.FirstOrDefault();

        if (futureBundle != null)
            return Guid.Parse(futureBundle.Id);

        var nextBundleReleaseInfo = schedule?.CalculateNextRelease(futureBundles.LastOrDefault()?.PlannedOn?.AddSeconds(1) ?? DateTimeOffset.Now);

        // Check tests for reason for this. Basically real life limitation.
        if (nextBundleReleaseInfo?.PrintBundleMode == PrintBundleMode.MaxFullSheets && hasSecondaryBundles)
            throw new NotSupportedException("PrintBundleMode MaxFullSheets cannot be used on bundles that have secondary bundles!");

        // Bundle creations are handled within the manager, so we do not get any double bundles for the same date + hash
        var response = await printBundleCreationRequestClient.GetResponse<PrintBundleCreationResponse>(
            new PrintBundleCreationRequest(
            DocumentMetadata: metadata,
            BundleHash: bundleHash,
            Customer: customer,
            StorageAccountName: storageAccountName,
            StorageContainerName: storageContainerName,
            PlannedOn: nextBundleReleaseInfo?.ReleaseOn,
            PrintBundleMode: nextBundleReleaseInfo?.PrintBundleMode ?? PrintBundleMode.EmptyGroup,
            PrimaryBundleId: primaryBundleId));

        return response.Message.Id;
    }

    public async Task<(bool Created, PrintBundleDetailsEntity Bundle)> GetOrCreatePrintBundleForRequestAsync(PrintBundleCreationRequest request)
    {
        var existingBundle = await GetBundleOrDefaultAsync(request);
        if (existingBundle != null)
            return (Created: false, Bundle: existingBundle);

        var now = DateTimeOffset.Now;

        var statusDetailsEntity = new PrintBundleStatusDetailsEntity
        {
            Status = MapPrintBundleState.Created.ToString(),
            Message = "Created",
            Timestamp = now
        };

        var bundleEntity = new PrintBundleDetailsEntity
        {
            PrimaryBundleId = request.PrimaryBundleId?.ToString(),
            BatchName = request.PlannedOn.HasValue ? PrintBatchHelper.CreateBatchName(request.PlannedOn) : "Unplanned",
            BundleMode = request.PrintBundleMode.ToString(),
            PlannedOn = request.PlannedOn,
            CreatedOn = now,
            Customer = request.Customer,
            InitiatedBy = "System",
            Metadata = request.DocumentMetadata.ToMetadataEntity(),
            Hash = request.BundleHash,
            PrintFilesContainerName = request.StorageContainerName,
            PrintFilesStorageAccountName = request.StorageAccountName,
            ScheduleName = request.DocumentMetadata.ReleaseSchedule,
            State = PrintBundleStateType.Active,
            Id = Guid.NewGuid().ToString(),
            LastStatus = statusDetailsEntity,
            IsDirectlyCompleted = IsDirectlyCompleted(request.DocumentMetadata.EndLocation, request.Customer)
        };

        var statusHistoryEntity = PrintBundleStatusHistoryEntity.Create(timestamp: now, bundleId: bundleEntity.Id.ToString(), statusDetailsEntity);

        await printBundleRepository.CreateAsync(bundleEntity);
        await printBundleStatusHistoryRepository.CreateAsync(statusHistoryEntity);

        // Register the new bundle id for all following documents. This is surrounded in semaphore so it's thread safe
        bundleHashToIdCorrelations.AddOrUpdate(
            key: request.BundleHash,
            addValue: Guid.Parse(bundleEntity.Id),
            updateValueFactory: (_, _) => Guid.Parse(bundleEntity.Id));

        return (Created: true, Bundle: bundleEntity);
    }

    private async Task<PrintBundleDetailsEntity?> GetBundleOrDefaultAsync(PrintBundleCreationRequest request)
    {
        // Try to get bundle using single read (much faster)
        if (bundleHashToIdCorrelations.TryGetValue(request.BundleHash, out var bundleId))
        {
            // Bundles are updated in the Worker and we are in the Manager so we can't use the cache here. Maybe add Redis if this is still too slow.
            var existingBundle = await printBundleRepository.GetSingleOrDefaultAsync(bundleId.ToString(), ignoreCache: true);

            var documentsCanBeAddedToBundle =
                existingBundle != null &&
                existingBundle.Resource.StateName != PrintBundleStateType.Completed.ToString() &&
                existingBundle.Resource.ReleasedOn == null &&
                existingBundle.Resource.PrimaryBundleId == request.PrimaryBundleId?.ToString();

            if (documentsCanBeAddedToBundle)
                return existingBundle?.Resource;
        }

        // Fallback to slower list method in case cache was not hit
        var entity = await printBundleRepository.GetPlannedBundleAsync(
            bundleHash: request.BundleHash,
            plannedOn: request.PlannedOn,
            primaryBundleId: request.PrimaryBundleId?.ToString());

        return entity;
    }

    private bool IsDirectlyCompleted(string endLocation, string customer)
    {
        if (!string.Equals(endLocation?.Replace(" ", ""), NavisionEndLocation.HANDMATIGCOUVERTEREN.ToString(), StringComparison.OrdinalIgnoreCase) &&
            !string.Equals(endLocation?.Replace(" ", ""), NavisionEndLocation.MACHINAALCOUVERTEREN.ToString(), StringComparison.OrdinalIgnoreCase))
        {
            return true;
        }

        return !printBundleConfiguration.Value.CustomersExcludedFromDirectComplete.Exists(c => c.Equals(customer, StringComparison.InvariantCultureIgnoreCase));
    }

    public async Task<PagedList<PrintBundleListItem>> GetPrintBundlesByIdsAsync(IEnumerable<string> printBundleIds)
    {
        var result = await printBundleRepository.GetByPrintBundleIdsAsync(printBundleIds);
        var printBundles = result.Entities.Select(x => x.ToListItemDto()).ToList();

        return new PagedList<PrintBundleListItem>
        {
            ContinuationToken = result.ContinuationToken,
            Items = printBundles
        };
    }

    public async Task<PagedList<PrintBundleListItem>> GetBundlesByBatchFilterAsync(PrintBundleBatchFilter filter)
    {
        var result = await printBundleRepository.GetByBundleBatchFilterAsync(filter);

        var printBundles = result.Entities.Select(x => x.ToListItemDto()).ToList();

        return new PagedList<PrintBundleListItem>
        {
            ContinuationToken = result.ContinuationToken,
            Items = printBundles
        };
    }

    public async Task RegisterSecondaryBundleOnPrimaryAsync(string primaryBundleId, string secondaryBundleId)
    {
        var primaryBundle = await printBundleRepository.GetSingleAsync(primaryBundleId);

        primaryBundle.Resource.SecondaryBundleIds.Add(secondaryBundleId);

        await printBundleRepository.ReplaceConsistentAsync(
            response: primaryBundle,
            updateActionRetry: (x) => x.SecondaryBundleIds.Add(secondaryBundleId));
    }

    public async Task UpdateWithReleaseRequestAsync(PrintBundleReleaseRequest request, string? newBatchName)
    {
        var bundle = await printBundleRepository.GetSingleAsync(request.PrintBundleId.ToString());

        UpdateBundleWithReleaseRequest(request, bundle, newBatchName);

        await printBundleRepository.ReplaceConsistentAsync(
            response: bundle,
            updateActionRetry: (x) => UpdateBundleWithReleaseRequest(request, x, newBatchName));
    }

    private static bool UpdateBundleWithReleaseRequest(PrintBundleReleaseRequest request, PrintBundleDetailsEntity bundle, string? newBatchName)
    {
        if (newBatchName != null)
            bundle.BatchName = newBatchName;

        bundle.TaskNumber = request.TaskNumber;
        bundle.GordNumber = request.GordNumber;

        if (request.BundleOptions?.BundleMode != null)
            bundle.BundleMode = request.BundleOptions.BundleMode.ToString();

        return true;
    }
}