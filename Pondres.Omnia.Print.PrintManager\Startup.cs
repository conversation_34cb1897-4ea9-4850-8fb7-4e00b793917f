using FluentValidation;
using MassTransit;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Pondres.Omnia.Print.Business;
using Pondres.Omnia.Print.Business.Consumers;
using Pondres.Omnia.Print.Common;
using Pondres.Omnia.Print.Integrations.Navision;
using Pondres.Omnia.Print.Integrations.Nies;
using Pondres.Omnia.Print.Integrations.Nies.Consumer;
using Pondres.Omnia.Print.Integrations.OrderHub;
using Pondres.Omnia.Print.PrintManager.DataSeed;
using Pondres.Omnia.Print.PrintManager.Migrations;
using Pondres.Omnia.Print.PrintManager.Services;
using Pondres.Omnia.Print.Storage;
using Serilog;
using Solude.ApiBase.Extensions;

namespace Pondres.Omnia.Print.PrintManager;

public class Startup(IConfiguration configuration)
{
    private readonly PrintAppSettings appSettings = configuration.Get<PrintAppSettings>() ?? throw new ValidationException("PrintApp settings should not be null");

    public void Configure(IApplicationBuilder app) =>
        app.UseDefaultApiDependencies(Program.Name, appSettings);

    public void ConfigureServices(IServiceCollection services)
    {
        services.AddSingleton(Log.Logger);

        services
            .AddDefaultApiDependencies(appSettings, Program.Name, auth => auth.RequiredToken = appSettings.PrintServiceAuthToken);

        services
            .AddDefaultMassTransit(
                appSettings: appSettings,
                endpointPrefix: Program.Name,
                configureBus: configurator =>
                {
                    configurator.AddConsumer<PrintBundleBatchAssignConsumer, PrintBundleBatchDataConsumerDefinition>();
                    configurator.AddConsumer<PrintBundleCreationRequestConsumer, PrintBundleCreationRequestConsumerDefinition>();
                    configurator.AddConsumer<ExportToNiesConsumer, ExportToNiesConsumerDefinition>();
                });

        services.Configure<ExportToNiesOptions>(options =>
        {
            options.CustomersToInclude = ["KLA0102"];
            options.Enabled = appSettings.NiesEnabled;
        });

        services
            .AddNavisionIntegrationModule(appSettings)
            .AddNiesIntegrationModule(appSettings)
            .AddOrderHubIntegrationModule()
            .AddBusinessModule(appSettings)
            .AddStorageModule(appSettings);

        services
            .AddHostedService<ExcludedLocationPeriodicDGImportService>()
            .AddHostedService<LocalFileStorePeriodicCleanupHostedService>();

        services
            .AddDatabaseMigrator<CosmosMigrator>()
            .AddDataSeed<TestDataSeed>();
    }
}