﻿using FluentAssertions.Execution;
using FluentAssertions.Primitives;
using MassTransit;
using System.Linq.Expressions;

namespace Pondres.Omnia.Print.IntegrationTests.Observers;

public class TestConsumeObserver<TMessageType> :
    IConsumeMessageObserver<TMessageType> where TMessageType : class
{
    public bool MessagePreConsumed { get; private set; }

    public TaskCompletionSource<TMessageType> ConsumeCompletionSource = new();

    private readonly Func<TMessageType, bool>? filter;

    public TestConsumeObserver(Func<TMessageType, bool>? filter)
    {
        this.filter = filter;
    }

    public Task<TMessageType> MessageConsumed => ConsumeCompletionSource.Task;

    public List<TMessageType> MessagesConsumed { get; private set; } = [];

    public List<Exception> Exceptions { get; private set; } = [];

    public Task PreConsume(ConsumeContext<TMessageType> context)
    {
        if (filter == null || filter(context.Message))
            MessagePreConsumed = true;

        return Task.CompletedTask;
    }

    public Task PostConsume(ConsumeContext<TMessageType> context)
    {
        if (filter == null || filter(context.Message))
            ConsumeCompletionSource.SetResult(context.Message);

        return Task.CompletedTask;
    }

    public Task ConsumeFault(ConsumeContext<TMessageType> context, Exception exception)
    {
        if (filter == null || filter(context.Message))
            ConsumeCompletionSource.SetException(exception);

        return Task.CompletedTask;
    }
    public TestConsumeObserverAssertions<TMessageType> Should()
    {
        return new TestConsumeObserverAssertions<TMessageType>(this);
    }
}

public class TestConsumeObserverAssertions<TMessage> :
    ReferenceTypeAssertions<TestConsumeObserver<TMessage>, TestConsumeObserverAssertions<TMessage>> where TMessage : class
{
    public TestConsumeObserverAssertions(TestConsumeObserver<TMessage> instance)
        : base(instance)
    {
    }

    protected override string Identifier => "publisher";

    public TMessage HasSeenPublishedMessage(
        Expression<Func<TMessage, bool>> messageExpression, string because = "", params object[] becauseArgs)
    {
        var messageMatchFunc = messageExpression.Compile();

        var message = Subject.MessagesConsumed.FirstOrDefault(messageMatchFunc);

        Execute.Assertion
            .BecauseOf(because, becauseArgs)
            .ForCondition( message != null) // Its possible to get multiple consume's in, we dont account for that right now.
            .FailWith("Expected message was not published where {0}{reason}, but the condition was not met by:"
                        + Environment.NewLine + Environment.NewLine + "{1}.",
                messageExpression, Subject);

        return message!;
    }
}

