﻿using FluentAssertions;
using Pondres.Omnia.Print.Business.Validation;
using Pondres.Omnia.Print.IntegrationTests.PrintClient;

namespace Pondres.Omnia.Print.IntegrationTests.Fixtures.Api;

public class ReleaseScheduleApiFixture
{
    private readonly PrintManagerClient client;

    public ReleaseScheduleApiFixture(PrintManagerClient client)
    {
        this.client = client;
    }

    public static ReleaseSchedules CreateReleaseSchedule(string? id = null, string? scheduleTime = null, PrintBundleMode type = PrintBundleMode.EmptyGroup)
    {
        id ??= Guid.NewGuid().ToString();
        scheduleTime ??= "00:00";

        return new ReleaseSchedules
        {
            Active = "true",
            Id = id,
            Schedule =
            [
                new ReleasePrintSchedule
                {
                    DayOfWeek =
                    [
                        DayOfWeek.Monday.ToString(),
                        DayOfWeek.Tuesday.ToString(),
                        DayOfWeek.Wednesday.ToString(),
                        DayOfWeek.Thursday.ToString(),
                        DayOfWeek.Friday.ToString(),
                        DayOfWeek.Saturday.ToString(),
                        DayOfWeek.Sunday.ToString()
                    ],
                    Releases =
                    [
                        new PrintScheduleTime
                        {
                            Time = scheduleTime,
                            Type = type
                        }
                    ]
                }
            ],
            Type = ReleaseScheduleValidator.scheduleType
        };
    }

    public async Task CreateReleaseScheduleAsync(ReleaseSchedules? releaseSchedules = null)
    {
        releaseSchedules ??= CreateReleaseSchedule();

        var response = await client.ReleaseSchedulesCreateAsync(releaseSchedules);

        response.Message.Should().BeNullOrEmpty();
        response.IsValid.Should().BeTrue();
    }
}
