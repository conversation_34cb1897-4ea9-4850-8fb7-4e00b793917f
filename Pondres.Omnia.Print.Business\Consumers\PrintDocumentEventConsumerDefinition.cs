﻿using MassTransit;
using System;

namespace Pondres.Omnia.Print.Business.Consumers;

public class PrintDocumentEventConsumerDefinition : ConsumerDefinition<PrintDocumentEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<PrintDocumentEventConsumer> consumerConfigurator, IRegistrationContext context)
    {
        endpointConfigurator.UseRateLimit(250, TimeSpan.FromSeconds(5));

        consumerConfigurator.UseMessageRetry(config => config.Interval(10, TimeSpan.FromSeconds(30)));
    }
}
