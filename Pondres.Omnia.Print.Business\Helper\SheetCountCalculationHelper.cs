﻿using System;
using Pondres.Omnia.Print.Business.Constants;

namespace Pondres.Omnia.Print.Business.Helper;

public static class SheetCountCalculationHelper
{
    public static int CalculateSheetCount(
        string? printMode,
        int sheetPageCount,
        int totalPageCount)
    {
        if (sheetPageCount == 0) return 0;

        var printModeIsDuplex = (printMode?.ToLower() ?? PrintModeConstants.DefaultPrintMode).Equals("duplex");

        return printModeIsDuplex ?
            CalculateDuplex(totalPageCount, sheetPageCount) :
            CalculateSimplex(totalPageCount, sheetPageCount);
    }

    private static int CalculateDuplex(double totalPageCount, int sheetPageCount)
    {
        return (int)Math.Ceiling(totalPageCount / 2 / sheetPageCount);
    }

    private static int CalculateSimplex(double totalPageCount, int sheetPageCount)
    {
        return (int)Math.Ceiling(totalPageCount / sheetPageCount);
    }
}