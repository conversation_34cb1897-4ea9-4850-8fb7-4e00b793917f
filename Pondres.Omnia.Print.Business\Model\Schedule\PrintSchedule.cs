﻿using Pondres.Omnia.Print.Business.Extensions;
using Pondres.Omnia.Print.Contracts.Common;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Pondres.Omnia.Print.Business.Model.Schedule;

public class PrintScheduleRelease
{
    public DateTimeOffset ReleaseOn { get; set; }
    public PrintBundleMode PrintBundleMode { get; set; }
}

public class PrintSchedule
{
    public string Name { get; set; } = string.Empty;
    public bool Active { get; set; }
    public List<PrintScheduleDay> ReleaseDays { get; set; } = [];

    public PrintScheduleRelease CalculateNextRelease(DateTimeOffset startDate)
    {
        if (ReleaseDays.Count == 0 || ReleaseDays.TrueForAll(x => x.Times.Count == 0))
            throw new ArgumentException("Schedule is empty");

        var nextRuntime = FindNextRunTime(startDate);

        if (nextRuntime == null)
            return CalculateNextRelease(new DateTimeOffset(startDate.AddDays(1).Date, startDate.Offset));

        var nextReleaseDateTimeOffset = GetNextReleaseTime(startDate, nextRuntime);

        return new PrintScheduleRelease
        {
            ReleaseOn = nextReleaseDateTimeOffset,
            PrintBundleMode = nextRuntime.Type
        };
    }

    private PrintScheduleTime? FindNextRunTime(DateTimeOffset startDate) =>
        ReleaseDays
            .SingleOrDefault(x => x.Day == startDate.DayOfWeek.ToString())?
            .Times
            .Find(x => x.ReleaseTimeOffset >= startDate.TimeOfDay);

    private static DateTimeOffset GetNextReleaseTime(DateTimeOffset startDate, PrintScheduleTime nextRuntime)
    {
        var nextReleaseDate = startDate.Date + nextRuntime.ReleaseTimeOffset;

        return nextReleaseDate.ToDateTimeOffsetWithLocalOffset();
    }
}