﻿using FluentAssertions;
using Moq;
using Moq.AutoMock;
using Solude.StorageBase.Model;
using Pondres.Omnia.Print.Business.Service;
using Pondres.Omnia.Print.Contracts.Api.Bundle;
using Pondres.Omnia.Print.Contracts.Common;
using Pondres.Omnia.Print.Storage.Entities.PrintBundle;
using Pondres.Omnia.Print.Storage.Entities.PrintBundleStatus;
using Pondres.Omnia.Print.Storage.Entities.Print;
using Pondres.Omnia.Print.Storage.Repositories;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Xunit;
using Pondres.Omnia.Print.Contracts.Api.Enums;

namespace Pondres.Omnia.Print.Business.UnitTests.Service;

public class PrintBundleServiceTests
{
    private readonly AutoMocker mocker;
    private readonly PrintBundleService service;

    public PrintBundleServiceTests()
    {
        mocker = new AutoMocker();

        service = mocker.CreateInstance<PrintBundleService>();
    }

    [Fact]
    public async Task GetActiveBundlesPagedAsync_ReturnsValidResult()
    {
        // Arrange
        var stateType = MapPrintBundleState.BatchCreationFailure;
        var expectedOptions = new List<PrintBundleInputOption> { PrintBundleInputOption.Continue, PrintBundleInputOption.Cancel };

        var printMetadataEntity = new PrintDocumentMetadataEntity
        {
            Carrier = "carrier",
            DocumentFormat = "documentFormat",
            EndLocation = "endLocation",
            Laminate = true,
            MailDate = DateTime.Today,
            PostalDestination = "postalDestination",
            PrinterType = PrinterType.FC.ToString(),
            PrintMode = "printMode",
            PageCount = 1,
            SheetArticleCode = "sheetArticleCode",
            SheetPageCount = 1,
            SheetFormat = "sheetFormat"
        };

        var manualEventEntity = new PrintBundleManualEventEntity
        {
            Event = "event",
            Timestamp = DateTimeOffset.Now,
            Username = "username"
        };

        var statusEntity = new PrintBundleStatusDetailsEntity
        {
            IsInFailedState = false,
            Message = "message",
            Status = stateType.ToString(),
            Timestamp = DateTimeOffset.Now,
            WaitingForInput = false
        };

        var entity = new PrintBundleDetailsEntity()
        {
            BatchName = "batchName",
            CreatedOn = DateTimeOffset.Now,
            Customer = "customer",
            Quantities = new PrintBundleQuantitiesEntity
            {
                DocumentCount = 1,
                PageCount = 1,
            },
            Files = ["File1", "File2"],
            Id = "id",
            InitiatedBy = "initiatedBy",
            ManualEvents = [manualEventEntity],
            Metadata = printMetadataEntity,
            State = PrintBundleStateType.Active,
            StateName = PrintBundleStateType.Active.ToString(),
            LastStatus = statusEntity
        };

        var entities = new List<PrintBundleDetailsEntity> { entity, entity };
        var continuationToken = "continuationToken";

        var filter = new PrintBundleDefaultFilter();

        mocker.GetMock<IPrintBundleDetailsRepository>()
            .Setup(x => x.GetByFilterAsync(filter))
            .ReturnsAsync(new ListResult<PrintBundleDetailsEntity>(entities, continuationToken));

        // Act
        var result = await service.GetBundlesByFilterAsync(filter);

        // Assert
        result.ContinuationToken.Should().Be(continuationToken);
        result.Items.Should().HaveCount(entities.Count);

        var resultItem = result.Items[0];
        resultItem.BatchName.Should().Be(entity.BatchName);
        resultItem.CreatedOn.Should().Be(entity.CreatedOn);
        resultItem.Customer.Should().Be(entity.Customer);
        resultItem.Documents.Should().Be(entity.Quantities.DocumentCount);
        resultItem.Actions.Should().BeEquivalentTo(expectedOptions);
        resultItem.Files.Should().Contain(entity.Files);
        resultItem.Id.Should().Be(entity.Id);
        resultItem.InitiatedBy.Should().Be(entity.InitiatedBy);
        resultItem.Sheets.Should().Be(1);

        resultItem.Metadata.CustomerDocumentReference.Should().BeNull();

        resultItem.Metadata.Carrier.Should().Be(printMetadataEntity.Carrier);
        resultItem.Metadata.DocumentFormat.Should().Be(printMetadataEntity.DocumentFormat);
        resultItem.Metadata.EndLocation.Should().Be(printMetadataEntity.EndLocation);
        resultItem.Metadata.Laminate.Should().Be(printMetadataEntity.Laminate);
        resultItem.Metadata.MailDate.Should().Be(printMetadataEntity.MailDate);
        resultItem.Metadata.PostalDestination.Should().Be(printMetadataEntity.PostalDestination);
        resultItem.Metadata.PrinterType.ToString().Should().Be(printMetadataEntity.PrinterType);
        resultItem.Metadata.PrintMode.Should().Be(printMetadataEntity.PrintMode);
        resultItem.Metadata.SheetArticleCode.Should().Be(printMetadataEntity.SheetArticleCode);
        resultItem.Metadata.SheetPageCount.Should().Be(printMetadataEntity.SheetPageCount);
        resultItem.Metadata.SheetFormat.Should().Be(printMetadataEntity.SheetFormat);

        resultItem.Status.IsInFailedState.Should().Be(statusEntity.IsInFailedState);
        resultItem.Status.Message.Should().Be(statusEntity.Message);
        resultItem.Status.Status.ToString().Should().Be(statusEntity.Status);
        resultItem.Status.Timestamp.Should().Be(statusEntity.Timestamp);
        resultItem.Status.WaitingForInput.Should().Be(statusEntity.WaitingForInput);

        mocker.VerifyAll();
    }

    [Fact]
    public async Task GetBundleDetailsAsync_ReturnsValidResult()
    {
        // Arrange
        var printBundleId = Guid.NewGuid();

        var printMetadataEntity = new PrintDocumentMetadataEntity
        {
            Carrier = "carrier",
            DocumentFormat = "documentFormat",
            EndLocation = "endLocation",
            Laminate = true,
            MailDate = DateTime.Today,
            PostalDestination = "postalDestination",
            PrinterType = PrinterType.FC.ToString(),
            PrintMode = "printMode",
            SheetArticleCode = "sheetArticleCode",
            SheetPageCount = 1,
            PageCount = 1,
            SheetFormat = "sheetFormat"
        };

        var manualEventEntity = new PrintBundleManualEventEntity
        {
            Event = "event",
            Timestamp = DateTimeOffset.Now,
            Username = "username"
        };

        var statusDetailsEntity = new PrintBundleStatusDetailsEntity
        {
            IsInFailedState = false,
            Message = "message",
            Status = MapPrintBundleState.Completed.ToString(),
            Timestamp = DateTimeOffset.Now,
            WaitingForInput = false
        };

        var statusEntity = PrintBundleStatusHistoryEntity.Create(
            timestamp: DateTimeOffset.Now,
            bundleId: printBundleId.ToString(),
            statusDetailsEntity);

        var entity = new PrintBundleDetailsEntity()
        {
            BatchName = "batchName",
            CreatedOn = DateTimeOffset.Now,
            Customer = "customer",
            Quantities = new PrintBundleQuantitiesEntity
            {
                DocumentCount = 1,
                PageCount = 1,
            },
            Files = ["File1", "File2"],
            Id = printBundleId.ToString(),
            InitiatedBy = "initiatedBy",
            ManualEvents = [manualEventEntity],
            Metadata = printMetadataEntity,
            State = PrintBundleStateType.Completed,
            StateName = PrintBundleStateType.Completed.ToString(),
            LastStatus = statusDetailsEntity
        };

        mocker.GetMock<IPrintBundleDetailsRepository>()
            .Setup(x => x.GetSingleAsync(printBundleId.ToString(), true))
            .ReturnsAsync(new CosmosResponse<PrintBundleDetailsEntity>(string.Empty, entity));

        mocker.GetMock<IPrintBundleStatusHistoryRepository>()
            .Setup(x => x.GetAllByBundleIdAsync(printBundleId.ToString()))
            .ReturnsAsync([statusEntity]);

        // Act
        var result = await service.GetBundleDetailsAsync(printBundleId);

        // Assert
        result.BatchName.Should().Be(entity.BatchName);
        result.CreatedOn.Should().Be(entity.CreatedOn);
        result.Customer.Should().Be(entity.Customer);
        result.Documents.Should().Be(entity.Quantities.DocumentCount);
        result.Files.Should().Contain(entity.Files);
        result.Id.Should().Be(entity.Id);
        result.InitiatedBy.Should().Be(entity.InitiatedBy);
        result.Sheets.Should().Be(1);
        result.StatusHistory.Should().ContainSingle();
        result.ManualEvents.Should().ContainSingle();

        result.Metadata.CustomerDocumentReference.Should().BeNull();

        result.Metadata.Carrier.Should().Be(printMetadataEntity.Carrier);
        result.Metadata.DocumentFormat.Should().Be(printMetadataEntity.DocumentFormat);
        result.Metadata.EndLocation.Should().Be(printMetadataEntity.EndLocation);
        result.Metadata.Laminate.Should().Be(printMetadataEntity.Laminate);
        result.Metadata.MailDate.Should().Be(printMetadataEntity.MailDate);
        result.Metadata.PostalDestination.Should().Be(printMetadataEntity.PostalDestination);
        result.Metadata.PrinterType.ToString().Should().Be(printMetadataEntity.PrinterType);
        result.Metadata.PrintMode.Should().Be(printMetadataEntity.PrintMode);
        result.Metadata.SheetArticleCode.Should().Be(printMetadataEntity.SheetArticleCode);
        result.Metadata.SheetPageCount.Should().Be(printMetadataEntity.SheetPageCount);
        result.Metadata.SheetFormat.Should().Be(printMetadataEntity.SheetFormat);

        result.Status.Should().NotBeNull();

        result.Status!.IsInFailedState.Should().Be(statusDetailsEntity.IsInFailedState);
        result.Status.Message.Should().Be(statusDetailsEntity.Message);
        result.Status.Status.ToString().Should().Be(statusDetailsEntity.Status);
        result.Status.Timestamp.Should().Be(statusDetailsEntity.Timestamp);
        result.Status.WaitingForInput.Should().Be(statusDetailsEntity.WaitingForInput);

        var statusHistoryEntry = result.StatusHistory[0];
        statusHistoryEntry.IsInFailedState.Should().Be(statusDetailsEntity.IsInFailedState);
        statusHistoryEntry.Message.Should().Be(statusDetailsEntity.Message);
        statusHistoryEntry.Status.ToString().Should().Be(statusDetailsEntity.Status);
        statusHistoryEntry.Timestamp.Should().Be(statusDetailsEntity.Timestamp);
        statusHistoryEntry.WaitingForInput.Should().Be(statusDetailsEntity.WaitingForInput);

        var manualEventEntry = result.ManualEvents[0];
        manualEventEntry.Timestamp.Should().Be(manualEventEntity.Timestamp);
        manualEventEntry.Event.Should().Be(manualEventEntity.Event);
        manualEventEntry.Username.Should().Be(manualEventEntity.Username);

        mocker.VerifyAll();
    }

    [Fact]
    public async Task GetCompletedBundlesPagedAsync_ReturnsValidResult()
    {
        // Arrange
        var printMetadataEntity = new PrintDocumentMetadataEntity
        {
            Carrier = "carrier",
            DocumentFormat = "documentFormat",
            EndLocation = "endLocation",
            Laminate = true,
            MailDate = DateTime.Today,
            PostalDestination = "postalDestination",
            PrinterType = PrinterType.FC.ToString(),
            PrintMode = "printMode",
            SheetArticleCode = "sheetArticleCode",
            SheetPageCount = 1,
            PageCount = 1,
            SheetFormat = "sheetFormat"
        };

        var manualEventEntity = new PrintBundleManualEventEntity
        {
            Event = "event",
            Timestamp = DateTimeOffset.Now,
            Username = "username"
        };

        var statusEntity = new PrintBundleStatusDetailsEntity
        {
            IsInFailedState = false,
            Message = "message",
            Status = MapPrintBundleState.Completed.ToString(),
            Timestamp = DateTimeOffset.Now,
            WaitingForInput = false
        };

        var entity = new PrintBundleDetailsEntity()
        {
            BatchName = "batchName",
            CreatedOn = DateTimeOffset.Now,
            Customer = "customer",
            Quantities = new PrintBundleQuantitiesEntity
            {
                DocumentCount = 1,
                PageCount = 1,
            },
            Files = ["File1", "File2"],
            Id = "id",
            InitiatedBy = "initiatedBy",
            ManualEvents = [manualEventEntity],
            Metadata = printMetadataEntity,
            State = PrintBundleStateType.Completed,
            StateName = PrintBundleStateType.Completed.ToString(),
            LastStatus = statusEntity
        };

        var entities = new List<PrintBundleDetailsEntity> { entity, entity };
        var continuationToken = "continuationToken";

        var filter = new PrintBundleDefaultFilter();

        mocker.GetMock<IPrintBundleDetailsRepository>()
            .Setup(x => x.GetByFilterAsync(filter))
            .ReturnsAsync(new ListResult<PrintBundleDetailsEntity>(entities, continuationToken));

        // Act
        var result = await service.GetBundlesByFilterAsync(filter);

        // Assert
        result.ContinuationToken.Should().Be(continuationToken);
        result.Items.Should().HaveCount(entities.Count);

        var resultItem = result.Items[0];
        resultItem.BatchName.Should().Be(entity.BatchName);
        resultItem.CreatedOn.Should().Be(entity.CreatedOn);
        resultItem.Customer.Should().Be(entity.Customer);
        resultItem.Documents.Should().Be(entity.Quantities.DocumentCount);
        resultItem.Files.Should().Contain(entity.Files);
        resultItem.Id.Should().Be(entity.Id);
        resultItem.InitiatedBy.Should().Be(entity.InitiatedBy);
        resultItem.Sheets.Should().Be(1);

        resultItem.Metadata.CustomerDocumentReference.Should().BeNull();

        resultItem.Metadata.Carrier.Should().Be(printMetadataEntity.Carrier);
        resultItem.Metadata.DocumentFormat.Should().Be(printMetadataEntity.DocumentFormat);
        resultItem.Metadata.EndLocation.Should().Be(printMetadataEntity.EndLocation);
        resultItem.Metadata.Laminate.Should().Be(printMetadataEntity.Laminate);
        resultItem.Metadata.MailDate.Should().Be(printMetadataEntity.MailDate);
        resultItem.Metadata.PostalDestination.Should().Be(printMetadataEntity.PostalDestination);
        resultItem.Metadata.PrinterType.ToString().Should().Be(printMetadataEntity.PrinterType);
        resultItem.Metadata.PrintMode.Should().Be(printMetadataEntity.PrintMode);
        resultItem.Metadata.SheetArticleCode.Should().Be(printMetadataEntity.SheetArticleCode);
        resultItem.Metadata.SheetPageCount.Should().Be(printMetadataEntity.SheetPageCount);
        resultItem.Metadata.SheetFormat.Should().Be(printMetadataEntity.SheetFormat);

        resultItem.Status.IsInFailedState.Should().Be(statusEntity.IsInFailedState);
        resultItem.Status.Message.Should().Be(statusEntity.Message);
        resultItem.Status.Status.ToString().Should().Be(statusEntity.Status);
        resultItem.Status.Timestamp.Should().Be(statusEntity.Timestamp);
        resultItem.Status.WaitingForInput.Should().Be(statusEntity.WaitingForInput);

        mocker.VerifyAll();
    }
}