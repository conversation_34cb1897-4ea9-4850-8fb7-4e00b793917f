﻿using Azure.Storage.Blobs;
using Azure.Storage.Sas;
using Pondres.Omnia.Print.Storage.DataStorage.Model;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Storage.DataStorage;

public interface ISharedAccessUrlGenerator
{
    Task<TemporaryDataUri> GetSharedAccessSignatureForContainerAsync(
        BlobServiceClient client, BlobContainerClient container, BlobContainerSasPermissions containerSasPermissions, TimeSpan expireTime);

    Task<TemporaryDataUri> GetSharedAccessSignatureUrlForBlobAsync(
        BlobServiceClient client, BlobContainerClient container, string filePath, TimeSpan expireTime);

    Task<List<TemporaryDataUri>> GetSharedAccessSignatureUrlForBlobsInFolderAsync(
                        BlobServiceClient client, BlobContainerClient container, string folderName, TimeSpan expireTime);
}