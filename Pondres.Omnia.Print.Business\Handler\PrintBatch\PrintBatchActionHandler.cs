﻿using Microsoft.Extensions.Options;
using Pondres.Omnia.Print.Business.Extensions;
using Pondres.Omnia.Print.Contracts.Api.Batch;
using Pondres.Omnia.Print.Contracts.Api.Bundle;
using Pondres.Omnia.Print.Contracts.Api.Common;
using Pondres.Omnia.Print.Contracts.Options;
using Pondres.Omnia.Print.Storage.Repositories;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Handler.PrintBatch;

public class PrintBatchActionHandler : IPrintBatchActionHandler
{
    private readonly IPrintBundleDetailsRepository printBundleDetailsRepository;
    private readonly PrintBatchOptions options;

    public PrintBatchActionHandler(IOptions<PrintBatchOptions> options, IPrintBundleDetailsRepository printBundleDetailsRepository)
    {
        this.options = options.Value;

        this.printBundleDetailsRepository = printBundleDetailsRepository;
    }

    public async Task<PagedList<DGCodeBatch>> GetBatchesAsync(PrintBundleDefaultFilter filter)
    {
        var result = await printBundleDetailsRepository.GetByFilterAsync(filter);

        var bundleListItems = result.Entities.Select(x => x.ToListItemDto()).ToList();

        var batches = CreateBatches(bundleListItems);

        return new PagedList<DGCodeBatch>
        {
            ContinuationToken = result.ContinuationToken,
            Items = batches
        };
    }

    private List<DGCodeBatch> CreateBatches(List<PrintBundleListItem> bundleListItems)
    {
        var batchList = new List<DGCodeBatch>();

        var batchNames = bundleListItems
            .DistinctBy(x => x.BatchName)
            .Select(x => x.BatchName);

        var bundlesByBatchName = bundleListItems
            .ToLookup(b => b.BatchName);

        foreach (var batchName in batchNames)
        {
            batchList.AddRange(CreateDGCodeBatch(bundlesByBatchName[batchName]));
        }

        return batchList;
    }

    private List<DGCodeBatch> CreateDGCodeBatch(IEnumerable<PrintBundleListItem> bundleListItems)
    {
        var batchList = new List<DGCodeBatch>();
        var distinctCustomers = bundleListItems.DistinctBy(b => b.Customer).Select(x => x.Customer);

        foreach (var customer in distinctCustomers)
        {
            var bundlesForCustomer = bundleListItems.Where(x => x.Customer.ToLower() == customer.ToLower()).ToList();
            var bundlesWithoutDGCode = bundlesForCustomer.Where(x => string.IsNullOrWhiteSpace(x.Metadata.DGCode)).ToList();
            var distinctDGCodes = bundlesForCustomer.Where(x => !string.IsNullOrWhiteSpace(x.Metadata.DGCode)).DistinctBy(b => b.Metadata.DGCode).Select(x => x.Metadata.DGCode).ToList();

            if (bundlesWithoutDGCode.Count > 0)
            {
                var batchWithoutDGCode = new DGCodeBatch
                {
                    BatchName = bundlesWithoutDGCode[0].BatchName,
                    Customer = customer,
                    GordNumber = bundlesWithoutDGCode[0].GordNumber,
                    DGCode = string.Empty,
                    Status = DetermineStatus(bundlesWithoutDGCode),
                    Details = [],
                    PrintBundles = bundlesWithoutDGCode
                };

                CreateDgCodeBatchDetails(bundlesWithoutDGCode, batchWithoutDGCode);
                batchList.Add(batchWithoutDGCode);
            }

            if (distinctDGCodes.Count > 0)
            {
                foreach (var DGCode in distinctDGCodes)
                {
                    var bundlesWithDGCode = bundlesForCustomer.Where(x => x.Metadata.DGCode == DGCode).ToList();
                    var batchWithDGCode = new DGCodeBatch
                    {
                        BatchName = bundlesWithDGCode[0].BatchName,
                        Customer = customer,
                        GordNumber = bundlesWithDGCode[0].GordNumber,
                        DGCode = DGCode,
                        Status = DetermineStatus(bundlesWithDGCode),
                        Details = [],
                        PrintBundles = bundlesWithDGCode
                    };

                    CreateDgCodeBatchDetails(bundlesWithDGCode, batchWithDGCode);
                    batchList.Add(batchWithDGCode);
                }
            }
        }

        return batchList;
    }

    private void CreateDgCodeBatchDetails(IEnumerable<PrintBundleListItem> bundles, DGCodeBatch batch)
    {
        var distinctPrinterTypes = bundles.DistinctBy(b => b.Metadata.PrinterType).Select(b => b.Metadata.PrinterType).ToList();

        foreach (var printerType in distinctPrinterTypes)
        {
            var bundlesForPrinterType = bundles.Where(x => x.Metadata.PrinterType == printerType).ToList();

            batch.Details.Add(new DGCodeBatchDetails
            {
                PrintBundleIds = bundlesForPrinterType.Select(b => b.Id).ToList(),
                Sheets = bundlesForPrinterType.Sum(b => b.Sheets),
                DocumentCount = bundlesForPrinterType.Sum(b => b.Documents),
                PrintFileLocation = bundlesForPrinterType[0].ToPrintFolderPath(options.PrintFolderPath),
                PrinterType = printerType,
                Status = DetermineStatus(bundlesForPrinterType),
                PrinterDetails = CreatePrinterDetails(bundlesForPrinterType)
            });
        }
    }

    private List<PrinterDetails> CreatePrinterDetails(List<PrintBundleListItem> bundles)
    {
        var printerDetails = new List<PrinterDetails>();
        foreach (var bundle in bundles)
        {
            foreach (var fileName in bundle.Files)
            {
                printerDetails.Add(new PrinterDetails
                {
                    PrintFileName = fileName,
                    PrintFilePath = bundle.ToPrintFilePath(options.PrintFolderPath, fileName)
                });
            }
        }

        return printerDetails;
    }

    private static string DetermineStatus(IEnumerable<PrintBundleListItem> bundles)
    {
        return bundles.Select(b => b.Status.Status).OrderBy(x => x).FirstOrDefault().ToString();
    }
}
