﻿using System.Xml.Serialization;

namespace Pondres.Omnia.Print.Integrations.Navision.Model.Bundle;

public class NavisionPrintBundle
{
    // Property names are in Dutch for the sake of Navision
    [XmlElement("ID")]
    public string DgCode { get; set; } = string.Empty;
    [XmlElement("BatchCode")]
    public string BatchName { get; set; } = string.Empty;
    [XmlElement("GroepID")]
    public int TaskNumber { get; set; }
    [XmlElement("Mailaantal")]
    public int DocumentCount { get; set; }
    [XmlElement("Maildatum")]
    public string MailDate { get; set; } = string.Empty;
    [XmlElement("Postverspreider")]
    public string Carrier { get; set; } = string.Empty;
    [XmlElement("Papierformaat")]
    public string PaperFormat { get; set; } = string.Empty;// SheetFormat
    [XmlElement("Drukformaat")]
    public string PrintFormat { get; set; } = string.Empty; // SheetFormat
    [XmlElement("Eindformaat")]
    public string EndFormat { get; set; } = string.Empty;// DocumentFormat
    [XmlElement("Vouwformaat")]
    public string FoldFormat { get; set; } = string.Empty; // DocumentFormat

    [XmlElement("Data_Regel")]
    public List<NavisionPrintDataRule> DataRules { get; set; } = [];
}
