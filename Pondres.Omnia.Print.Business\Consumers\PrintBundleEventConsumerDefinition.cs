﻿using MassTransit;
using System;

namespace Pondres.Omnia.Print.Business.Consumers;

public class PrintBundleEventConsumerDefinition : ConsumerDefinition<PrintBundleEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<PrintBundleEventConsumer> consumerConfigurator, IRegistrationContext context)
    {
        consumerConfigurator.UseMessageRetry(config => config.Interval(10, TimeSpan.FromSeconds(30)));
    }
}