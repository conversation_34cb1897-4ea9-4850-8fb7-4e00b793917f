﻿using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.DependencyInjection;
using Pondres.Omnia.Print.Common;
using Pondres.Omnia.Print.Storage.DataStorage;
using Pondres.Omnia.Print.Storage.Repositories;

namespace Pondres.Omnia.Print.Storage;

public static class StorageModule
{
    public static IServiceCollection AddStorageModule(this IServiceCollection services, PrintAppSettings appSettings)
    {
        // (cosmos) Repositories
        services.AddSingleton(x => new CosmosClient(appSettings.CosmosDbEndpoint));

        services.AddTransient<IPrintDocumentRepository, PrintDocumentRepository>();
        services.AddTransient<IPrintBundleDetailsRepository, PrintBundleDetailsRepository>();
        services.AddTransient<IPrintBundleBasicRepository, PrintBundleBasicRepository>();
        services.AddTransient<IPrintSheetMappingRepository, PrintSheetMappingRepository>();
        services.AddTransient<IDivisionMappingRepository, DivisionMappingRepository>();
        services.AddTransient<IPrintBundleStatusHistoryRepository, PrintBundleStatusHistoryRepository>();
        services.AddTransient<IPrintConfigurationRepository, PrintConfigurationRepository>();
        services.AddTransient<INavisionArticleRepository, NavisionArticleRepository>();

        services.AddTransient<IReleaseSchedulesRepository, ReleaseSchedulesRepository>();
        services.AddTransient<IOverrideSchedulesRepository, OverrideSchedulesRepository>();

        services.AddSingleton<IDataStorageRepositoryFactory, DataStorageRepositoryFactory>();

        return services;
    }
}