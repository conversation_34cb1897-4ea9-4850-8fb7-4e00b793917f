﻿using Pondres.Omnia.Print.Storage.DataStorage.Model;
using System;
using System.IO;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Storage.DataStorage;

public interface IDataStorageContainerRepository
{
    Task CreateIfNotExistsAsync();

    Task DownloadToStreamAsync(Stream stream, string filePath);

    Task<TemporaryDataUri> GetSharedAccessSignatureUrlForBlobAsync(string blobName, TimeSpan expireTime);

    Task<TemporaryDataUri> GetSharedAccessSignatureUrlForContainerAsync(TimeSpan expireTime);

    Task<string> GetTextAsync(string filePath);

    Task UploadTextAsync(string fileContents, string filePath, bool overwrite = false);
}