﻿using Pondres.Omnia.Print.Contracts.Api.Document;
using Pondres.Omnia.Print.Storage.Entities.PrintDocument;
using Solude.StorageBase;
using Solude.StorageBase.Model;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Storage.Repositories;

public interface IPrintDocumentRepository : ISinglePartitionBaseRepository<PrintDocumentEntity>
{
    Task<List<PrintDocumentEntity>> GetAllForOrderIdAsync(string customer, Guid orderId, bool throwIfNoDocuments = true);

    Task<List<PrintDocumentEntity>> GetAllForPrintBundleAsync(Guid bundleId);

    Task<ListResult<PrintDocumentEntity>> GetByFilterAsync(PrintDocumentListFilter filter);

    Task<ListResult<PrintDocumentEntity>> GetByBatchFilterAsync(PrintDocumentBatchListFilter filter);
}