﻿using Microsoft.AspNetCore.Mvc;
using Pondres.Omnia.Print.Business.Service;
using Pondres.Omnia.Print.Contracts.Api.Bundle;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.PrintManager.Controllers;

[Route("stats")]
public class StatisticsController : Controller
{
    private readonly IPrintBundleStatisticsService printBundleService;

    public StatisticsController(IPrintBundleStatisticsService printBundleService)
    {
        this.printBundleService = printBundleService;
    }

    [HttpPost]
    [Route("bundleTotalsForDateRange")]
    [ProducesResponseType(200, Type = typeof(PrintBundleTotalsResult))]
    public async Task<IActionResult> GetTotalsForDateRange([FromBody] PrintBundleDateRangeFilter filter)
    {
        return Ok(await printBundleService.GetTotalsForDateRangeAsync(filter));
    }
}