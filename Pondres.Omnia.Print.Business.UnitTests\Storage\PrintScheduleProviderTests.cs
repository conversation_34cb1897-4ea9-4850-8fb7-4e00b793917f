﻿using FluentAssertions;
using Moq;
using Moq.AutoMock;
using Pondres.Omnia.Print.Business.Storage;
using Pondres.Omnia.Print.Contracts.Common;
using Pondres.Omnia.Print.Storage.Entities.Schedule;
using Pondres.Omnia.Print.Storage.Repositories;
using Solude.StorageBase.Model;
using System;
using System.Linq;
using System.Threading.Tasks;
using Xunit;

namespace Pondres.Omnia.Print.Business.UnitTests.Storage;

public class PrintScheduleProviderTests
{
    private readonly AutoMocker mocker;
    private readonly IPrintScheduleProvider provider;

    public PrintScheduleProviderTests()
    {
        mocker = new AutoMocker(MockBehavior.Strict);
        provider = mocker.CreateInstance<PrintScheduleProvider>();

    }

    [Fact]
    public async Task OverrideSchedule_Overrides_DaysOfWeek_Correctly()
    {
        // * Arrange
        var normalSchedule = new ReleaseSchedulesEntity
        {
            Id = "TEST_SCHEDULE",
            Active = true,
            Schedule =
            [
                new()
                {
                    DayOfWeek =
                    [
                        "Monday",
                        "Tuesday",
                        "Wednesday",
                        "Thursday",
                        "Friday",
                        "Saturday",
                        "Sunday"
                    ],
                    Releases =
                    [
                        new()
                        {
                            Time = "12:00",
                            Type = PrintBundleMode.MaxFullSheets.ToString()
                        },
                        new()
                        {
                            Time = "10:30",
                            Type = PrintBundleMode.EmptyGroup.ToString()
                        }
                    ]
                }
            ]
        };

        var overrideSchedule = new ReleaseSchedulesEntity
        {
            Id = "TEST_SCHEDULE",
            Active = true,
            Schedule =
            [
                new()
                {
                    Dates =
                    [
                        DateTime.Now.Date.ToString(),
                        DateTime.Now.Date.AddDays(3).ToString()
                    ],
                    Releases =
                    [
                        new()
                        {
                            Time = "16:00",
                            Type = PrintBundleMode.MaxFullSheets.ToString()
                        }
                    ]
                }
            ]
        };

        mocker.GetMock<IReleaseSchedulesRepository>()
            .Setup(x => x.GetSingleOrDefaultAsync("TEST_SCHEDULE", false))
            .ReturnsAsync(new CosmosResponse<ReleaseSchedulesEntity>(string.Empty, normalSchedule));

        mocker.GetMock<IOverrideSchedulesRepository>()
            .Setup(x => x.GetSingleOrDefaultAsync("TEST_SCHEDULE", false))
            .ReturnsAsync((new CosmosResponse<ReleaseSchedulesEntity>(string.Empty, overrideSchedule)));

        // * Act
        var schedule = await provider.GetApplicableScheduleAsync("TEST_SCHEDULE");

        // * Assert
        schedule.Should().NotBeNull();

        schedule!.ReleaseDays.Count(x => x.Times[0].ReleaseTimeOffset == TimeSpan.Parse("16:00")).Should().Be(2);
    }
}