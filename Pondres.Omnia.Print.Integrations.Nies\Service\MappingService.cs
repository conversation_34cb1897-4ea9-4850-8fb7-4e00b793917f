﻿using Pondres.Omnia.Print.Storage.Entities.Mapping;
using Pondres.Omnia.Print.Storage.Repositories;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Integrations.Nies.Service;

public class MappingService : IMappingService
{
    private readonly IDivisionMappingRepository divisionMappingRepository;
    public MappingService(IDivisionMappingRepository divisionMappingRepository)
    {
        this.divisionMappingRepository = divisionMappingRepository;
    }
    public async Task<List<DivisionMappingEntity>> GetMappingsAsync(IEnumerable<string> customers)
    {
        return await divisionMappingRepository.GetAllForCustomersAsync(customers);
    }
}
