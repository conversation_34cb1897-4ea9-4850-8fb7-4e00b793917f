﻿using MassTransit;
using Pondres.Omnia.Print.Business.Service;
using Pondres.Omnia.Print.Contracts.PrintBundle;
using Serilog;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Consumers;

public class PrintBundleEventConsumer :
    IConsumer<PrintBundleImpositioningCompleted>,
    IConsumer<PrintBundleCompleted>,
    IConsumer<PrintBundleSkipped>,
    IConsumer<PrintBundleManualEvent>,
    IConsumer<PrintBundleQuantitiesUpdated>
{
    private readonly IPrintBundleEventService printBundleService;
    private readonly ILogger logger;

    public PrintBundleEventConsumer(IPrintBundleEventService printBundleService, ILogger logger)
    {
        this.printBundleService = printBundleService;
        this.logger = logger;
    }

    public async Task Consume(ConsumeContext<PrintBundleImpositioningCompleted> context)
    {
        logger.Information("PrintBundleEventConsumer received PrintBundleImpositioningCompleted for PrintBundle {PrintBundleId}", context.Message.PrintBundleId);
        await printBundleService.UpdateFileListAsync(context.Message);
    }

    public async Task Consume(ConsumeContext<PrintBundleCompleted> context)
    {
        logger.Information("PrintBundleEventConsumer received PrintBundleCompleted for PrintBundle {PrintBundleId}", context.Message.PrintBundleId);
        await printBundleService.RegisterBundleCompletionAsync(context.Message);
    }

    public async Task Consume(ConsumeContext<PrintBundleManualEvent> context)
    {
        logger.Information("PrintBundleEventConsumer received PrintBundleManualEvent for PrintBundle {PrintBundleId}", context.Message.PrintBundleId);
        await printBundleService.RegisterManualEventAsync(context.Message);
    }

    public async Task Consume(ConsumeContext<PrintBundleSkipped> context)
    {
        logger.Information("PrintBundleEventConsumer received PrintBundleSkipped for PrintBundle {PrintBundleId}", context.Message.PrintBundleId);
        await printBundleService.RegisterBundleSkippedAsync(context.Message);
    }

    public async Task Consume(ConsumeContext<PrintBundleQuantitiesUpdated> context)
    {
        logger.Information("PrintBundleEventConsumer received PrintBundleQuantitiesUpdated for PrintBundle {PrintBundleId}", context.Message.PrintBundleId);
        await printBundleService.UpdateQuantitiesAsync(context.Message);
    }
}