﻿using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.Caching.Memory;
using Solude.StorageBase;
using Pondres.Omnia.Print.Storage.Configuration;
using Pondres.Omnia.Print.Storage.Entities.PrintBundleStatus;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Storage.Repositories;

public class PrintBundleStatusHistoryRepository : PartitionedBaseRepository<PrintBundleStatusHistoryEntity>, IPrintBundleStatusHistoryRepository
{
    protected override bool CanWrite => true;

    public PrintBundleStatusHistoryRepository(CosmosClient cosmosClient, IMemoryCache memoryCache) :
        base(cosmosClient, CosmosConfiguration.PrintDatabaseName, CosmosConfiguration.PrintBundleStatusHistoryContainerName, memoryCache)
    {
    }

    public async Task<List<PrintBundleStatusHistoryEntity>> GetAllByBundleIdAsync(string bundleId)
    {
        var query = BuildQueryByBundleId(bundleId);

        var results = new List<PrintBundleStatusHistoryEntity>();
        string? continuationToken = null;

        do
        {
            var currentResults = await GetEntityListAsync(query, continuationToken, 100);

            results.AddRange(currentResults.Entities);

            continuationToken = currentResults.ContinuationToken;
        }
        while (!string.IsNullOrEmpty(continuationToken));

        return results;
    }

    public async Task<List<PrintBundleStatusHistoryEntity>> GetBundlesWithStatusWithinTimeRangeAsync(
        DateTimeOffset timeRangeStart,
        DateTimeOffset timeRangeEnd,
        string status)
    {
        var query = container.GetItemLinqQueryable<PrintBundleStatusHistoryEntity>()
                .Where(x => x.CreatedOn > timeRangeStart && x.CreatedOn < timeRangeEnd && x.Details.Status == status)
                .OrderBy(x => x.CreatedOn);

        var result = await GetEntityListAsync(query, null, -1);

        return result.Entities;
    }

    protected override MemoryCacheEntryOptions CreateCacheOptions() =>
        new()
        {
            SlidingExpiration = TimeSpan.FromMinutes(1)
        };

    protected override string GetCacheKey(string id, string partitionKeyValue) => $"pbundlestatus_{id}_{partitionKeyValue}";

    private IOrderedQueryable<PrintBundleStatusHistoryEntity> BuildQueryByBundleId(string bundleId) =>
        container.GetItemLinqQueryable<PrintBundleStatusHistoryEntity>()
            .Where(x => x.BundleId == bundleId)
            .OrderBy(x => x.Id);
}
