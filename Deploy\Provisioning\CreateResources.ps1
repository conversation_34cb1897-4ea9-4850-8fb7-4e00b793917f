# Create Omnia Resources for orderhub. managed Identity, KeyVault, availability test, Redis Cache
[CmdletBinding()]
param (
    $ENV
)
# variables REDIS_SKU, REDIS_VM_SIZE are managed from seperate file for each enviririonment
. .\variables-$ENV.ps1

$LOCATION        = "westeurope" 
$RESOURCEGROUP  = "rg-omnia-"+ $ENV
$RESOURCE_NAME  = "-print-"+ $ENV  

if($ENV -eq 'prod')
{
    $RESOURCE_NAME  = "-print2-"+ $ENV
}
az servicebus namespace create --resource-group $RESOURCEGROUP --name sb$RESOURCE_NAME --location $LOCATION

Write-Host "##vso[task.setvariable variable=PRINT_SERVICE_BUS_URI;]sb://sb$RESOURCE_NAME.servicebus.windows.net/"