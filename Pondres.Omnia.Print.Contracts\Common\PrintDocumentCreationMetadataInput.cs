﻿using Pondres.Omnia.Print.Contracts.Api.Enums;
using System;
using System.Collections.Generic;

namespace Pondres.Omnia.Print.Contracts.Common
{
    public class PrintDocumentCreationMetadataInput
    {
        public string DocumentFormat { get; set; } = string.Empty;
        public string Carrier { get; set; } = string.Empty;
        public string EndLocation { get; set; } = string.Empty;
        public string CustomerDocumentReference { get; set; } = string.Empty;
        public string? DocumentStatus { get; set; }
        public bool Laminate { get; set; }
        public DateTimeOffset? MailDate { get; set; }
        public int Quantity { get; set; }
        public PrinterType? PrinterType { get; set; }
        public string PrintMode { get; set; } = string.Empty;
        public string PostalDestination { get; set; } = string.Empty;
        public int SheetPageCount { get; set; }
        public string SheetArticleCode { get; set; } = string.Empty;
        public string? DGCode { get; set; }
        public string SheetFormat { get; set; } = string.Empty;
        public string? Packaging { get; set; }
        public int PageCount { get; set; }
        public string? PackingName { get; set; }
        public PackingType? PackingType { get; set; }
        public string ReleaseSchedule { get; set; } = string.Empty;
        public string? SheetArticleDescription { get; set; }
        public List<PrintDocumentCreationMetadataAttachmentInput> Attachments { get; set; } = new();

        public bool IsPrimary { get; set; }
        public string? PrintBundleSort { get; set; }
    }
}
