﻿using Pondres.Omnia.Print.Business.Helper;
using Pondres.Omnia.Print.Contracts.Api.Bundle;
using Pondres.Omnia.Print.Contracts.Common;
using Pondres.Omnia.Print.Storage.Entities.PrintBundle;
using Pondres.Omnia.Print.Storage.Entities.PrintBundleStatus;
using System.Collections.Generic;
using System.Linq;

namespace Pondres.Omnia.Print.Business.Extensions;

public static class PrintBundleEntityExtensions
{
    public static PrintBundleDetails ToDetailsDto(this PrintBundleDetailsEntity entity, List<PrintBundleStatusHistoryEntity> statusEntities)
    {
        var sheetCount = SheetCountCalculationHelper.CalculateSheetCount(
            entity.Metadata.PrintMode,
            entity.Metadata.SheetPageCount,
            entity.Quantities.PageCount);
        var statusList = statusEntities.Select(x => x.Details.ToDto()).ToList();
        var currentStatus = statusList.OrderByDescending(x => x.Timestamp).FirstOrDefault();

        return new PrintBundleDetails
        {
            Id = entity.Id,
            BatchName = entity.BatchName,
            Customer = entity.Customer,
            CreatedOn = entity.CreatedOn,
            PlannedOn = entity.PlannedOn,
            ReleasedOn = entity.ReleasedOn,
            InitiatedBy = entity.InitiatedBy,
            Documents = entity.Quantities.DocumentCount,
            TotalPageCount = entity.Quantities.PageCount,
            Sheets = sheetCount,
            Status = currentStatus ?? new(),
            Metadata = entity.Metadata.ToPrintDocumentCreationMetadata(),
            GordNumber = entity.GordNumber,
            TaskNumber = entity.TaskNumber ?? 0,
            Files = entity.Files,
            StatusHistory = statusList,
            ManualEvents = entity.ManualEvents.ToDto().ToList(),
            SecondaryBundles = entity.SecondaryBundleIds.Select(x => x.ToString()).ToList(),
            IsDirectlyCompleted = entity.IsDirectlyCompleted,
        };
    }

    public static PrintBundleListItem ToListItemDto(this PrintBundleDetailsEntity entity)
    {
        var sheetCount = SheetCountCalculationHelper.CalculateSheetCount(
            entity.Metadata.PrintMode,
            entity.Metadata.SheetPageCount,
            entity.Quantities.PageCount);

        return new PrintBundleListItem
        {
            Id = entity.Id,
            BatchName = entity.BatchName,
            Customer = entity.Customer,
            CreatedOn = entity.CreatedOn,
            InitiatedBy = entity.InitiatedBy,
            Actions = entity.ToActionsDto(),
            AvailablePrintBundleModes = GetAvailableReleaseModes(entity),
            Documents = entity.Quantities.DocumentCount,
            TotalPageCount = entity.Quantities.PageCount,
            Sheets = sheetCount,
            Status = entity.LastStatus.ToDto(),
            Metadata = entity.Metadata.ToPrintDocumentCreationMetadata(quantity: entity.Quantities.Quantity),
            GordNumber = entity.GordNumber,
            TaskNumber = entity.TaskNumber ?? 0,
            Files = entity.Files
        };
    }

    private static List<PrintBundleMode> GetAvailableReleaseModes(PrintBundleDetailsEntity entity)
    {
        var modes = new List<PrintBundleMode>();

        if (!CanBeReleased(entity))
            return modes;

        if (entity.SecondaryBundleIds.Count != 0)
            modes.Add(PrintBundleMode.MaxFullSheets);

        modes.Add(PrintBundleMode.EmptyGroup);

        return modes;
    }

    public static List<PrintBundleInputOption> ToActionsDto(this PrintBundleDetailsEntity entity)
    {
        var actions = new List<PrintBundleInputOption>();

        actions.IfAdd(entity.LastStatus.CanBeContinued(), PrintBundleInputOption.Continue);
        actions.IfAdd(entity.CanBeReleased(), PrintBundleInputOption.Release);
        actions.IfAdd(entity.LastStatus.CanBeCancelled(), PrintBundleInputOption.Cancel);
        actions.IfAdd(entity.LastStatus.CanRemoveDocument(), PrintBundleInputOption.RemoveDocument);
        actions.IfAdd(entity.LastStatus.CanConfirmPrint(), PrintBundleInputOption.PrintConfirmation);

        return actions;
    }

    public static bool CanBeReleased(this PrintBundleDetailsEntity entity)
    {
        return entity.LastStatus.Status == MapPrintBundleState.WaitingForStart.ToString() && entity.Metadata.IsPrimary;
    }

}