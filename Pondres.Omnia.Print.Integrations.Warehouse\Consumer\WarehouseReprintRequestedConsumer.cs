﻿using MassTransit;
using Pondres.Omnia.Print.Business.CommandHandler;
using Pondres.Omnia.Warehouse.Contracts.Outbound;
using Serilog;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Integrations.OrderHub.Consumer;

public class WarehouseReprintRequestedConsumer :
    IConsumer<WarehouseReprintRequested>
{
    private readonly RequeueDocumentsForOrderCommandHandler requeueDocumentsForOrderCommandHandler;
    private readonly ILogger logger;

    public WarehouseReprintRequestedConsumer(RequeueDocumentsForOrderCommandHandler requeueDocumentsForOrderCommandHandler, ILogger logger)
    {
        this.requeueDocumentsForOrderCommandHandler = requeueDocumentsForOrderCommandHandler;
        this.logger = logger;
    }

    public async Task Consume(ConsumeContext<WarehouseReprintRequested> context)
    {
        logger.Information("RequeueDocumentsForOrderCommandHand<PERSON> received WarehouseReprintRequested message for Order {OrderId}", context.Message.OrderId);
        await requeueDocumentsForOrderCommandHandler.HandleAsync(new RequeueDocumentsForOrderCommand(context, context.Message.OrderId, context.Message.Customer));
    }
}