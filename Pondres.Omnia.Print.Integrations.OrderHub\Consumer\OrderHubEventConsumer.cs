﻿using MassTransit;
using Pondres.Omnia.OrderHub.Contracts.Order;
using Pondres.Omnia.Print.Integrations.OrderHub.Handler;
using Serilog;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Integrations.OrderHub.Consumer;
public class OrderHubEventConsumer :
    IConsumer<OrderRejectedByCancellation>
{
    private readonly IOrderCancellationHandler orderCancellationHandler;
    private readonly ILogger logger;

    public OrderHubEventConsumer(IOrderCancellationHandler orderCancellationHandler, ILogger logger)
    {
        this.orderCancellationHandler = orderCancellationHandler;
        this.logger = logger;
    }

    public async Task Consume(ConsumeContext<OrderRejectedByCancellation> context)
    {
        logger.Information("OrderHubEventConsumer received OrderRejectedByCancellation message for Order {OrderId}", context.Message.OrderId);
        await orderCancellationHandler.HandleOrderCancellationAsync(context.Message.OrderId, context.Message.Customer);
    }
}
