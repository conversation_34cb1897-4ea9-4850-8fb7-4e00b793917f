﻿using MassTransit;
using Pondres.Omnia.Print.Business.Service;
using Pondres.Omnia.Print.Contracts.PrintDocument;
using Serilog;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Consumers;

public class PrintDocumentEventConsumer : 
    IConsumer<PrintDocumentAssignSequenceId>,
    IConsumer<PrintDocumentRequeued>
{
    private readonly IPrintDocumentService printDocumentService;
    private readonly ILogger logger;

    public PrintDocumentEventConsumer(IPrintDocumentService printDocumentService, ILogger logger)
    {
        this.printDocumentService = printDocumentService;
        this.logger = logger;
    }

    public async Task Consume(ConsumeContext<PrintDocumentAssignSequenceId> context)
    {
        logger.Information("PrintDocumentEventConsumer received PrintDocumentAssignSequenceId for PrintBundle {PrintBundleId}", context.Message.PrintBundleId);
        await printDocumentService.AssignSequenceIdToEntityAsync(context.Message);
    }

    public async Task Consume(ConsumeContext<PrintDocumentRequeued> context)
    {
        logger.Information("PrintDocumentEventConsumer received PrintDocumentRequeued for PrintBundle {PrintBundleId}", context.Message.PrintBundleId);
        await printDocumentService.RegisterPrintDocumentRequeuedAsync(context.Message);
    }
}
