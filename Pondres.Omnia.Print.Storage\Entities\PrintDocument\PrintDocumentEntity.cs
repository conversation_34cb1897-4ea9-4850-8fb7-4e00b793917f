﻿using Newtonsoft.Json;
using Solude.StorageBase.Model;
using Pondres.Omnia.Print.Contracts.PrintDocument;
using Pondres.Omnia.Print.Storage.Configuration;
using System;
using System.Collections.Generic;

namespace Pondres.Omnia.Print.Storage.Entities.PrintDocument;

public class PrintDocumentEntity : CosmosEntity
{
    [JsonProperty("orderMetadata")] public OrderMetadataEntity OrderMetadata { get; set; } = new();

    [JsonProperty("fileInformation")] public PrintFileInformationEntity FileInformation { get; set; } = new();

    [JsonProperty("customerDocumentReference")]
    public string? CustomerDocumentReference { get; set; }

    [JsonProperty("createdOn")]
    public DateTimeOffset CreatedOn { get; set; }

    [JsonProperty("lastUpdatedOn")]
    public DateTimeOffset LastUpdatedOn { get; set; }

    [JsonProperty("quantity")]
    public int Quantity { get; set; }

    [JsonProperty("bundles")]
    public List<PrintDocumentBundleRegistrationEntity> Bundles { get; set; } = [];

    [JsonProperty("lastBundle")]
    public PrintDocumentBundleRegistrationEntity? LastBundle { get; set; }

    [JsonIgnore]
    public override string PartitionKeyValue => Type;

    [JsonProperty("type")]
    public string Type => CosmosConfiguration.PrintDocumentPartitionValue;

    [JsonProperty("version")]
    public override string Version { get; set; } = CosmosConfiguration.PrintDocumentVersion;

    [JsonProperty("reprintRequestHistory")]
    public List<PrintDocumentReprintRequest> ReprintRequestHistory { get; set; } = [];

    [JsonProperty("primaryDocumentId")]
    public string? PrimaryDocumentId { get; set; }

    [JsonProperty(PropertyName = "_etag")]
    public string ETag { internal get; set; } = "";   // for CosmosDB so we can get the ETag but doesn't get serialized to CosmosDB

    public string GetETag() => ETag;
}