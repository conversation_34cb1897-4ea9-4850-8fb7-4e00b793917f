﻿using ExportServiceClient;
using Polly;
using Polly.Retry;
using Pondres.Omnia.Print.Integrations.Nies.Model;
using Serilog;
using System;
using System.ServiceModel;
using System.Threading.Tasks;
using System.Xml;

namespace Pondres.Omnia.Print.Integrations.Nies.Service;

public class NiesExportService : INiesExportService
{
    private readonly ExportServiceSoapChannel proxy;
    private readonly string endpointaddress;
    private readonly AsyncRetryPolicy retryPolicy;

    public NiesExportService(string endpointaddress)
    {
        this.endpointaddress = endpointaddress;

        proxy = CreateProxy();

        retryPolicy = Policy.Handle<Exception>().WaitAndRetryAsync(
            retryCount: 3,
            sleepDurationProvider: x => TimeSpan.FromSeconds(x),
            onRetry: (exception, waitTime) =>
            {
                Log.Warning("Exception of type {ExceptionType} during upload niesexport, waiting {WaitingTime} for retry. Exception message: {ExceptionMessage}", exception.GetType().Name, waitTime, exception.Message);
            });
    }

    private ExportServiceSoapChannel CreateProxy()
    {
        var binding = new BasicHttpBinding
        {
            SendTimeout = TimeSpan.FromSeconds(600),
            MaxBufferSize = int.MaxValue,
            MaxReceivedMessageSize = int.MaxValue,
            AllowCookies = true,
            ReaderQuotas = XmlDictionaryReaderQuotas.Max
        };
        binding.Security.Mode = BasicHttpSecurityMode.None;
        var address = new EndpointAddress(endpointaddress);
        var factory = new ChannelFactory<ExportServiceSoapChannel>(binding, address);
        return factory.CreateChannel();
    }

    public async Task SendAsync(OrderToSendResult orderToSend)
    {
        Log.Debug("Uploading nies export");
        await retryPolicy.ExecuteAsync(async () => await proxy.SaveOrdersAsync(orderToSend.Orders, orderToSend.Division));
    }
}
