﻿using MassTransit;
using Pondres.Omnia.Print.Business.Handler;
using Pondres.Omnia.Print.Business.StateMachine.PrintBundle.Model;
using System;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Activities.Bundle;

public class PrintBundleSendToImpositioningActivity : BaseInstanceActivity<PrintBundleState>
{
    public const string impositioningQueueName = "quadient_cloud_composition_print_impositioning";

    private readonly IPrintBundleImpositioningHandler impositioningHandler;

    public PrintBundleSendToImpositioningActivity(IPrintBundleImpositioningHandler impositioningHandler)
    {
        this.impositioningHandler = impositioningHandler;
    }

    public override string ActivityName => "send-print-bundle-to-impositioning-activity";

    protected override async Task ExecuteAsync(BehaviorContext<PrintBundleState> context)
    {
        var sendEndpoint = await context.GetSendEndpoint(new Uri($"queue:{impositioningQueueName}"));

        await impositioningHandler.SendBundleToImpositioningAsync(sendEndpoint,
            customer: context.Saga.Customer,
            printBundleId: context.Saga.CorrelationId,
            gordNumber: context.Saga.GordNumber,
            taskNumber: context.Saga.TaskNumber);
    }
}