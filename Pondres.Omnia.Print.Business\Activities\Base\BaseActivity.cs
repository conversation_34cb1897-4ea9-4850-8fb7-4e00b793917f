﻿using MassTransit;
using System;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Activities
{
    public abstract class BaseActivity<TInstance, TData> : IStateMachineActivity<TInstance, TData>
        where TInstance : class, SagaStateMachineInstance
        where TD<PERSON> : class
    {
        public abstract string ActivityName { get; }

        public void Accept(StateMachineVisitor visitor)
        {
            visitor.Visit(this);
        }

        public async Task Execute(BehaviorContext<TInstance, TData> context, IBehavior<TInstance, TData> next)
        {
            await ExecuteAsync(context);

            await next.Execute(context).ConfigureAwait(false);
        }

        public async Task Faulted<TException>(BehaviorExceptionContext<TInstance, TData, TException> context, IBehavior<TInstance, TData> next) where TException : Exception
        {
            await next.Faulted(context).ConfigureAwait(false);
        }

        public void Probe(ProbeContext context)
        {
            context.CreateScope(ActivityName);
        }

        protected abstract Task ExecuteAsync(BehaviorContext<TInstance, TData> context);
    }
}