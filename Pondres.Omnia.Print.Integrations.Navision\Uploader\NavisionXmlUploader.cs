﻿using Pondres.Omnia.Print.Integrations.Navision.Model.Bundle;
using System.Xml;
using System.Xml.Serialization;

namespace Pondres.Omnia.Print.Integrations.Navision.Uploader;

public class NavisionXmlUploader : INavisionXmlUploader
{
    private readonly XmlSerializerNamespaces emptyNamespace;
    private readonly XmlSerializer dgProjectSerializer;

    private readonly string xmlStorageFolder;

    public NavisionXmlUploader(string xmlStorageFolder)
    {
        this.xmlStorageFolder = xmlStorageFolder;

        emptyNamespace = new XmlSerializerNamespaces(new[] { XmlQualifiedName.Empty }); // add to remove metadata from namespace
        dgProjectSerializer = new XmlSerializer(typeof(NavisionDgProjects));
    }

    public void UploadNavisionPrintBundle(NavisionPrintBundle printBundle, string gordNumber, bool isExcludedLocation)
    {
        var filePath = isExcludedLocation ? CreateFilePathForExcludedLocationPrintBundle(printBundle) : CreateFilePathForPrintBundle(printBundle, gordNumber);
        using StreamWriter stream = new(filePath, false);
        var DGProjects = new NavisionDgProjects { Projects = [printBundle] };

        dgProjectSerializer.Serialize(stream, DGProjects, emptyNamespace);
    }

    private string CreateFilePathForPrintBundle(NavisionPrintBundle printBundle, string gordNumber)
    {
        var fileName = $"{gordNumber}-{printBundle.TaskNumber:D4}-{DateTime.Now:yyyyMMdd_hhmmss}.xml";
        var filePath = Path.Combine(xmlStorageFolder, fileName);
        return filePath;
    }

    private string CreateFilePathForExcludedLocationPrintBundle(NavisionPrintBundle printBundle)
    {
        var fileName = !string.IsNullOrWhiteSpace(printBundle.DgCode)
            ? $"{printBundle.DgCode}-{DateTimeOffset.Now:dd-HH-mm}-{Guid.NewGuid()}.xml"
            : $"{DateTimeOffset.Now:dd-HH-mm}-{Guid.NewGuid()}.xml";

        return Path.Combine(xmlStorageFolder, fileName);
    }
}
