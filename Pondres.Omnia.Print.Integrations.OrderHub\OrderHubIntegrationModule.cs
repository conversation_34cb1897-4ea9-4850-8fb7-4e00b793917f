﻿using Microsoft.Extensions.DependencyInjection;
using Pondres.Omnia.Print.Integrations.OrderHub.Handler;

namespace Pondres.Omnia.Print.Integrations.OrderHub;

public static class OrderHubIntegrationModule
{
    public static IServiceCollection AddOrderHubIntegrationModule(this IServiceCollection services)
    {
        services.AddTransient<IPrintMetadataEnrichmentHandler, PrintMetadataEnrichmentHandler>();
        services.AddTransient<IOrderCancellationHandler, OrderCancellationHandler>();

        return services;
    }
}
