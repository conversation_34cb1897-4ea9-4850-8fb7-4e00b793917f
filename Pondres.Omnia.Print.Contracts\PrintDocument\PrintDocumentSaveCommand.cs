﻿using Pondres.Omnia.Print.Contracts.Common;
using System;

namespace Pondres.Omnia.Print.Contracts.PrintDocument
{
    public class PrintDocumentSaveCommand
    {
        public PrintDocumentMetadata DocumentMetadata { get; set; } = new();
        public PrintDocumentFileInformation FileInformation { get; set; } = new();
        public DateTimeOffset MailDate { get; set; }
        public OrderMetadata OrderMetadata { get; set; } = new();

        public Guid PrintBundleId { get; set; }
        public DateTimeOffset Timestamp { get; set; }
    }
}