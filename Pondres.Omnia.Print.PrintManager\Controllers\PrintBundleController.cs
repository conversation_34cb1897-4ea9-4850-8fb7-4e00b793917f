﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Solude.StorageBase.Exceptions;
using Pondres.Omnia.Print.Business.Exceptions;
using Pondres.Omnia.Print.Business.Handler;
using Pondres.Omnia.Print.Business.Handler.Navision;
using Pondres.Omnia.Print.Business.Handler.PrintBatch;
using Pondres.Omnia.Print.Business.Helper;
using Pondres.Omnia.Print.Business.Service;
using Pondres.Omnia.Print.Contracts.Api.Batch;
using Pondres.Omnia.Print.Contracts.Api.Bundle;
using Pondres.Omnia.Print.Contracts.Api.Common;
using Pondres.Omnia.Print.Contracts.PrintBundle;
using Pondres.Omnia.Print.Storage.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.PrintManager.Controllers;

[Route("bundle")]
public class PrintBundleController : Controller
{
    private readonly IPrintBundleService printBundleService;
    private readonly IPrintBundleActionHandler printBundleActionHandler;
    private readonly IPrintBundleDetailsRepository printBundleDetailsRepository;
    private readonly IDGImportHandler dGImportHandler;
    private readonly IPrintBatchActionHandler batchActionHandler;

    public PrintBundleController(
        IPrintBundleService printBundleService,
        IPrintBundleActionHandler printBundleActionHandler,
        IDGImportHandler dGImportHandler,
        IPrintBatchActionHandler batchActionHandler,
        IPrintBundleDetailsRepository printBundleDetailsRepository)
    {
        this.printBundleService = printBundleService;
        this.printBundleActionHandler = printBundleActionHandler;
        this.dGImportHandler = dGImportHandler;
        this.batchActionHandler = batchActionHandler;
        this.printBundleDetailsRepository = printBundleDetailsRepository;
    }

    [HttpPost]
    [Route("list")]
    [ProducesResponseType(200, Type = typeof(PagedList<PrintBundleListItem>))]
    public async Task<IActionResult> GetBundlesPagedAsync([FromBody] PrintBundleDefaultFilter filter)
    {
        try
        {
            return Ok(await printBundleService.GetBundlesByFilterAsync(filter));
        }
        catch (EntityNotFoundException)
        {
            return Ok(new PagedList<PrintBundleListItem>());
        }
    }

    [HttpGet]
    [Route("raw")]
    [ProducesResponseType(200, Type = typeof(PrintBundleRawDetails))]
    [ProducesResponseType(404, Type = typeof(string))]
    public async Task<IActionResult> RawDetailsAsync(Guid printBundleId)
    {
        try
        {
            var bundle = await printBundleDetailsRepository.GetSingleAsync(printBundleId.ToString());

            var rawJson = Newtonsoft.Json.JsonConvert.SerializeObject(bundle);
            var printbundle = new PrintBundleRawDetails(rawJson);

            return Ok(printbundle);
        }
        catch (EntityNotFoundException)
        {
            return NotFound($"Could not find print bundle for id {printBundleId}");
        }
    }

    [HttpPost]
    [Route("batchList")]
    [ProducesResponseType(200, Type = typeof(PagedList<DGCodeBatch>))]
    public async Task<IActionResult> GetPagedBatchesAsync([FromBody] PrintBundleDefaultFilter filter)
    {
        try
        {
            return Ok(await batchActionHandler.GetBatchesAsync(filter));
        }
        catch (EntityNotFoundException)
        {
            return Ok(new PagedList<DGCodeBatch>());
        }
    }

    [HttpPost]
    [Route("bundleBatchList")]
    [ProducesResponseType(200, Type = typeof(PagedList<PrintBundleListItem>))]
    public async Task<IActionResult> GetBatchBundlesPagedAsync([FromBody] PrintBundleBatchFilter filter)
    {
        try
        {
            return Ok(await printBundleService.GetBundlesByBatchFilterAsync(filter));
        }
        catch (EntityNotFoundException)
        {
            return Ok(new PagedList<PrintBundleListItem>());
        }
    }

    [HttpPost]
    [Route("cancel")]
    [ProducesResponseType(200, Type = typeof(PrintBundleCancelResponse))]
    [ProducesResponseType(404, Type = typeof(string))]
    [ProducesResponseType(500, Type = typeof(string))]
    public async Task<IActionResult> CancelBundleAsync([FromBody] PrintBundleCancelCommand command)
    {
        return await ExecutePrintBundleActionAsync(
            async () => await printBundleActionHandler.CancelBundleAsync(command),
            result => result.Success ? Ok(result) : BadRequest(result.Message));
    }

    private async Task<IActionResult> ExecutePrintBundleActionAsync<TResponseType>(
        Func<Task<TResponseType>> executeAction,
        Func<TResponseType, IActionResult> handleAction)
    {
        try
        {
            var result = await executeAction();

            return handleAction(result);
        }
        catch (PrintDocumentNotFoundException exception)
        {
            return NotFound(exception.Message);
        }
        catch (PrintBundleNotFoundException exception)
        {
            return NotFound(exception.Message);
        }
        catch (InvalidOperationException exception)
        {
            return BadRequest(exception.Message);
        }
    }

    [HttpPost]
    [Route("release")]
    [ProducesResponseType(200, Type = typeof(PrintBundleReleaseResponse))]
    [ProducesResponseType(400, Type = typeof(string))]
    [ProducesResponseType(404, Type = typeof(string))]
    [ProducesResponseType(500, Type = typeof(string))]
    public async Task<IActionResult> ReleaseBundleAsync([FromBody] PrintBundleReleaseRequest request)
    {
        return await ExecutePrintBundleActionAsync(
            async () => await printBundleActionHandler.ReleaseBundleAsync(request),
            result => result.Success ? Ok(result) : BadRequest(result.Message));
    }

    [HttpPost]
    [Route("continue")]
    [ProducesResponseType(200, Type = typeof(PrintBundleManualContinueResponse))]
    [ProducesResponseType(404, Type = typeof(string))]
    [ProducesResponseType(500, Type = typeof(string))]
    public async Task<IActionResult> ContinueBundleAsync([FromBody] PrintBundleContinueCommand command)
    {
        return await ExecutePrintBundleActionAsync(
            async () => await printBundleActionHandler.ContinueBundleAsync(command),
            result => result.Success ? Ok(result) : BadRequest(result.Message));
    }

    [HttpGet]
    [Route("details")]
    [ProducesResponseType(200, Type = typeof(PrintBundleDetails))]
    [ProducesResponseType(404, Type = typeof(string))]
    public async Task<IActionResult> DetailsAsync(Guid printBundleId)
    {
        try
        {
            var printbundle = await printBundleService.GetBundleDetailsAsync(printBundleId);

            return Ok(printbundle);
        }
        catch (EntityNotFoundException)
        {
            return NotFound($"Could not find print bundle for id {printBundleId}");
        }
    }

    [HttpPost]
    [Route("printconfirm")]
    [ProducesResponseType(200)]
    [ProducesResponseType(404, Type = typeof(string))]
    public async Task<IActionResult> PrintConfirmBundleAsync([FromBody] PrintBundlePrintConfirmCommand command) =>
        await ExecutePrintBundleActionAsync(
            async () => await printBundleActionHandler.PrintConfirmBundleAsync(command),
            result => Ok());

    [HttpPost]
    [AllowAnonymous]
    [Route("completedconfirm")]
    [ProducesResponseType(200)]
    [ProducesResponseType(404, Type = typeof(string))]
    public async Task<IActionResult> CompletedConfirmBundleAsync([FromBody] PrintBundleCompletedConfirmCommand command) =>
        await ExecutePrintBundleActionAsync(
            async () => await printBundleActionHandler.CompletedConfirmBundleAsync(command),
            result => Ok());

    [HttpPost]
    [Route("scannedconfirm")]
    [ProducesResponseType(200)]
    [ProducesResponseType(404, Type = typeof(string))]
    public async Task<IActionResult> ScannedConfirmBundleAsync([FromBody] PrintBundleScannedConfirmCommand command) =>
        await ExecutePrintBundleActionAsync(
            async () => await printBundleActionHandler.ScannedConfirmBundleAsync(command),
            result => Ok());

    [HttpGet]
    [Route("activeStatusRaw")]
    [ProducesResponseType(200, Type = typeof(PrintBundleStatusResponse))]
    [ProducesResponseType(404, Type = typeof(string))]
    public async Task<IActionResult> RawStatusAsync(Guid printBundleId) =>
        await ExecutePrintBundleActionAsync(
            async () => await printBundleActionHandler.RawStatusAsync(printBundleId),
            result => Ok(result));

    [HttpPost]
    [Route("removeDocument")]
    [ProducesResponseType(200, Type = typeof(PrintBundleDocumentRemovedResponse))]
    [ProducesResponseType(404, Type = typeof(string))]
    public async Task<IActionResult> RemoveDocumentFromBundleAsync([FromBody] PrintBundleRemoveDocumentCommand command) =>
        await ExecutePrintBundleActionAsync(
            async () => await printBundleActionHandler.RemoveDocumentFromBundleAsync(command),
            result => Ok(result));

    [HttpPost]
    [Route("listByIds")]
    [ProducesResponseType(200, Type = typeof(PagedList<PrintBundleListItem>))]
    [ProducesResponseType(404, Type = typeof(string))]
    public async Task<IActionResult> PrintBundlesByIds([FromBody] IEnumerable<string> printBundleIds)
    {
        var result = await printBundleService.GetPrintBundlesByIdsAsync(printBundleIds);
        return Ok(result);
    }

    [HttpPost]
    [Route("zippedExcludedLocationBundles")]
    [ProducesResponseType(200, Type = typeof(FileContentResult))]
    [ProducesResponseType(404, Type = typeof(string))]
    public async Task<IActionResult> ZippedExcludedLocationBundlesAsync([FromBody] ZippedExcludedLocationPrintBundleCommand command)
    {
        var requestsWithBundles = await dGImportHandler.GetExcludedLocationBundlesForTimeRangeAsync(command.TimeRangeStart, command.TimeRangeEnd);

        var zip = ZipBuilderHelper.CreateZip(requestsWithBundles.Values.ToList());

        return File(zip, "application/zip", $"bundles-{DateTimeOffset.Now:dd/MM/yyyy HH:mm}.zip");
    }
}