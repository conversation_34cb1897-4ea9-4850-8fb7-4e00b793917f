﻿using Microsoft.Extensions.Options;
using Pondres.Omnia.Print.Business.Exceptions;
using Pondres.Omnia.Print.Business.Extensions;
using Pondres.Omnia.Print.Business.Model;
using Pondres.Omnia.Print.Business.Service;
using Pondres.Omnia.Print.Contracts.PrintBundle;
using Serilog;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Handler.PrintBundleBuffer;

public class PrintBundleBufferHandler : IPrintBundleBufferHandler
{
    private readonly ConcurrentDictionary<string, PrintBundleBuffer> buffers = new();
    private readonly IPrintBundleService printBundleService;
    private readonly IPrintBundleDocumentStorageService printBundleDocumentStorageService;
    private readonly PrintBundleBufferOptions options;

    public PrintBundleBufferHandler(
        IOptions<PrintBundleBufferOptions> options,
        IPrintBundleService printBundleService,
        IPrintBundleDocumentStorageService printBundleDocumentStorageService)
    {
        this.printBundleService = printBundleService;
        this.printBundleDocumentStorageService = printBundleDocumentStorageService;
        this.options = options.Value;
    }

    public async Task QueueDocumentGroupAsync(PrintDocumentGroup group)
    {
        const int maxAddAttempts = 2;
        var addAttempts = 0;

        do
        {
            await printBundleService.AssignDocumentGroupToBundlesAsync(group);

            var bundleCompletionTask = await AddDocumentGroupToBufferAsync(group);

            var bundleAddResult = await bundleCompletionTask;

            if (bundleAddResult.AddedToBundle)
                return;

            addAttempts++;
        } while (addAttempts < maxAddAttempts);

        throw new BufferOutOfSyncException($"Could not add document to a new bundle after {maxAddAttempts} attempts.");
    }

    internal async Task<Task<PrintBundleBufferFlushResult>> AddDocumentGroupToBufferAsync(PrintDocumentGroup group)
    {
        var bufferHash = $"{group.Primary.BundleHash}{group.Primary.BundleId}";

        var buffer = buffers.GetOrAdd(bufferHash, _ => new PrintBundleBuffer(group.Primary.BundleId));

        await buffer.Lock.WaitAsync();

        try
        {
            buffer.AddDocuments(group);
        }
        finally
        {
            buffer.Lock.Release();
        }

        // After flushing it will be refreshed
        var completionTask = buffer.CompletionSource.Task;

        if (buffer.Groups.Count == options.BufferLimit)
            await FlushAsync(bufferHash);

        return completionTask;
    }

    private async Task FlushAsync(string bufferHash)
    {
        Log.Information("Flushing buffer {BufferName}", bufferHash);

        var buffer = buffers[bufferHash];

        await buffer.Lock.WaitAsync();

        try
        {
            // Cleanup old buffers
            if (buffer.MarkedForDeletion)
            {
                buffers.Remove(bufferHash, out _);
                return;
            }

            // Mark for deletion so in the second cleanup cycle we actually remove it
            if (buffer.TryMarkForDeletion())
                return;

            var primaryDocuments = buffer.Groups.Select(x => x.Primary).ToList();

            var primaryQueuedDocuments = primaryDocuments.Select(x => new PrintBundleQueuedDocument(
                                x.CreationModel.OrderMetadata,
                                x.Metadata.ToPrintDocumentMetadata(x.CreationModel.DocumentId, x.CreationModel.PrimaryDocumentId),
                                x.CreationModel.FileInformation.PrintFilePath,
                                x.BundleHash)).ToList();

            var primaryAddResult = await printBundleDocumentStorageService.AddDocumentsToBundleAsync(buffer.BundleId, primaryQueuedDocuments);

            // maybe move this to the storage service and do them all at once
            if (primaryAddResult.Success)
            {
                var secondaryDocuments = buffer.Groups.SelectMany(x => x.Secondaries).GroupBy(x => x.BundleId).ToList();

                foreach (var secondaryDocumentGroup in secondaryDocuments)
                {
                    var secondaryQueuedDocuments = secondaryDocumentGroup.Select(x => new PrintBundleQueuedDocument(
                                x.CreationModel.OrderMetadata,
                                x.Metadata.ToPrintDocumentMetadata(x.CreationModel.DocumentId, x.CreationModel.PrimaryDocumentId),
                                x.CreationModel.FileInformation.PrintFilePath,
                                x.BundleHash)).ToList();

                    var secondaryAddResult = await printBundleDocumentStorageService.AddDocumentsToBundleAsync(
                        secondaryDocumentGroup.Key,
                        secondaryQueuedDocuments,
                        forceAddIfStarted: true);

                    if (!secondaryAddResult.Success)
                    {
                        // we in deep shit now
                        throw new InvalidOperationException($"Failed to add secondary documents to bundle {secondaryDocumentGroup.Key}");
                    }
                }
            }

            var flushResult = new PrintBundleBufferFlushResult(AddedToBundle: primaryAddResult.Success);

            buffer.CompletionSource.SetResult(flushResult);
        }
        catch (Exception exception)
        {
            buffer.CompletionSource.SetException(exception);
        }
        finally
        {
            buffer.Refresh();
            buffer.Lock.Release();
        }
    }

    public async Task FlushAllBuffersAsync()
    {
        foreach (var groupHash in buffers.Keys)
        {
            await FlushAsync(groupHash);
        }
    }
}
