﻿using Solude.StorageBase.Exceptions;
using Pondres.Omnia.Print.Business.Extensions;
using Pondres.Omnia.Print.Contracts.PrintDocument;
using Pondres.Omnia.Print.Storage.Entities.PrintDocument;
using Pondres.Omnia.Print.Storage.Repositories;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Handler;

public class PrintDocumentSaveHandler : IPrintDocumentSaveHandler
{
    private readonly IPrintDocumentRepository printDocumentRepository;
    private readonly IPrintBundleBasicRepository printBundleBasicRepository;

    public PrintDocumentSaveHandler(
        IPrintDocumentRepository printDocumentRepository,
        IPrintBundleBasicRepository printBundleBasicRepository)
    {
        this.printDocumentRepository = printDocumentRepository;
        this.printBundleBasicRepository = printBundleBasicRepository;
    }

    public async Task<PrintDocumentEntity> CreateOrGetPrintDocumentAsync(PrintDocumentSaveCommand command)
    {
        var bundle = await printBundleBasicRepository.GetSingleAsync(command.PrintBundleId.ToString());

        var entity = command.ToPrintDocumentEntity(bundle);

        try
        {
            return await printDocumentRepository.CreateAsync(entity);
        }
        catch (EntityAlreadyExistsException)
        {
            return await printDocumentRepository.GetSingleAsync(entity.Id);
        }
    }
}