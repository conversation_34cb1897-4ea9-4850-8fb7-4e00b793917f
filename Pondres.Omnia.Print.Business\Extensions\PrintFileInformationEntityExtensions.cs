﻿using Pondres.Omnia.Print.Contracts.PrintDocument;
using Pondres.Omnia.Print.Storage.Entities.PrintDocument;

namespace Pondres.Omnia.Print.Business.Extensions;

public static class PrintFileInformationEntityExtensions
{
    public static PrintDocumentFileInformation ToPrintDocumentFileInformation(this PrintFileInformationEntity entity) =>
        new PrintDocumentFileInformation
        {
            MetaDataFilePath = entity.MetadataFilePath,
            PrintFilePath = entity.DocumentFilePath,
            StorageAccountName = entity.StorageAccountName,
            StorageContainerName = entity.StorageContainerName
        };
}