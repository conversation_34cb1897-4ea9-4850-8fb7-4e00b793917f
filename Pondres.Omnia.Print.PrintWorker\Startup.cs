using FluentValidation;
using MassTransit;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Pondres.Omnia.Print.Business;
using Pondres.Omnia.Print.Business.Consumers;
using Pondres.Omnia.Print.Business.Handler.PrintBundleBuffer;
using Pondres.Omnia.Print.Business.Service;
using Pondres.Omnia.Print.Business.StateMachine.PrintBundle;
using Pondres.Omnia.Print.Business.StateMachine.PrintBundle.Model;
using Pondres.Omnia.Print.Common;
using Pondres.Omnia.Print.Integrations.Navision;
using Pondres.Omnia.Print.Integrations.Nies;
using Pondres.Omnia.Print.Integrations.OrderHub;
using Pondres.Omnia.Print.Integrations.OrderHub.Consumer;
using Pondres.Omnia.Print.PrintWorker.Services;
using Pondres.Omnia.Print.Storage;
using Pondres.Omnia.Print.Storage.DataStorage;
using Serilog;
using Solude.ApiBase.Extensions;
using System;
using System.Linq;

namespace Pondres.Omnia.Print.PrintWorker;

public class Startup(IHostEnvironment environment, IConfiguration configuration)
{
    private readonly PrintAppSettings appSettings = configuration.Get<PrintAppSettings>() ?? throw new ValidationException("PrintApp settings should not be null");

    public static void Configure(IApplicationBuilder app) =>
        app.UseDefaultServiceDependencies();

    public void ConfigureServices(IServiceCollection services)
    {
        services.AddDefaultServiceDependencies(appSettings, Program.Name);

        services.AddSingleton(Log.Logger);

        services
            .Configure<PrintBundleBufferOptions>(ConfigureBundleBuffer)
            .Configure<DataStorageOptions>(ConfigureDataStorage)
            .AddHealthChecks();

        services
            .AddNavisionIntegrationModule(appSettings)
            .AddNiesIntegrationModule(appSettings)
            .AddOrderHubIntegrationModule()
            .AddBusinessModule(appSettings)
            .AddStorageModule(appSettings);

        services
            .AddDefaultMassTransit(
                appSettings: appSettings,
                endpointPrefix: Program.Name,
                configureBus: ConfigureBusEndpoints);

        services
            .AddHostedService<BundleBufferPeriodicFlushService>();
    }

    private void ConfigureDataStorage(DataStorageOptions options)
    {
        options.IsDevelopment = environment.IsDevelopment();
        options.DevelopmentConnectionString = appSettings.AzuriteConnectionString;
    }

    private void ConfigureBusEndpoints(IBusRegistrationConfigurator configurator)
    {
        configurator.AddConsumer<PrintBundleEventConsumer, PrintBundleEventConsumerDefinition>();
        configurator.AddConsumer<PrintBundleEventHistoryConsumer, PrintBundleEventHistoryConsumerDefinition>();

        configurator.AddConsumer<OrderTaskPrintDocumentSubmissionConsumer, OrderTaskPrintDocumentSubmissionDefinition>();
        configurator.AddConsumer<OrderTaskMultiPrintDocumentSubmissionConsumer, OrderTaskMultiPrintDocumentSubmissionConsumerDefinition>();
        configurator.AddConsumer<OrderHubEventConsumer>();

        configurator.AddConsumer<PrintDocumentSaveCommandConsumer, PrintDocumentSaveCommandConsumerDefinition>();

        configurator.AddConsumer<EnrichPrintMetadataConsumer, EnrichPrintMetadataConsumerDefinition>();

        configurator.AddConsumer<WarehouseReprintRequestedConsumer, WarehouseReprintRequestedConsumerDefinition>();

        configurator.AddConsumer<PrintDocumentEventConsumer>();
        configurator.AddConsumer<PrintBundleReprintConsumer>();

        configurator.AddConsumer<PrintDocumentRequeueCommandConsumer>();

        configurator.AddSagaStateMachine<PrintBundleStateMachine, PrintBundleState, PrintBundleStateMachineDefinition>()
            .RedisRepository(appSettings.RedisConnectionString, x =>
            {
                x.ConcurrencyMode = ConcurrencyMode.Pessimistic;
                x.KeyPrefix = "printbundle";
            });
    }

    private static void ConfigureBundleBuffer(PrintBundleBufferOptions options)
    {
        options.BufferLimit = 100;
        options.BufferFlushInterval = TimeSpan.FromSeconds(5);
    }
}