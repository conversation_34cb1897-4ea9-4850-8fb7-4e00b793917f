﻿using Pondres.Omnia.Print.Business.Extensions;
using Pondres.Omnia.Print.Contracts.PrintBundle;
using Pondres.Omnia.Print.Storage.Repositories;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Handler;

public class PrintBundleEventHistoryUpdateHandler : IPrintBundleEventHistoryUpdateHandler
{
    private readonly IPrintBundleStatusHistoryRepository printBundleStatusRepository;

    public PrintBundleEventHistoryUpdateHandler(IPrintBundleStatusHistoryRepository printBundleStatusRepository)
    {
        this.printBundleStatusRepository = printBundleStatusRepository;
    }

    public async Task RegisterBundleCancelledEventAsync(PrintBundleCancelled message) =>
        await printBundleStatusRepository.CreateAsync(message.ToStatusEntity());

    public async Task RegisterBundleCompletionEventAsync(PrintBundleCompleted message) =>
        await printBundleStatusRepository.CreateAsync(message.ToStatusEntity());

    public async Task RegisterBundleSkippedEventAsync(PrintBundleSkipped message) =>
        await printBundleStatusRepository.CreateAsync(message.ToStatusEntity());

    public async Task RegisterStatusUpdateEventAsync(PrintBundleStatusUpdate message) =>
        await printBundleStatusRepository.CreateAsync(message.ToStatusEntity());
}