﻿using MassTransit;
using System;

namespace Pondres.Omnia.Print.Integrations.Nies.Consumer;

public class ExportToNiesConsumerDefinition : ConsumerDefinition<ExportToNiesConsumer>
{
    public ExportToNiesConsumerDefinition()
    {
        Endpoint(x =>
        {
            x.PrefetchCount = 300;
        });
    }

    protected override void ConfigureConsumer(IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<ExportToNiesConsumer> consumerConfigurator, IRegistrationContext context)
    {

        consumerConfigurator.Options<BatchOptions>(options => options
            .SetMessageLimit(100)
            .SetTimeLimit(TimeSpan.FromMinutes(1))
            .SetConcurrencyLimit(1));

        endpointConfigurator.UseScheduledRedelivery(configure =>
            configure.Intervals(
                TimeSpan.FromMinutes(1),
                TimeSpan.FromMinutes(10),
                TimeSpan.FromMinutes(15),
                TimeSpan.FromMinutes(30),
                TimeSpan.FromMinutes(60),
                TimeSpan.FromMinutes(90)));
    }
}