﻿using Newtonsoft.Json;
using Solude.StorageBase.Model;
using Pondres.Omnia.Print.Storage.Configuration;

namespace Pondres.Omnia.Print.Storage.Entities.Print;

public class PrintSheetMappingEntity : CosmosEntity
{
    [JsonProperty("id")]
    public override string Id => SheetArticleCode;

    [JsonProperty("printerType")] public string PrinterType { get; set; } = string.Empty;

    [JsonIgnore] public string SheetArticleCode { get; set; } = string.Empty;

    [JsonProperty("sheetFormat")] public string SheetFormat { get; set; } = string.Empty;

    [JsonProperty("type")]
    public string Type { get; } = CosmosConfiguration.PrintSheetMappingPartitionValue;

    public override string PartitionKeyValue => Type;

    [JsonProperty("version")]
    public override string Version { get; set; } = CosmosConfiguration.PrintSheetMappingVersion;
}