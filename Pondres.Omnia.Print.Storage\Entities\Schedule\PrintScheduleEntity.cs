﻿using Newtonsoft.Json;
using System.Collections.Generic;

namespace Pondres.Omnia.Print.Storage.Entities.Schedule;

public class PrintScheduleEntity
{
    [JsonProperty("dayOfWeek")]
    public List<string> DayOfWeek { get; set; } = [];

    [JsonProperty("description")]
    public string? Description { get; set; }

    [JsonProperty("releases")]
    public List<PrintScheduleTimeEntity> Releases { get; set; } = [];

    [JsonProperty("dates")]
    public List<string> Dates { get; set; } = [];
}