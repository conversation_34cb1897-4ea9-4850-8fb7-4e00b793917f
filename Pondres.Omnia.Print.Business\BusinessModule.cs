﻿using Microsoft.Extensions.DependencyInjection;
using Pondres.Omnia.Print.Business.CommandHandler;
using Pondres.Omnia.Print.Business.Handler;
using Pondres.Omnia.Print.Business.Handler.Navision;
using Pondres.Omnia.Print.Business.Handler.PrintBatch;
using Pondres.Omnia.Print.Business.Handler.PrintBundleBuffer;
using Pondres.Omnia.Print.Business.Helper;
using Pondres.Omnia.Print.Business.Service;
using Pondres.Omnia.Print.Business.Storage;
using Pondres.Omnia.Print.Common;
using Pondres.Omnia.Print.Contracts.Options;
using StackExchange.Redis;
using System;
using System.Linq;
using System.Runtime.CompilerServices;

[assembly: InternalsVisibleTo("Pondres.Omnia.Print.Business.UnitTests")]

namespace Pondres.Omnia.Print.Business;

public static class BusinessModule
{
    public static IServiceCollection AddBusinessModule(this IServiceCollection services, PrintAppSettings appSettings)
    {
        services.AddRedis(appSettings);
        services.AddOptions(appSettings);
        services.AddHandlers();
        services.AddServices();

        return services;
    }

    public static IServiceCollection AddOptions(this IServiceCollection services, PrintAppSettings appSettings)
    {
        services.Configure<PrintBatchOptions>(options =>
        {
            options.PrintFolderPath = appSettings.PrintFolderPath;
        });

        services.Configure<PrintDocumentLocalFileStoreServiceOptins>(options =>
        {
            options.ScalerMountFolderPrefixPath = appSettings.ScalerMountFolderPrefixPath;
        });

        services.Configure<PrintBundleConfiguration>(options =>
        {
            options.CustomersExcludedFromDirectComplete = appSettings.CustomersExcludedFromDirectComplete.Split(',', StringSplitOptions.TrimEntries).ToList();
        });

        return services;
    }

    public static IServiceCollection AddServices(this IServiceCollection services)
    {
        services.AddTransient<IPrintDocumentService, PrintDocumentService>();
        services.AddTransient<IPrintBundleService, PrintBundleService>();
        services.AddTransient<IPrintBundleStatisticsService, PrintBundleStatisticsService>();
        services.AddSingleton<IPrintScheduleProvider, PrintScheduleProvider>();
        services.AddTransient<IReleaseScheduleService, ReleaseScheduleService>();
        services.AddTransient<INavisionArticleService, NavisionArticleService>();
        services.AddTransient<IPrintBundleDocumentStorageService, PrintBundleDocumentStorageService>();
        services.AddTransient<IPrintBundleEventService, PrintBundleEventService>();
        services.AddTransient<PrintDocumentLocalFileStoreService>();

        services.AddHttpClient<IDownloadService, DownloadService>();

        return services;
    }

    public static IServiceCollection AddHandlers(this IServiceCollection services)
    {
        services.AddTransient<IPrintDocumentSaveHandler, PrintDocumentSaveHandler>();
        services.AddTransient<IPrintBundleImpositioningHandler, PrintBundleImpositioningHandler>();
        services.AddTransient<IPrintBundleEventHistoryUpdateHandler, PrintBundleEventHistoryUpdateHandler>();
        services.AddSingleton<IPrintBatchOrchestrationHandler, PrintBatchOrchestrationHandler>();
        services.AddSingleton<IPrintBatchActionHandler, PrintBatchActionHandler>();

        services.AddSingleton<IPrintBundleActionHandler, PrintBundleActionHandler>();
        services.AddSingleton<IPrintBatchNotificationHandler, PrintBatchNotificationHandler>();

        services.AddSingleton<IPrintBundleCreationHandler, PrintBundleCreationHandler>();

        services.AddSingleton<IPrintBundleBufferHandler, PrintBundleBufferHandler>();
        services.AddSingleton<IDGImportHandler, DGImportHandler>();

        services.AddTransient<QueueNewDocumentGroupCommandHandler>();
        services.AddTransient<RequeueDocumentsForOrderCommandHandler>();

        return services;
    }

    public static IServiceCollection AddRedis(this IServiceCollection services, PrintAppSettings appSettings)
    {
        services.AddSingleton<IConnectionMultiplexer>(ConnectionMultiplexer.Connect(appSettings.RedisConnectionString));
        services.AddTransient<IRedisCacheHelper, RedisCacheHelper>();

        return services;
    }
}
