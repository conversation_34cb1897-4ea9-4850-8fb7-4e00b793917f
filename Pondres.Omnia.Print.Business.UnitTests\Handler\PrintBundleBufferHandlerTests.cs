﻿using FluentAssertions;
using Microsoft.Extensions.Options;
using Moq;
using Moq.AutoMock;
using Pondres.Omnia.Print.Business.Handler.PrintBundleBuffer;
using Pondres.Omnia.Print.Business.Model;
using Pondres.Omnia.Print.Business.Service;
using Pondres.Omnia.Print.Contracts.Api.Enums;
using Pondres.Omnia.Print.Contracts.Common;
using Pondres.Omnia.Print.Contracts.PrintBundle;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Xunit;

namespace Pondres.Omnia.Print.Business.UnitTests.Handler;

public class PrintBundleBufferHandlerTests
{
    private readonly AutoMocker mocker;
    private readonly Mock<IPrintBundleDocumentStorageService> documentStorageMock;
    private readonly Mock<IPrintBundleService> printServiceMock;

    public PrintBundleBufferHandlerTests()
    {
        mocker = new AutoMocker(MockBehavior.Strict);
        documentStorageMock = mocker.GetMock<IPrintBundleDocumentStorageService>();
        printServiceMock = mocker.GetMock<IPrintBundleService>();
    }

    private PrintBundleBufferHandler CreateHandler(int bufferLimit = 100, int bufferFlushIntervalSeconds = 10)
    {
        var options = new OptionsWrapper<PrintBundleBufferOptions>(new PrintBundleBufferOptions
        {
            BufferLimit = bufferLimit,
            BufferFlushInterval = System.TimeSpan.FromSeconds(bufferFlushIntervalSeconds)
        });

        return new PrintBundleBufferHandler(
            options,
            printServiceMock.Object,
            documentStorageMock.Object);
    }

    private static PrintDocumentGroup GenerateGroup(Guid primaryBundleId)
    {
        var primaryPair = new PrintDocumentCreationPair(
                creationModel: new Common.Model.PrintDocumentCreationModel(
                    documentId: Guid.NewGuid().ToString(),
                    primaryDocumentId: Guid.NewGuid().ToString(),
                    fileInformation: new Contracts.PrintDocument.PrintDocumentFileInformation
                    {
                        MetaDataFilePath = "MetaDataFilePath",
                        PrintFilePath = "PrintFilePath",
                        StorageAccountName = "StorageAccountName",
                        StorageContainerName = "StorageContainerName"
                    },
                    orderMetadata: new OrderMetadata
                    {
                        OrderId = Guid.NewGuid(),
                    }), metadata: CreatePrintDocumentMetadata())
        {
            BundleId = primaryBundleId
        };

        return new(
            Primary: primaryPair,
            Secondaries: []);
    }

    public static PrintDocumentCreationMetadata CreatePrintDocumentMetadata(Guid? sheetArticleCode = null, DateTimeOffset? mailDate = null, string? releaseScheduleId = null)
    {
        return new PrintDocumentCreationMetadata
        {
            CustomerDocumentReference = Guid.NewGuid().ToString(),
            Carrier = "carrier",
            DocumentFormat = "DocumentFormat",
            Laminate = true,
            PrintMode = "Duplex",
            MailDate = mailDate ?? DateTime.Now,
            PostalDestination = "PostalDestination",
            PrinterType = PrinterType.FC,
            SheetArticleCode = $"SheetArticleCode{sheetArticleCode ?? Guid.NewGuid():N}",
            PageCount = new Random().Next(2, 8),
            SheetPageCount = 2,
            SheetFormat = "SheetFormat",
            Quantity = 1,
            ReleaseSchedule = releaseScheduleId,
            EndLocation = "TestEndLocation",
            DGCode = "Test",
            IsPrimary = true
        };
    }

    [Fact]
    public async Task AddDocuments_FlushAll_FlushesBuffers()
    {
        // Arrange
        var bufferLimit = 100;
        var bufferFlushIntervalSeconds = 5;

        var handler = CreateHandler(
            bufferLimit: bufferLimit,
            bufferFlushIntervalSeconds: bufferFlushIntervalSeconds);

        documentStorageMock
            .Setup(storage => storage.AddDocumentsToBundleAsync(
                It.IsAny<Guid>(),
                It.IsAny<List<PrintBundleQueuedDocument>>(),
                It.IsAny<bool>()))
            .ReturnsAsync(new AddDocumentsToBundleResult(BundleId: Guid.NewGuid(), Success: true, AddedCount: bufferLimit));

        var bundleId = Guid.NewGuid();
        var group = GenerateGroup(bundleId);

        var tasks = new List<Task<PrintBundleBufferFlushResult>>();
        for (var i = 0; i < bufferLimit - 2; i++)
        {
            tasks.Add(await handler.AddDocumentGroupToBufferAsync(group));
        }

        // Act
        await handler.FlushAllBuffersAsync();

        await Task.WhenAll(tasks);

        // Assert
        documentStorageMock.Verify(storage => storage.AddDocumentsToBundleAsync(It.IsAny<Guid>(), It.IsAny<List<PrintBundleQueuedDocument>>(), It.IsAny<bool>()), times: Times.Once);

        tasks.Should().AllSatisfy(x => x.IsCompletedSuccessfully.Should().BeTrue());
        tasks.Should().AllSatisfy(x => x.Result.AddedToBundle.Should().BeTrue());
    }

    [Fact]
    public async Task AddDocument_BundleStarted_ReturnsFalse()
    {
        // Arrange
        var bufferLimit = 100;
        var handler = CreateHandler(bufferLimit: bufferLimit);

        var bundleId = Guid.NewGuid();
        documentStorageMock
            .Setup(storage => storage.AddDocumentsToBundleAsync(bundleId, It.IsAny<List<PrintBundleQueuedDocument>>(), It.IsAny<bool>()))
            .ReturnsAsync(new AddDocumentsToBundleResult(BundleId: Guid.NewGuid(), Success: false, AddedCount: bufferLimit));

        var group = GenerateGroup(bundleId);

        var tasks = new List<Task>();
        for (var i = 0; i < bufferLimit; i++)
        {
            tasks.Add(await handler.AddDocumentGroupToBufferAsync(group));
        }

        // Act
        await Task.WhenAll(tasks);

        // Assert
        tasks.Should().HaveCount(100);
        documentStorageMock.Verify(storage => storage.AddDocumentsToBundleAsync(bundleId, It.IsAny<List<PrintBundleQueuedDocument>>(), It.IsAny<bool>()), times: Times.Once);
        documentStorageMock.Verify(storage => storage.AddDocumentsToBundleAsync(bundleId, It.IsAny<List<PrintBundleQueuedDocument>>(), It.IsAny<bool>()), times: Times.Once);
    }
    [Fact]
    public async Task AddDocument_FlushesBufferWhenSizeLimitReached()
    {
        // Arrange
        var bufferLimit = 100;
        var handler = CreateHandler(bufferLimit: 100);

        documentStorageMock
            .Setup(storage => storage.AddDocumentsToBundleAsync(It.IsAny<Guid>(), It.IsAny<List<PrintBundleQueuedDocument>>(), It.IsAny<bool>()))
            .ReturnsAsync(new AddDocumentsToBundleResult(BundleId: Guid.NewGuid(), Success: true, AddedCount: bufferLimit));

        var bundleId = Guid.NewGuid();
        var group = GenerateGroup(bundleId);

        var tasks = new List<Task>();
        for (var i = 0; i < bufferLimit; i++)
        {
            tasks.Add(await handler.AddDocumentGroupToBufferAsync(group));
        }

        // Act
        await Task.WhenAll(tasks);

        // Assert
        tasks.Should().HaveCount(100);
        documentStorageMock.Verify(storage => storage.AddDocumentsToBundleAsync(It.IsAny<Guid>(), It.IsAny<List<PrintBundleQueuedDocument>>(), It.IsAny<bool>()), times: Times.Once);
    }

    [Fact]
    public async Task AddDocument_FlushesBufferConsequentlyEverytimeSizeIsReached()
    {
        // Arrange
        var bufferLimit = 100;
        var handler = CreateHandler(bufferLimit: 100);

        documentStorageMock
            .Setup(storage => storage.AddDocumentsToBundleAsync(It.IsAny<Guid>(), It.IsAny<List<PrintBundleQueuedDocument>>(), It.IsAny<bool>()))
            .ReturnsAsync(new AddDocumentsToBundleResult(BundleId: Guid.NewGuid(), Success: true, AddedCount: bufferLimit));

        var bundleId = Guid.NewGuid();
        var group = GenerateGroup(bundleId);

        var tasks = new List<Task<PrintBundleBufferFlushResult>>();
        for (var i = 0; i < bufferLimit * 3; i++)
        {
            tasks.Add(await handler.AddDocumentGroupToBufferAsync(group));
        }

        // Act
        await Task.WhenAll(tasks);

        // Assert
        tasks.Should().HaveCount(300);
        tasks.Should().AllSatisfy(x => x.Result.AddedToBundle.Should().BeTrue());

        documentStorageMock.Verify(storage => storage.AddDocumentsToBundleAsync(It.IsAny<Guid>(), It.IsAny<List<PrintBundleQueuedDocument>>(), It.IsAny<bool>()), times: Times.Exactly(3));
    }
}
