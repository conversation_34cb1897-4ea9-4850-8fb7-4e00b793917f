﻿using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.Caching.Memory;
using Solude.StorageBase;
using Pondres.Omnia.Print.Storage.Configuration;
using Pondres.Omnia.Print.Storage.Entities.Navision;
using System;

namespace Pondres.Omnia.Print.Storage.Repositories;

public class NavisionArticleRepository : SinglePartitionBaseRepository<NavisionArticleEntity>, INavisionArticleRepository
{
    public NavisionArticleRepository(CosmosClient cosmosClient, IMemoryCache memoryCache)
        : base(cosmosClient, CosmosConfiguration.PrintDatabaseName, CosmosConfiguration.NavisionArticleContainerName, memoryCache)
    { }

    protected override string PartitionKeyValue => CosmosConfiguration.NavisionArticlePartitionValue;

    protected override bool CanWrite => false;

    protected override MemoryCacheEntryOptions CreateCacheOptions() => new()
    {
        SlidingExpiration = TimeSpan.FromMinutes(1)
    };

    protected override string GetCacheKey(string id, string partitionKeyValue) =>
        $"NavisionArticleEntity_{id}_{partitionKeyValue}";
}
