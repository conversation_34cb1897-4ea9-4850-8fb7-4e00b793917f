﻿using Newtonsoft.Json;
using Solude.StorageBase.Model;
using Pondres.Omnia.Print.Storage.Configuration;
using System;

namespace Pondres.Omnia.Print.Storage.Entities.PrintBundleStatus;

public class PrintBundleStatusHistoryEntity : CosmosEntity
{
    [JsonProperty("bundleId")]
    public string BundleId { get; set; } = string.Empty;

    [JsonProperty("details")] public PrintBundleStatusDetailsEntity Details { get; set; } = new();

    [JsonProperty("createdOn")]
    public DateTimeOffset CreatedOn { get; set; }

    public override string PartitionKeyValue => BundleId;

    public override string Version { get; set; } = CosmosConfiguration.PrintBundleStatusHistoryVersion;

    public static PrintBundleStatusHistoryEntity Create(DateTimeOffset timestamp, string bundleId, PrintBundleStatusDetailsEntity details) =>
        new()
        {
            Id = timestamp.UtcTicks.ToString(),
            BundleId = bundleId,
            CreatedOn = timestamp,
            Details = details
        };
}
