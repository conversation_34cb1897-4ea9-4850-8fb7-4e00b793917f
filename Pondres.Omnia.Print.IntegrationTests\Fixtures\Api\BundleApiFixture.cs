﻿using FluentAssertions;
using MassTransit;
using Pondres.Omnia.OrderHub.IntegrationTests.Helper;
using Pondres.Omnia.Print.IntegrationTests.PrintClient;

namespace Pondres.Omnia.Print.IntegrationTests.Fixtures.Api;

public class BundleApiFixture
{
    private readonly PrintManagerClient client;
    private readonly ITestDependenciesProvider testDependenciesProvider;

    public BundleApiFixture(PrintManagerClient client, ITestDependenciesProvider testDependenciesProvider)
    {
        this.client = client;
        this.testDependenciesProvider = testDependenciesProvider;
    }

    public async Task<Contracts.PrintBundle.PrintBundlePrintConfirmationResponse> ConfirmBundleByPrintAsync(Guid id)
    {
        var bus = testDependenciesProvider.ResolveManagerService<IBus>();
        var requestClient = bus.CreateRequestClient<Contracts.PrintBundle.PrintBundlePrintConfirmationRequest>();

        var response = await requestClient.GetResponse<Contracts.PrintBundle.PrintBundlePrintConfirmationResponse>(new Contracts.PrintBundle.PrintBundlePrintConfirmationRequest
        {
            PrintBundleId = id,
            Username = "Integration test"
        });

        return response.Message;
    }

    public async Task<PrintBundleRawDetails> GetRawAsync(Guid id) => await client.BundleRawAsync(id);

    public async Task<PrintBundleListItemPagedList> GetBatchesAsync(PrintBundleBatchFilter batchFilter)
    {
        var response = await client.BundleBundleBatchListAsync(batchFilter);
        return response;
    }

    public async Task<PrintBundleListItemPagedList> GetListByIds(Guid[] ids) => await client.BundleListByIdsAsync(ids.Select(x => x.ToString()));

    public async Task<PrintBundleDetails?> GetDetailsAsync(Guid id) => await client.BundleDetailsAsync(id);

    public async Task<PrintBundleReleaseResponse> ReleaseBundleAsync(Guid id, PrintBundleMode? printBundleMode = null, DateTimeOffset? releaseTime = null)
    {
        var request = new PrintBundleReleaseRequest
        {
            PrintBundleId = id,
            BundleOptions = new PrintBundleOptions { BundleMode = printBundleMode ?? PrintBundleMode.EmptyGroup },
            Username = $"IntegrationTest",
            ReleaseDateTime = releaseTime ?? DateTimeOffset.Now
        };

        var response = await client.BundleReleaseAsync(request);

        return response;
    }

    public async Task<PrintBundleDocumentRemovedResponse> RemoveDocumentFromBundleAsync(Guid bundleId, string documentId)
    {
        var removeCommand = new PrintBundleRemoveDocumentCommand
        {
            PrintBundleId = bundleId,
            DocumentId = documentId
        };

        var response = await client.BundleRemoveDocumentAsync(removeCommand);

        return response;
    }

    public async Task<PrintBundleCancelResponse> CancelBundleAsync(PrintBundleCancelCommand cancelCommand)
    {
        var response = await client.BundleCancelAsync(cancelCommand);

        return response;
    }

    public async Task<PrintBundleDetails?> WaitForBundleToBeInState(Guid id, MapPrintBundleState state)
    {
        return await TestHelper.WaitForAsync(async () =>
        {
            var bundle = await GetDetailsAsync(id);

            bundle.Should().NotBeNull();
            bundle!.Status.Status.Should().Be(state);

            return bundle;
        });
    }
}