﻿using MassTransit;
using Pondres.Omnia.Print.Business.Handler;
using Pondres.Omnia.Print.Contracts.PrintBundle;
using Serilog;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Consumers;

public class PrintBundleEventHistoryConsumer :
    IConsumer<PrintBundleCompleted>,
    IConsumer<PrintBundleCancelled>,
    IConsumer<PrintBundleSkipped>
{
    private readonly IPrintBundleEventHistoryUpdateHandler handler;
    private readonly ILogger logger;

    public PrintBundleEventHistoryConsumer(IPrintBundleEventHistoryUpdateHandler handler, ILogger logger)
    {
        this.handler = handler;
        this.logger = logger;
    }

    public async Task Consume(ConsumeContext<PrintBundleCompleted> context)
    {
        logger.Information("PrintBundleEventHistoryConsumer received PrintBundleCompleted for PrintBundle {PrintBundleId}", context.Message.PrintBundleId);
        await handler.RegisterBundleCompletionEventAsync(context.Message);
    }

    public async Task Consume(ConsumeContext<PrintBundleCancelled> context)
    {
        logger.Information("PrintBundleEventHistoryConsumer received PrintBundleCancelled for PrintBundle {PrintBundleId}", context.Message.PrintBundleId);
        await handler.RegisterBundleCancelledEventAsync(context.Message);
    }

    public async Task Consume(ConsumeContext<PrintBundleSkipped> context)
    {
        logger.Information("PrintBundleEventHistoryConsumer received PrintBundleSkipped for PrintBundle {PrintBundleId}", context.Message.PrintBundleId);
        await handler.RegisterBundleSkippedEventAsync(context.Message);
    }
}
