﻿using Solude.StorageBase;
using Pondres.Omnia.Print.Contracts.Api.Bundle;
using Pondres.Omnia.Print.Storage.Entities.PrintBundle;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Storage.Repositories;

public interface IPrintBundleBasicRepository : ISinglePartitionBaseRepository<PrintBundleBasicEntity>
{
    Task<List<PrintBundleBasicEntity>> GetAllSecondaryPrintBundlesAsync(string primaryBundleId);
    Task<List<PrintBundleBasicEntity>> GetAllForDateRangeAsync(PrintBundleDateRangeFilter filter);
    Task<List<PrintBundleBasicEntity>> GetFutureBundlesForHashAsync(string bundleHash, DateTimeOffset? plannedOn, string? primaryBundleId);
}