﻿using MassTransit;
using Pondres.Omnia.Print.Business.Extensions;
using Pondres.Omnia.Print.Business.Handler.PrintBundleBuffer;
using Pondres.Omnia.Print.Business.Model;
using Pondres.Omnia.Print.Business.Service;
using Pondres.Omnia.Print.Contracts.PrintDocument;
using Pondres.Omnia.Print.Storage.Repositories;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.CommandHandler;

public record RequeueDocumentsForOrderCommand(ConsumeContext Context, Guid OrderId, string Customer);

public class RequeueDocumentsForOrderCommandHandler(
        PrintDocumentLocalFileStoreService printDocumentLocalFileStoreService,
    IPrintDocumentRepository printDocumentRepository,
    IPrintBundleBasicRepository printBundleBasicRepository,
    IPrintBundleBufferHandler printBundleBufferHandler)
{
    public async Task HandleAsync(RequeueDocumentsForOrderCommand command)
    {
        var groups = await CreatePrintGroupForCommandAsync(command);

        foreach (var group in groups)
        {
            var allCreationPairs = group.All;

            UpdateDocumentMetadataHashes(group, allCreationPairs);

            await printBundleBufferHandler.QueueDocumentGroupAsync(group);

            await Parallel.ForEachAsync(
                source: allCreationPairs,
                body: async (pair, cancellationToken) =>
                {
                    await printDocumentLocalFileStoreService.StoreDocumentFileToLocalFileStoreAsync(
                        bundleId: pair.BundleId,
                        storageAccountName: pair.CreationModel.FileInformation.StorageAccountName,
                        storageContainerName: pair.CreationModel.FileInformation.StorageContainerName,
                        filePath: pair.CreationModel.FileInformation.PrintFilePath);
                });

            foreach (var pair in allCreationPairs)
            {
                await command.Context.Publish(new PrintDocumentRequeued
                {
                    DocumentMetadata = pair.Metadata.ToPrintDocumentMetadata(pair.CreationModel.DocumentId, pair.CreationModel.PrimaryDocumentId),
                    OrderMetadata = pair.CreationModel.OrderMetadata,
                    MailDate = pair.Metadata.MailDate,
                    PrintBundleId = pair.BundleId,
                    Timestamp = DateTimeOffset.Now
                });
            }

            Log.Debug("Print document requeued for command {@Command}", command);
        }
    }

    private static void UpdateDocumentMetadataHashes(PrintDocumentGroup group, List<PrintDocumentCreationPair> allCreationPairs)
    {
        var allMetadata = allCreationPairs.Select(x => x.Metadata).ToArray();

        group.Primary.BundleHash = group.Primary.Metadata.ToHash(allMetadata);
        foreach (var secondaryPair in group.Secondaries)
        {
            secondaryPair.BundleHash = secondaryPair.Metadata.ToHash(allMetadata);
        }
    }

    private async Task<List<PrintDocumentGroup>> CreatePrintGroupForCommandAsync(RequeueDocumentsForOrderCommand command)
    {
        var result = new List<PrintDocumentGroup>();

        var allDocuments = await printDocumentRepository.GetAllForOrderIdAsync(command.Customer, command.OrderId);

        var primaryDocuments = allDocuments.Where(document => document.PrimaryDocumentId == document.Id);

        foreach (var primaryDocument in primaryDocuments)
        {
            var secondaryDocuments = allDocuments
                .Where(document =>
                    document.PrimaryDocumentId != document.Id &&
                    document.PrimaryDocumentId == primaryDocument.Id)
                .ToList();

            if (primaryDocument.LastBundle == null)
            {
                throw new InvalidOperationException("LastBundle should not be null for primary document");
            }

            if (secondaryDocuments.Exists(d => d.LastBundle == null))
            {
                throw new InvalidOperationException("LastBundle should not be null for any secondary document");
            }

            var originalPrimaryBundle = await printBundleBasicRepository.GetSingleAsync(primaryDocument.LastBundle.BundleId.ToString());
            var originalSecondaryBundles = await printBundleBasicRepository.GetAllSecondaryPrintBundlesAsync(primaryDocument.LastBundle.BundleId.ToString());

            var primaryCreationMetadata = originalPrimaryBundle.Resource.Metadata.ToPrintDocumentCreationMetadata(
                quantity: primaryDocument.Quantity,
                customerDocumentReference: primaryDocument.CustomerDocumentReference);

            if (!originalSecondaryBundles.TrueForAll(x => secondaryDocuments.Exists(doc => doc.LastBundle!.BundleId.ToString() == x.Id)))
                throw new InvalidOperationException("Not all bundles selected actually have a document, this should not really happen actually");

            var primaryPair = new PrintDocumentCreationPair(primaryDocument.ToPrintDocumentCreationModel(), primaryCreationMetadata);

            var secondaryPairs = secondaryDocuments
              .Select(x => new PrintDocumentCreationPair(
                  x.ToPrintDocumentCreationModel(),
                  originalSecondaryBundles.Single(bundle => bundle.Id == x.LastBundle!.BundleId.ToString()).Metadata.ToPrintDocumentCreationMetadata(
                      quantity: x.Quantity,
                      customerDocumentReference: x.CustomerDocumentReference)))
              .ToList();

            result.Add(new PrintDocumentGroup(primaryPair, secondaryPairs));
        }

        return result;
    }
}
