﻿using Pondres.Omnia.Print.Contracts.PrintBundle;

namespace Pondres.Omnia.Print.Business.Extensions;

public static class PrintBundleRemoveDocumentRequestExtensions
{
    public static PrintBundleDocumentNotFoundResponse ToPrintBundleDocumentNotFoundResponse(this PrintBundleRemoveDocumentRequest request)
        => new(PrintBundleId: request.PrintBundleId, DocumentId: request.DocumentId);

    public static PrintBundleDocumentRemovedResponse ToPrintBundleDocumentRemovedResponse(this PrintBundleRemoveDocumentRequest request)
        => new(PrintBundleId: request.PrintBundleId, RemovedDocumentId: request.DocumentId);

    public static PrintBundleDocumentRemoved ToPrintBundleDocumentRemoved(this PrintBundleRemoveDocumentRequest request)
        => new(PrintBundleId: request.PrintBundleId, DocumentId: request.DocumentId);
}
