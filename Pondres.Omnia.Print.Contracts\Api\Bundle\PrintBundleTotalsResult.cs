﻿using System.Collections.Generic;

namespace Pondres.Omnia.Print.Contracts.Api.Bundle
{
    public class PrintBundleTotalsResult
    {
        public List<PrintBundleTotalsPerDayItem> Days { get; set; } = new();

        public Dictionary<int, PrintBundleTotalsItem> TotalsPerWeek { get; set; } = new();

        public Dictionary<int, PrintBundleTotalsItem> TotalsPerHour { get; set; } = new();
    }
}
