﻿using MassTransit;
using Pondres.Omnia.Print.Contracts.PrintBundle;

namespace Pondres.Omnia.Print.IntegrationTests.Fixtures.Integration;
public class ScalerIntegrationFixture
{
    private readonly ITestDependenciesProvider testDependenciesProvider;

    public ScalerIntegrationFixture(ITestDependenciesProvider testDependenciesProvider)
    {
        this.testDependenciesProvider = testDependenciesProvider;
    }

    public async Task PublishImpositioningFailedReplyAsync(Guid bundleId, string customer)
    {
        var impositioningReply = new PrintBundleImpositioningFailed
        {
            PrintBundleId = bundleId,
            Customer = customer
        };
        var bus = testDependenciesProvider.ResolveWorkerService<IBus>();

        await bus.PublishAndWaitForConsumeAsync(impositioningReply, x => x.PrintBundleId == impositioningReply.PrintBundleId);
    }

    public async Task PublishImpositioningCompletedReplyAsync(Guid bundleId, string customer)
    {
        var impositioningReply = new PrintBundleImpositioningCompleted
        {
            PrintBundleId = bundleId,
            Customer = customer,
            ResultFileNames = ["test.pdf"]
        };
        var bus = testDependenciesProvider.ResolveWorkerService<IBus>();

        await bus.PublishAndWaitForConsumeAsync(impositioningReply, x => x.PrintBundleId == impositioningReply.PrintBundleId);
    }
}
