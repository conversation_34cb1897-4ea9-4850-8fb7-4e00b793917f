﻿using Pondres.Omnia.Print.Contracts.PrintBundle;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Handler;

public interface IPrintBundleEventHistoryUpdateHandler
{
    Task RegisterBundleCancelledEventAsync(PrintBundleCancelled message);
    Task RegisterBundleCompletionEventAsync(PrintBundleCompleted message);
    Task RegisterBundleSkippedEventAsync(PrintBundleSkipped message);
    Task RegisterStatusUpdateEventAsync(PrintBundleStatusUpdate message);
}