﻿using MassTransit;
using Pondres.Omnia.Print.Business.Helper;
using Pondres.Omnia.Print.Business.Service;
using Pondres.Omnia.Print.Business.StateMachine.PrintBundle.Model;
using Pondres.Omnia.Print.Contracts.PrintBundle;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Activities.Bundle;
public class PrintBundleReleaseActivity(IPrintBundleService printBundleService, IPrintDocumentService printDocumentService)
    : BaseActivity<PrintBundleState, PrintBundleReleaseRequest>
{
    public override string ActivityName => "print-bundle-release-activity";

    protected override async Task ExecuteAsync(BehaviorContext<PrintBundleState, PrintBundleReleaseRequest> context)
    {
        var request = context.Message;
        var state = context.Saga;

        string? newBatchName = null;

        if (request.ReleaseDateTime.HasValue || state.PlannedOn == null)
        {
            newBatchName = PrintBatchHelper.CreateBatchName(request.ReleaseDateTime);
        }

        UpdateWithReleaseRequest(context.Saga, context.Message, newBatchName);

        await printBundleService.UpdateWithReleaseRequestAsync(context.Message, newBatchName);

        if (newBatchName != null)
        {
            await printDocumentService.UpdateBatchNameForDocumentsInBundleAsync(request.PrintBundleId, newBatchName);
        }
    }

    private static void UpdateWithReleaseRequest(PrintBundleState state, PrintBundleReleaseRequest request, string? newBatchName)
    {
        if (request.BundleOptions != null)
            state.BundleOptions = request.BundleOptions;

        state.ReleasedOn = request.ReleaseDateTime;

        if (newBatchName != null)
        {
            state.BatchName = newBatchName;
        }

        if (request.TaskNumber.HasValue)
            state.TaskNumber = request.TaskNumber.Value;

        // Extra check because scaler doesn't work with empty. It expects null.
        if (!string.IsNullOrEmpty(request.GordNumber))
            state.GordNumber = request.GordNumber;
    }
}