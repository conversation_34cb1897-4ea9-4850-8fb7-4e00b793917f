# Docker
# Build a Docker image 
# https://docs.microsoft.com/azure/devops/pipelines/languages/docker

trigger:
- master

resources:
- repo: self

variables:
- name: workerContainerRepository
  value: 'omnia-print-printworker'
- name: managerContainerRepository
  value: 'omnia-print-printmanager'
- name: managerDockerfile
  value: $(Build.SourcesDirectory)/Pondres.Omnia.Print.PrintManager/Dockerfile
- name: workerDockerfile
  value: $(Build.SourcesDirectory)/Pondres.Omnia.Print.PrintWorker/Dockerfile
- name: tag
  value: '$(Build.BuildNumber)'
- name: nugetVersion
  value: '1.$(Build.BuildNumber)'
- group: DockerBuild - Nuget PAT TOKEN
- group: Build - Common
- name: sonarProjectKey
  value: 'pondresnl_Pondres.Omnia.Print'
- name: sonarProjectName
  value: 'Pondres.Omnia.Print'
- name: contractPackageProject
  value: $(Build.SourcesDirectory)/Pondres.Omnia.Print.Contracts/Pondres.Omnia.Print.Contracts.csproj

pool:
  vmImage: 'ubuntu-latest'

stages:
- stage: BuildAndPublish
  displayName: Build, Test and Publish
  jobs:  
  - job: BuildAndTest
    displayName: Build and Test .NET projects
    steps:
    - task: UseDotNet@2
      displayName: 'Install .NET 9 SDK'
      inputs:
        packageType: 'sdk'
        version: '9.x'
        installationPath: $(Agent.ToolsDirectory)/dotnet
    - task: SonarCloudPrepare@1
      inputs:
        SonarCloud: 'SonarCloud'
        organization: 'pondresnl'
        scannerMode: 'MSBuild'
        projectKey: '$(sonarProjectKey)'
        projectName: '$(sonarProjectName)'
        extraProperties: |
          # Additional properties that will be passed to the scanner, 
          # Put one key=value per line, example:
          # sonar.exclusions=**/*.bin
          sonar.cs.opencover.reportsPaths=$(Agent.TempDirectory)/**/coverage.opencover.xml
    - task: DotNetCoreCLI@2
      displayName: Restore
      inputs:
        command: 'restore'
        projects: '**/*.sln'
        feedsToUse: 'select'
        vstsFeed: '534b90ef-0e1b-475a-8b29-f869c9ffb2fd/202483c3-5d54-4aec-a875-a4bd78523c18'
        verbosityRestore: minimal
    - task: DotNetCoreCLI@2
      displayName: Build
      inputs:
        command: 'build'
        projects: '**/*.sln'
        arguments: '--no-restore'
    - task: DotNetCoreCLI@2
      displayName: Test
      inputs:
        command: 'test'
        projects: '**/*.sln'
        arguments: '--no-build -- xunit.parallelizeAssembly=true'
      env:
        CosmosDbEndpoint: "$(CosmosDbEndpoint)"
    - task: SonarCloudAnalyze@1
      inputs:
        jdkversion: 'JAVA_HOME_17_X64'
    - task: SonarCloudPublish@1
      inputs:
        pollingTimeoutSec: '300'
  - job: BuildandPublishManagerContainer
    displayName: Build and Publish Manager Container
    steps:
    - task: UseDotNet@2
      displayName: 'Install .NET 9 SDK'
      inputs:
        packageType: 'sdk'
        version: '9.x'
        installationPath: $(Agent.ToolsDirectory)/dotnet
    - task: Docker@2
      inputs:
        containerRegistry: 'TestContainerRegistry'
        repository: '$(managerContainerRepository)'
        command: 'build'
        Dockerfile: '$(managerDockerfile)'
        buildContext: '$(Build.SourcesDirectory)'
        tags: |
          latest
          $(tag)
        arguments: '--build-arg FEED_ACCESSTOKEN=$(FEED_ACCESSTOKEN)'
    - task: Docker@2
      inputs:
        containerRegistry: 'TestContainerRegistry'
        repository: '$(managerContainerRepository)'
        command: 'push'
        tags: |
          latest
          $(tag)
    - task: Docker@2
      inputs:
        containerRegistry: 'ProdContainerRegistry'
        repository: '$(managerContainerRepository)'
        command: 'build'
        Dockerfile: '$(managerDockerfile)'
        buildContext: '$(Build.SourcesDirectory)'
        tags: |
          latest
          $(tag)
        arguments: '--build-arg FEED_ACCESSTOKEN=$(FEED_ACCESSTOKEN)'
    - task: Docker@2
      inputs:
        containerRegistry: 'ProdContainerRegistry'
        repository: '$(managerContainerRepository)'
        command: 'push'
        tags: |
          latest
          $(tag)
          
  - job: BuildandPublishWorkerContainer
    displayName: Build and Publish Worker Container
    steps:
    - task: UseDotNet@2
      displayName: 'Install .NET 9 SDK'
      inputs:
        packageType: 'sdk'
        version: '9.x'
        installationPath: $(Agent.ToolsDirectory)/dotnet
    - task: Docker@2
      inputs:
        containerRegistry: 'TestContainerRegistry'
        repository: '$(workerContainerRepository)'
        command: 'build'
        Dockerfile: '$(workerDockerfile)'
        buildContext: '$(Build.SourcesDirectory)'
        tags: |
          latest
          $(tag)
        arguments: '--build-arg FEED_ACCESSTOKEN=$(FEED_ACCESSTOKEN)'
    - task: Docker@2
      inputs:
        containerRegistry: 'TestContainerRegistry'
        repository: '$(workerContainerRepository)'
        command: 'push'
        tags: |
          latest
          $(tag)
    - task: Docker@2
      inputs:
        containerRegistry: 'ProdContainerRegistry'
        repository: '$(workerContainerRepository)'
        command: 'build'
        Dockerfile: '$(workerDockerfile)'
        buildContext: '$(Build.SourcesDirectory)'
        tags: |
          latest
          $(tag)
        arguments: '--build-arg FEED_ACCESSTOKEN=$(FEED_ACCESSTOKEN)'
    - task: Docker@2
      inputs:
        containerRegistry: 'ProdContainerRegistry'
        repository: '$(workerContainerRepository)'
        command: 'push'
        tags: |
          latest
          $(tag)

  - job: PublishNugetContracts
    displayName: Build and Publish Contracts
    dependsOn: 
    - BuildAndTest
    - BuildandPublishWorkerContainer
    - BuildandPublishManagerContainer
    steps:
    - task: UseDotNet@2
      displayName: 'Install .NET 9 SDK'
      inputs:
        packageType: 'sdk'
        version: '9.x'
        installationPath: $(Agent.ToolsDirectory)/dotnet
    - task: DotNetCoreCLI@2
      displayName: Restore
      inputs:
        command: 'restore'
        projects: '$(contractPackageProject)'
        feedsToUse: 'select'
        vstsFeed: '534b90ef-0e1b-475a-8b29-f869c9ffb2fd/202483c3-5d54-4aec-a875-a4bd78523c18'
        verbosityRestore: minimal
    - task: DotNetCoreCLI@2
      displayName: Build
      inputs:
        command: 'build'
        projects: '$(contractPackageProject)'
        arguments: '--no-restore -c Release'
    - task: DotNetCoreCLI@2
      inputs:
        command: 'pack'
        packagesToPack: '$(contractPackageProject)'
        nobuild: true
        versioningScheme: 'byEnvVar'
        versionEnvVar: 'nugetVersion'
        verbosityPack: 'Normal'
    - task: DotNetCoreCLI@2
      inputs:
        command: 'push'
        packagesToPush: '$(Build.ArtifactStagingDirectory)/*.nupkg'
        nuGetFeedType: 'internal'
        publishVstsFeed: '534b90ef-0e1b-475a-8b29-f869c9ffb2fd/202483c3-5d54-4aec-a875-a4bd78523c18'
    
  - job: PublishDeployArtifacts
    displayName: Build and Publish Deployment artifacts
    steps:
    - task: UseDotNet@2
      displayName: 'Install .NET 9 SDK'
      inputs:
        packageType: 'sdk'
        version: '9.x'
        installationPath: $(Agent.ToolsDirectory)/dotnet
    - task: replacetokens@3
      inputs:
        rootDirectory: '$(Build.SourcesDirectory)/Deploy'
        targetFiles: '**/*.yml'
        encoding: 'auto'
        writeBOM: false
        actionOnMissing: 'fail'
        keepToken: false
        tokenPrefix: '#{'
        tokenSuffix: '}#'
        useLegacyPattern: false
        enableTelemetry: true
    - task: PublishBuildArtifacts@1
      displayName: Publish deployment files
      inputs:
        PathtoPublish: '$(Build.SourcesDirectory)/Deploy'
        ArtifactName: 'Deploy'
        publishLocation: 'Container'