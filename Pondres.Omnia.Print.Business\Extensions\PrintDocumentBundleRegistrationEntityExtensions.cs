﻿using Pondres.Omnia.Print.Contracts.Api.Document;
using Pondres.Omnia.Print.Storage.Entities.PrintDocument;

namespace Pondres.Omnia.Print.Business.Extensions;

public static class PrintDocumentBundleRegistrationEntityExtensions
{
    public static PrintDocumentBundle ToPrintDocumentBundle(this PrintDocumentBundleRegistrationEntity entity) =>
        new()
        {
            BundleId = entity.BundleId,
            BatchName = entity.BatchName,
            CreatedOn = entity.CreatedOn,
            GordNumber = entity.GordNumber,
            TaskNumber = entity.TaskNumber,
            SequenceId = entity.SequenceId
        };
}
