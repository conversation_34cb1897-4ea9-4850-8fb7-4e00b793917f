﻿using Pondres.Omnia.Print.IntegrationTests.PrintClient;

namespace Pondres.Omnia.Print.IntegrationTests.Fixtures.Api;

public class DocumentApiFixture
{
    private readonly PrintManagerClient client;

    public DocumentApiFixture(PrintManagerClient client)
    {
        this.client = client;
    }

    public static PrintDocumentListFilter CreateListFilter(string customer)
    {
        return new PrintDocumentListFilter
        {
            Customer = customer,
            MaxPageSize = 100
        };
    }

    public async Task<PrintDocumentListItemPagedList> GetDocumentPagedAsync(PrintDocumentListFilter filter)
    {
        var response = await client.DocumentPagedListAsync(filter);

        return response;
    }

    public async Task<PrintDocumentListItem?> GetSingleDocumentByCustomerReferenceAsync(string customerDocumentReference, string customer)
    {
        var filter = CreateListFilter(customer);
        filter.CustomerDocumentReference = customerDocumentReference;

        var pagedList = await GetDocumentPagedAsync(filter);

        return pagedList.Items.SingleOrDefault();
    }
}
