# generic api chart

replicaCount: 1

namespace: solude

image:
  repository: "#|ContainerRegistry|#.azurecr.io/omnia-print-printmanager"
  pullPolicy: IfNotPresent
  tag: "#|Build.BuildNumber|#"

imagePullSecrets:
  - name: acr-pondres-secret

nameOverride: "print-manager" # name of application
fullnameOverride: "print-manager" # name of application

config:
  ASPNETCORE_ENVIRONMENT: "#|ASPNETCORE_ENVIRONMENT|#"
  RELEASE_NAME: "#|Release.ReleaseName|##|Release.AttemptNumber|#"
  VaultName: "kv-sol-print-#|OmniaEnvironment|#"
  TZ: "Europe/Amsterdam"
  CustomersExcludedFromDirectComplete: "#|CUSTOMERS_EXCLUDED_FROM_DIRECTCOMPLETE|#"
  NiesEnabled: false # TODO
  NiesEndpointAddress: "" # TODO
  FileStoragePath: "" # TODO
  PrintFolderPath: "" # TODO
  
secrets:
  NavisionXMLInfoLogicAppConnectionString: "" # TODO

podAnnotations: {}

podLabels:
  azure.workload.identity/use: "true"

service:
  type: ClusterIP
  port: 80
  containerPort: 8080

serviceAccount:
  enabled: true
  name: "#|ServiceAccountName|#"

probes:
  initialDelaySeconds: 0
  periodSeconds: 10
  timeoutSeconds: 2
  successThreshold: 1
  failureThreshold: 5
  http:
    livenessPath: /health/live
    readinessPath: /health/ready
    scheme: HTTP

hostAliases:
  - ip: "#|NIES_IPADDRESS|#"
    hostnames:
      - "#|NIES_HOSTALIASES|#"

ingress:
  enabled: false # enable if API needs to be accesible from outside the cluster
  className: "nginx-external"
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
  hosts:
    - host: "#|Solude.BaseApiDomain|#"
      paths:
        - path: /print
          pathType: Prefix
  tls:
    - secretName: ingress-tls-csi
      hosts:
        - "#|Solude.BaseApiDomain|#"

resources:
  limits:
    cpu: 250m
    memory: 512Mi
  requests:
    cpu: 100m
    memory: 512Mi

volumes:
#  - name: pondressync-storage #TODO
#    azureFile:
#      secretName: pondressync-shared-storage-secret
#      shareName: "scaler-pondressync-storage/PondresShare"
#      readOnly: false               
  - name: secrets-store-inline
    csi:
      driver: secrets-store.csi.k8s.io
      readOnly: true
      volumeAttributes:
        secretProviderClass: "azure-tls"
  - name: tmp
    emptyDir: {}
#  - name: inspire-scaler-shared-storage #TODO
#    azureFile:
#      secretName: quadient-shared-storage-secret
#      shareName: scaler-shared-storage
#      readOnly: false    

volumeMounts:
  - name: secrets-store-inline
    mountPath: "/mnt/secrets-store"
    readOnly: true   
#  - name: pondressync-storage # TODO
#    mountPath: "/mnt/PondresShare"
  - name: tmp
    mountPath: /tmp
#  - name: inspire-scaler-shared-storage #TODO
#    mountPath: "/mnt/scaler"    

nodeSelector: {}

tolerations: []

affinity: {}
