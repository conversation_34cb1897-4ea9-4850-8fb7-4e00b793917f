﻿using Moq.AutoMock;
using Moq;
using Pondres.Omnia.Print.Business.Handler.PrintBatch;
using Pondres.Omnia.Print.Storage.Repositories;
using Microsoft.Extensions.Options;
using Pondres.Omnia.Print.Contracts.Options;
using System.Threading.Tasks;
using Xunit;
using Pondres.Omnia.Print.Storage.Entities.PrintBundle;
using System;
using Pondres.Omnia.Print.Storage.Entities.Print;
using Pondres.Omnia.Print.Storage.Entities.PrintBundleStatus;
using System.Collections.Generic;
using Pondres.Omnia.Print.Contracts.Api.Bundle;
using FluentAssertions;
using Solude.StorageBase.Model;

namespace Pondres.Omnia.Print.Business.UnitTests.Handler;
public class PrintBatchActionHandlerTests
{
    private readonly AutoMocker mocker;

    private readonly Mock<IPrintBundleDetailsRepository> printBundleDetailsRepository;

    private readonly PrintBatchActionHandler handler;
    private readonly PrintBatchOptions batchOptions = new() { PrintFolderPath = "\\testPath" };

    public PrintBatchActionHandlerTests()
    {
        mocker = new AutoMocker(MockBehavior.Strict);

        mocker.GetMock<IOptions<PrintBatchOptions>>().Setup(x => x.Value).Returns(batchOptions);

        handler = mocker.CreateInstance<PrintBatchActionHandler>();

        printBundleDetailsRepository = mocker.GetMock<IPrintBundleDetailsRepository>();
    }

    [Fact]
    public async Task GetBatches_WhenBundlesFound_ReturnBatch()
    {
        // Arrange
        var batchName = "batch1";
        var customer = "testCustomer";
        var DGCode = "dgCode";
        var printerType = "FC";

        var bundle1 = CreateBundle(batchName, customer, DGCode, printerType);
        var bundle2 = CreateBundle(batchName, customer, DGCode, printerType);

        var bundles = new List<PrintBundleDetailsEntity>
        {
            bundle1,
            bundle2
        };

        printBundleDetailsRepository
            .Setup(x => x.GetByFilterAsync(It.IsAny<PrintBundleDefaultFilter>()))
            .ReturnsAsync(new ListResult<PrintBundleDetailsEntity>(bundles, It.IsAny<string>()));

        // Act
        var result = await handler.GetBatchesAsync(It.IsAny<PrintBundleDefaultFilter>());

        // Assert
        mocker.VerifyAll();
        result.Items.Should().HaveCount(1);
    }

    [Fact]
    public async Task GetBatches_WhenMultipleBatchNamesFound_ReturnBatches()
    {
        // Arrange
        var batchName1 = "batch1";
        var batchName2 = "batch2";
        var customer = "testCustomer";
        var DGCode = "dgCode";
        var printerType = "FC";

        var bundle1 = CreateBundle(batchName1, customer, DGCode, printerType);
        var bundle2 = CreateBundle(batchName2, customer, DGCode, printerType);

        var bundles = new List<PrintBundleDetailsEntity>
        {
            bundle1,
            bundle2
        };

        printBundleDetailsRepository
            .Setup(x => x.GetByFilterAsync(It.IsAny<PrintBundleDefaultFilter>()))
            .ReturnsAsync(new ListResult<PrintBundleDetailsEntity>(bundles, It.IsAny<string>()));

        // Act
        var result = await handler.GetBatchesAsync(It.IsAny<PrintBundleDefaultFilter>());

        // Assert
        mocker.VerifyAll();
        result.Items.Should().HaveCount(2);
        result.Items[0].BatchName.Should().Be(batchName1);
        result.Items[1].BatchName.Should().Be(batchName2);
    }

    [Fact]
    public async Task GetBatches_WhenMultipleDGCodesFound_ReturnBatches()
    {
        // Arrange
        var batchName1 = "batch1";
        var customer = "testCustomer";
        var DGCode1 = "dgCode1";
        var DGCode2 = "dgCode2";
        var printerType = "FC";

        var bundle1 = CreateBundle(batchName1, customer, DGCode1, printerType);
        var bundle2 = CreateBundle(batchName1, customer, DGCode2, printerType);

        var bundles = new List<PrintBundleDetailsEntity>
        {
            bundle1,
            bundle2
        };

        printBundleDetailsRepository
            .Setup(x => x.GetByFilterAsync(It.IsAny<PrintBundleDefaultFilter>()))
            .ReturnsAsync(new ListResult<PrintBundleDetailsEntity>(bundles, It.IsAny<string>()));

        // Act
        var result = await handler.GetBatchesAsync(It.IsAny<PrintBundleDefaultFilter>());

        // Assert
        mocker.VerifyAll();
        result.Items.Should().HaveCount(2);
    }

    [Fact]
    public async Task GetBatches_WhenMultipleDGCodesIncludingEmptyDGCodeFound_ReturnBatches()
    {
        // Arrange
        var batchName1 = "batch1";
        var customer = "testCustomer";
        var DGCode1 = "dgCode1";
        var DGCode2 = "dgCode2";
        var DGCode3 = "";
        var printerType = "FC";

        var bundle1 = CreateBundle(batchName1, customer, DGCode1, printerType);
        var bundle2 = CreateBundle(batchName1, customer, DGCode2, printerType);
        var bundle3 = CreateBundle(batchName1, customer, DGCode3, printerType);

        var bundles = new List<PrintBundleDetailsEntity>
        {
            bundle1,
            bundle2,
            bundle3
        };

        printBundleDetailsRepository
            .Setup(x => x.GetByFilterAsync(It.IsAny<PrintBundleDefaultFilter>()))
            .ReturnsAsync(new ListResult<PrintBundleDetailsEntity>(bundles, It.IsAny<string>()));

        // Act
        var result = await handler.GetBatchesAsync(It.IsAny<PrintBundleDefaultFilter>());

        // Assert
        mocker.VerifyAll();
        result.Items.Should().HaveCount(3);
    }

    private static PrintBundleDetailsEntity CreateBundle(string batchName, string customer, string DGCode, string printerType)
    {
        var metadata = new PrintDocumentMetadataEntity
        {
            DGCode = DGCode,
            PrinterType = printerType,
            PrintMode = "EmptyGroup",
            SheetPageCount = 1,
            PageCount = 1
        };

        var status = new PrintBundleStatusDetailsEntity
        {
            Status = "WaitingForPrint"
        };

        var quantities = new PrintBundleQuantitiesEntity
        {
            Quantity = 2,
            DocumentCount = 6,
            PageCount = 3
        };

        var files = new List<string>
        {
            "file1",
            "file2"
        };

        return new PrintBundleDetailsEntity
        {
            Id = Guid.NewGuid().ToString(),
            BatchName = batchName,
            Customer = customer,
            GordNumber = "gordnumber",
            Metadata = metadata,
            LastStatus = status,
            Quantities = quantities,
            Files = files
        };
    }
}
