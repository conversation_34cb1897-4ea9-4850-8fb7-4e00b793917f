﻿using Pondres.Omnia.Print.Contracts.Common;
using Pondres.Omnia.Print.Contracts.PrintBundle;
using System;

namespace Pondres.Omnia.Print.Business.UnitTests.Helper;
public static class TestPrintBundleHelper
{
    public static PrintBundleQueuedDocument CreateQueuedDocument(string? documentId = null, string? customer = null)
    {
        return new PrintBundleQueuedDocument
        (
            OrderMetadata: new OrderMetadata
            {
                Flow = RandomValue("Flow"),
                Customer = customer ?? RandomValue("Customer"),
                CustomerReference = RandomValue("CustomerReference"),
                OrderId = Guid.NewGuid()
            },
            DocumentMetadata: new Contracts.PrintDocument.PrintDocumentMetadata
            {
                CustomerDocumentReference = RandomValue("CustomerDocumentReference"),
                DocumentId = documentId ?? RandomValue("DocumentId"),
                Quantity = 1,
                PageCount = 1,
            },
            PrintFilePath: RandomValue("printFilePath"),
            CreationHash: Guid.NewGuid().ToString())
        {
            SequenceId = 0
        };
    }

    private static string RandomValue(string prefix) => $"{prefix}_{Guid.NewGuid()}";

    public static string DirectCompleteDisabledCustomer => "DirectCompleteDisabledCustomer";
}
