﻿using Pondres.Omnia.Print.Contracts.PrintBundle;
using Pondres.Omnia.Print.Storage.Entities.PrintBundleStatus;

namespace Pondres.Omnia.Print.Business.Extensions;

public static class PrintBundleStatusUpdateExtensions
{
    public static PrintBundleStatusHistoryEntity ToStatusEntity(this PrintBundleStatusUpdate message) =>
        PrintBundleStatusHistoryEntity.Create(
            timestamp: message.Timestamp,
            bundleId: message.PrintBundleId.ToString(),
            details: message.ToStatusDetailsEntity());

    public static PrintBundleStatusDetailsEntity ToStatusDetailsEntity(this PrintBundleStatusUpdate message)
        => new()
        {
            Timestamp = message.Timestamp,
            Message = message.Message,
            Status = message.Status,
            WaitingForInput = message.WaitingForInput,
            IsInFailedState = message.IsInFailedState
        };
}