﻿using FluentAssertions;
using MassTransit;
using Pondres.Omnia.OrderHub.IntegrationTests.Helper;
using Pondres.Omnia.Print.IntegrationTests.Observers;

namespace Pondres.Omnia.Print.IntegrationTests.Extensions;
public static class ISendEndpointExtensions
{
    public static async Task SendAndWaitForConsumeAsync<TMessageType>(
        this ISendEndpoint endpoint,
        IBus bus, 
        TMessageType message,
        Func<TMessageType, bool>? filter = null) where TMessageType : class
    {
        var observer = new TestConsumeObserver<TMessageType>(filter);
        using var observerHandle = bus.ConnectConsumeMessageObserver(observer);

        await endpoint.Send(message);

        await TestHelper.WaitForAsync(() =>
        {
            observer.MessagePreConsumed.Should().BeTrue();

            return Task.CompletedTask;
        });

        await observer.MessageConsumed;
    }
}
