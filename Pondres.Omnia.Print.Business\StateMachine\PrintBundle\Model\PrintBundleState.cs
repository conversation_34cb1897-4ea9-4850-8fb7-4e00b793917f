﻿using MassTransit;
using Pondres.Omnia.Print.Contracts.Common;
using System;

namespace Pondres.Omnia.Print.Business.StateMachine.PrintBundle.Model;

public class PrintBundleState : SagaStateMachineInstance, ISagaVersion
{
    public PrintBundleOptions BundleOptions { get; set; } = new();
    public Guid? CompletionTimeoutTokenId { get; set; }
    public Guid CorrelationId { get; set; }

    public DateTimeOffset CreatedOn { get; set; }
    public string CurrentState { get; set; } = string.Empty;

    public string Customer { get; set; } = string.Empty;
    public string InitiatedBy { get; set; } = string.Empty;

    public int DocumentsTotalQuantity { get; set; }
    public int DocumentsTotalPageCount { get; set; }
    public int DocumentsTotalRecordCount { get; set; }

    public int Version { get; set; }
    public string BatchName { get; set; } = string.Empty;
    public int SheetPageCount { get; set; }
    public string SheetArticleCode { get; set; } = string.Empty;
    public string? PrintMode { get; set; }
    public string? DGCode { get; set; }
    public string? GordNumber { get; set; }
    public int TaskNumber { get; set; }
    public string EndLocation { get; set; } = string.Empty;
    public DateTimeOffset? PlannedOn { get; set; }
    public Guid? StartTimeoutTokenId { get; set; }

    public bool IsPrimary { get; set; }
    public Guid PrimaryBundleId { get; set; }
    public DateTimeOffset? ReleasedOn { get; set; }
    public bool IsDirectlyCompleted { get; set; }
}