﻿using FluentAssertions;
using Moq;
using Moq.AutoMock;
using Solude.StorageBase.Model;
using Pondres.Omnia.Print.Business.Handler;
using Pondres.Omnia.Print.Contracts.PrintBundle;
using Pondres.Omnia.Print.Storage.Entities.PrintBundleStatus;
using Pondres.Omnia.Print.Storage.Repositories;
using System;
using System.Threading.Tasks;
using Xunit;

namespace Pondres.Omnia.Print.Business.UnitTests.Handler;

public class PrintBundleEventHistoryUpdateHandlerTests
{
    private readonly AutoMocker mocker;
    private readonly PrintBundleEventHistoryUpdateHandler handler;

    public PrintBundleEventHistoryUpdateHandlerTests()
    {
        mocker = new AutoMocker(MockBehavior.Strict);

        handler = mocker.CreateInstance<PrintBundleEventHistoryUpdateHandler>();
    }

    [Fact]
    public async Task RegisterBundleCompletionAsync_CompletesBundleState()
    {
        // Arrange
        var bundleId = Guid.NewGuid();

        var message = new PrintBundleCompleted
        {
            PrintBundleId = bundleId,
            Timestamp = DateTimeOffset.Now,
            BatchName = "BatchName",
            CompletedBy = "Tester",
            Message = "Message",
            Status = "Status"
        };

        PrintBundleStatusHistoryEntity? printBundleStatusHistoryEntity = null;
        mocker.GetMock<IPrintBundleStatusHistoryRepository>()
            .Setup(x => x.CreateAsync(It.IsAny<PrintBundleStatusHistoryEntity>()))
            .Callback((PrintBundleStatusHistoryEntity entity) => printBundleStatusHistoryEntity = entity)
            .ReturnsAsync(new CosmosResponse<PrintBundleStatusHistoryEntity>(string.Empty, new()));

        // Act
        await handler.RegisterBundleCompletionEventAsync(message);

        // Assert
        printBundleStatusHistoryEntity.Should().NotBeNull();

        printBundleStatusHistoryEntity!.BundleId.Should().Be(message.PrintBundleId.ToString());
        printBundleStatusHistoryEntity!.CreatedOn.Should().Be(message.Timestamp);
        printBundleStatusHistoryEntity!.Id.Should().Be(message.Timestamp.UtcTicks.ToString());

        printBundleStatusHistoryEntity!.Details.IsInFailedState.Should().BeFalse();
        printBundleStatusHistoryEntity!.Details.Message.Should().Be(message.Message);
        printBundleStatusHistoryEntity!.Details.Status.Should().Be(message.Status);
        printBundleStatusHistoryEntity!.Details.Timestamp.Should().Be(message.Timestamp);
        printBundleStatusHistoryEntity!.Details.WaitingForInput.Should().BeFalse();
    }

    [Fact]
    public async Task UpdateStatusAsync_UpdatesStatus()
    {

        // Arrange
        var bundleId = Guid.NewGuid();

        var message = new PrintBundleStatusUpdate
        {
            PrintBundleId = bundleId,
            BatchName = "BatchName",
            WaitingForInput = true,
            IsInFailedState = false,
            Message = "Message",
            Status = "Status",
            Timestamp = DateTimeOffset.Now,
        };

        mocker.GetMock<IPrintBundleStatusHistoryRepository>()
            .Setup(x => x.CreateOrReplaceAsync(It.IsAny<PrintBundleStatusHistoryEntity>()))
            .ReturnsAsync(new CosmosResponse<PrintBundleStatusHistoryEntity>(string.Empty, new()));

        PrintBundleStatusHistoryEntity? printBundleStatusHistoryEntity = null;
        mocker.GetMock<IPrintBundleStatusHistoryRepository>()
            .Setup(x => x.CreateAsync(It.IsAny<PrintBundleStatusHistoryEntity>()))
            .Callback((PrintBundleStatusHistoryEntity entity) => printBundleStatusHistoryEntity = entity)
            .ReturnsAsync(new CosmosResponse<PrintBundleStatusHistoryEntity>(string.Empty, new()));

        // Act
        await handler.RegisterStatusUpdateEventAsync(message);

        // Assert
        printBundleStatusHistoryEntity.Should().NotBeNull();

        printBundleStatusHistoryEntity!.BundleId.Should().Be(message.PrintBundleId.ToString());
        printBundleStatusHistoryEntity!.CreatedOn.Should().Be(message.Timestamp);
        printBundleStatusHistoryEntity!.Id.Should().Be(message.Timestamp.UtcTicks.ToString());

        printBundleStatusHistoryEntity!.Details.IsInFailedState.Should().Be(message.IsInFailedState);
        printBundleStatusHistoryEntity!.Details.Message.Should().Be(message.Message);
        printBundleStatusHistoryEntity!.Details.Status.Should().Be(message.Status);
        printBundleStatusHistoryEntity.Details.Timestamp.Should().Be(message.Timestamp);
        printBundleStatusHistoryEntity.Details.WaitingForInput.Should().Be(message.WaitingForInput);
    }
}
