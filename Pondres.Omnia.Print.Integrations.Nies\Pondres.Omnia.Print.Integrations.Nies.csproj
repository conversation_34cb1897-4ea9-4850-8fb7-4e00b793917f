﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="MassTransit" Version="8.4.1" />
		<PackageReference Include="Polly" Version="8.5.2" />
		<PackageReference Include="System.ServiceModel.Duplex" Version="6.0.0" />
		<PackageReference Include="System.ServiceModel.Http" Version="8.1.2" />
		<PackageReference Include="System.ServiceModel.NetTcp" Version="8.1.2" />
		<PackageReference Include="System.ServiceModel.Security" Version="6.0.0" />
	</ItemGroup>

	<ItemGroup>
		<WCFMetadata Include="Connected Services" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Pondres.Omnia.Print.Business\Pondres.Omnia.Print.Business.csproj" />
		<ProjectReference Include="..\Pondres.Omnia.Print.Contracts\Pondres.Omnia.Print.Contracts.csproj" />
		<ProjectReference Include="..\Pondres.Omnia.Print.Storage\Pondres.Omnia.Print.Storage.csproj" />
	</ItemGroup>

</Project>
