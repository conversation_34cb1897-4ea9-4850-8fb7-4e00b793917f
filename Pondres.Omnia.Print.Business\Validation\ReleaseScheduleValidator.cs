﻿using FluentValidation;
using Pondres.Omnia.Print.Contracts.Api.ReleaseSchedule;
using Pondres.Omnia.Print.Contracts.Common;
using System.Collections.Generic;
using System.Text.RegularExpressions;

namespace Pondres.Omnia.Print.Business.Validation;

public class ReleaseScheduleValidator : AbstractValidator<ReleaseSchedules>
{
    public const string scheduleType = "releaseSchedule";
    private readonly List<string> daysOfWeek = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"];
    private readonly List<PrintBundleMode> printScheduleTimeTypes = [PrintBundleMode.MaxFullSheets, PrintBundleMode.EmptyGroup];
    private readonly Regex timeRegex = new(@"^([01][0-9]|2[0-3]):([0-5][0-9])$", RegexOptions.None, matchTimeout: System.TimeSpan.FromSeconds(5));

    public ReleaseScheduleValidator()
    {
        RuleFor(r => r.Id).NotEmpty();

        RuleFor(r => r.Type).NotEmpty();
        RuleFor(r => r.Type).Equal(scheduleType).WithMessage($"Schedule type is not specified correctly, should be {scheduleType}");

        RuleFor(r => r.Active).NotEmpty();
        RuleFor(r => r.Active)
            .Must(x => x.ToLower() == bool.TrueString.ToLower() || x.ToLower() == bool.FalseString.ToLower())
            .WithMessage("Active should be true or false");

        RuleForEach(r => r.Schedule).ChildRules(schedule =>
        {
            schedule.RuleFor(s => s.DayOfWeek).NotEmpty();
            schedule.RuleFor(s => s.Releases).NotEmpty();

            schedule.RuleForEach(s => s.DayOfWeek).ChildRules(day =>
            {
                day.RuleFor(d => daysOfWeek.Contains(d.ToLower()));
            });

            schedule.RuleForEach(s => s.Releases).ChildRules(release =>
            {
                release.RuleFor(r => r.Type).NotEmpty();
                release.RuleFor(r => r.Time).NotEmpty();
            });
        });

        RuleForEach(r => r.Schedule).Custom((schedule, context) =>
        {
            foreach (var release in schedule.Releases)
            {
                if (!printScheduleTimeTypes.Contains(release.Type))
                    context.AddFailure("Type for release is not specified correctly, should be MaxFullSheets or EmptyGroup");

                if (!timeRegex.IsMatch(release.Time))
                    context.AddFailure("Time for release should be specified as 24 hour format");
            }

            foreach (var dayOfWeek in schedule.DayOfWeek)
            {
                if (!daysOfWeek.Contains(dayOfWeek.ToLower()))
                    context.AddFailure("Day of week not specified correctly, should be Monday, Tuesday, Wednesday, Thursday, Friday, Saturday or Sunday");
            }
        });
    }
}
