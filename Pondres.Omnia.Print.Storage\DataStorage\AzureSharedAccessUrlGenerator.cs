﻿using Azure;
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Azure.Storage.Sas;
using Pondres.Omnia.Print.Storage.DataStorage.Model;
using Serilog;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Storage.DataStorage;

public class AzureSharedAccessUrlGenerator : ISharedAccessUrlGenerator
{
    private const int pageSize = 500;

    public async Task<TemporaryDataUri> GetSharedAccessSignatureForContainerAsync(
        BlobServiceClient client,
        BlobContainerClient container,
        BlobContainerSasPermissions containerSasPermissions,
        TimeSpan expireTime)
    {
        var expiresOn = DateTimeOffset.UtcNow.Add(expireTime);

        var delegationKey = await client.GetUserDelegationKeyAsync(DateTime.UtcNow, expiresOn);

        var builder = new BlobSasBuilder
        {
            BlobContainerName = container.Name,
            Resource = "b",
            StartsOn = DateTime.UtcNow,
            ExpiresOn = expiresOn
        };

        builder.SetPermissions(containerSasPermissions);
        var queryParameters = builder.ToSasQueryParameters(delegationKey, client.AccountName);

        var sasUri = new UriBuilder(container.Uri)
        {
            Query = queryParameters.ToString()
        };

        return new TemporaryDataUri
        {
            DataUri = sasUri.ToString(),
            ExpiresOn = expiresOn
        };
    }

    public async Task<TemporaryDataUri> GetSharedAccessSignatureUrlForBlobAsync(BlobServiceClient client, BlobContainerClient container, string filePath, TimeSpan expireTime)
    {
        var expiresOn = DateTime.UtcNow + expireTime;
        var delegationKey = await client.GetUserDelegationKeyAsync(DateTime.UtcNow, expiresOn);

        var urlInformation = GetSharedAccessSignatureForBlob(client, container, filePath, delegationKey, expiresOn);

        return urlInformation;
    }

    public async Task<List<TemporaryDataUri>> GetSharedAccessSignatureUrlForBlobsInFolderAsync(BlobServiceClient client, BlobContainerClient container, string folderName, TimeSpan expireTime)
    {
        var expiresOn = DateTime.UtcNow + expireTime;
        var delegationKey = await client.GetUserDelegationKeyAsync(DateTime.UtcNow, expiresOn);

        var urls = new List<TemporaryDataUri>();

        string? continuationToken = null;

        try
        {
            // Call the listing operation and enumerate the result segment.
            // When the continuation token is empty, the last segment has been returned
            // and execution can exit the loop.
            do
            {
                var resultSegment = container
                    .GetBlobs(prefix: folderName)
                    .AsPages(continuationToken, pageSize);

                foreach (var blobPage in resultSegment)
                {
                    foreach (var blobItem in blobPage.Values)
                    {
                        var urlInformation = GetSharedAccessSignatureForBlob(client, container, blobItem.Name, delegationKey, expiresOn);

                        urls.Add(urlInformation);
                    }

                    // Get the continuation token and loop until it is empty.
                    continuationToken = blobPage.ContinuationToken;

                    Console.WriteLine();
                }
            } while (!string.IsNullOrEmpty(continuationToken));
        }
        catch (RequestFailedException exception)
        {
            Log.Error(exception, "Error during blob list");
            throw;
        }

        return urls;
    }

    private static TemporaryDataUri GetSharedAccessSignatureForBlob(
        BlobServiceClient client,
        BlobContainerClient container,
        string filePath,
        Response<UserDelegationKey> delegationKey,
        DateTimeOffset expiresOn)
    {
        var blobClient = container.GetBlobClient(filePath);

        var builder = new BlobSasBuilder
        {
            BlobName = filePath,
            BlobContainerName = container.Name,
            Resource = "b",
            StartsOn = DateTime.UtcNow,
            ExpiresOn = expiresOn
        };

        builder.SetPermissions(BlobSasPermissions.Read);
        var queryParameters = builder.ToSasQueryParameters(delegationKey, client.AccountName);

        var sasUri = new UriBuilder(blobClient.Uri)
        {
            Query = queryParameters.ToString()
        };

        return new TemporaryDataUri
        {
            DataUri = sasUri.ToString(),
            ExpiresOn = expiresOn,
            FileName = filePath.Split("/")[^1]
        };
    }
}