﻿namespace Pondres.Omnia.Print.Contracts.Common
{
    public enum MapPrintBundleState
    {
        None = 0,
        Cancelled = 1,
        Skipped = 2,
        WaitingForBatch = 3,
        BatchCreationFailure = 4,
        Impositioning = 5,
        ImpositioningOnHold = 6,
        Printed = 7,
        WaitingForPrint = 8,
        WaitingForStart = 9,
        Created = 10,
        Started = 11,
        Completed = 12,
        Final = 13,
        WaitingForScan = 14,
        Scanned = 15
    }
}
