﻿using Pondres.Omnia.Print.Business.Extensions;
using Pondres.Omnia.Print.Business.Service;
using Pondres.Omnia.Print.Contracts.Api.Enums;
using Pondres.Omnia.Print.Contracts.Common;
using Pondres.Omnia.Print.Storage.Repositories;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Integrations.OrderHub.Handler;

public class PrintMetadataEnrichmentHandler : EnrichmentHandler<PrintDocumentCreationMetadata>, IPrintMetadataEnrichmentHandler
{
    private readonly IPrintSheetMappingRepository repository;

    public PrintMetadataEnrichmentHandler(IDownloadService downloadService, IPrintSheetMappingRepository repository)
        : base(downloadService)
    {
        this.repository = repository;
    }

    protected override async Task EnrichDataAsync(PrintDocumentCreationMetadata enrichmentData)
    {
        var dataSheet = await repository.GetSingleCachedAsync(enrichmentData.SheetArticleCode);

        enrichmentData.SheetFormat = dataSheet.Resource.SheetFormat;
        enrichmentData.PrinterType = dataSheet.Resource.PrinterType.ToEnumOrDefault(PrinterType.FC);
    }
}