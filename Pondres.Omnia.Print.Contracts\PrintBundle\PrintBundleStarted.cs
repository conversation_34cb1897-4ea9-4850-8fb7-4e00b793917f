﻿using System;

namespace Pondres.Omnia.Print.Contracts.PrintBundle
{
    public class PrintBundleStarted
    {
        public string Customer { get; set; } = string.Empty;
        public Guid PrintBundleId { get; set; }
        public DateTimeOffset Timestamp { get; set; }
        public string Message { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public int DocumentCount { get; set; }
        public string BatchName { get; set; } = string.Empty;
        public int PageCount { get; set; }
    }
}
