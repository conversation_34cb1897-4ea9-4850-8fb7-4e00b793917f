﻿using MassTransit;
using Microsoft.AspNetCore.Mvc;
using Pondres.Omnia.Print.Business.Service;
using Pondres.Omnia.Print.Contracts.Api.Common;
using Pondres.Omnia.Print.Contracts.Api.Document;
using Pondres.Omnia.Print.Contracts.PrintDocument;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.PrintManager.Controllers;

[Route("document")]
public class PrintDocumentController : Controller
{
    private readonly IPrintDocumentService printDocumentService;
    private readonly IBus bus;

    public PrintDocumentController(IPrintDocumentService printDocumentService, IBus bus)
    {
        this.printDocumentService = printDocumentService;
        this.bus = bus;
    }

    [HttpPost]
    [Route("pagedList")]
    [ProducesResponseType(200, Type = typeof(PagedList<PrintDocumentListItem>))]
    public async Task<IActionResult> GetDocumentListPaged([FromBody] PrintDocumentListFilter filter)
    {
        Log.Debug("Retrieving document with filter {Filter}", filter);

        return Ok(await printDocumentService.GetDocumentsByFilterAsync(filter));
    }

    [HttpPost]
    [Route("pagedBatchList")]
    [ProducesResponseType(200, Type = typeof(PagedList<PrintDocumentListItem>))]
    public async Task<IActionResult> GetDocumentBatchListPaged([FromBody] PrintDocumentBatchListFilter filter)
    {
        Log.Debug("Retrieving document with filter {Filter}", filter);

        return Ok(await printDocumentService.GetDocumentsByBatchFilterAsync(filter));
    }

    [HttpPost]
    [Route("cancelList")]
    [ProducesResponseType(200)]
    public async Task<IActionResult> CancelSelectedDocuments([FromBody] List<Contracts.Api.Document.PrintDocumentReprintRequest> items)
    {
        try
        {
            var requeueMessages = items.Select(x => new PrintDocumentRequeueCommand(x.Customer, x.OrderId, x.Username));
            await bus.PublishBatch(requeueMessages);

            return Ok();
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }
}