﻿using MassTransit;
using Pondres.Omnia.Print.Business.Service;
using Pondres.Omnia.Print.Business.StateMachine.PrintBundle.Model;
using Pondres.Omnia.Print.Contracts.PrintDocument;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Activities.Bundle;

public class PrintBundleSortDocumentsActivity : BaseInstanceActivity<PrintBundleState>
{
    private readonly IPrintBundleDocumentStorageService bundleDocumentStorageService;

    public PrintBundleSortDocumentsActivity(IPrintBundleDocumentStorageService bundleDocumentStorageService)
    {
        this.bundleDocumentStorageService = bundleDocumentStorageService;
    }

    public override string ActivityName => "sort-print-document-activity";

    protected override async Task ExecuteAsync(BehaviorContext<PrintBundleState> context)
    {
        List<PrintDocumentAssignSequenceId> assignSequenceIdToDocumentsCommands = [];

        await bundleDocumentStorageService.UpdateDocumentCollectionAsync(context.Saga.CorrelationId, collection =>
        {
            assignSequenceIdToDocumentsCommands = new List<PrintDocumentAssignSequenceId>(collection.Documents.Count);

            var sequenceId = 1;
            foreach (var document in collection.Documents)
            {
                document.SequenceId = sequenceId;

                assignSequenceIdToDocumentsCommands.Add(new PrintDocumentAssignSequenceId
                {
                    PrintBundleId = context.Saga.CorrelationId,
                    DocumentId = document.DocumentMetadata.DocumentId,
                    SequenceId = sequenceId,
                    GordNumber = context.Saga.GordNumber,
                    TaskNumber = context.Saga.TaskNumber
                });

                sequenceId++;
            }
        });

        var publishTasks = assignSequenceIdToDocumentsCommands.Select(assignSequenceIdToDocumentCommand => context.Publish(assignSequenceIdToDocumentCommand)).ToList();
        await Task.WhenAll(publishTasks);
    }
}