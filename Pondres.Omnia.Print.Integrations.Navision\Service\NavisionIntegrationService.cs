﻿using Pondres.Omnia.Print.Integrations.Navision.Extensions;
using Pondres.Omnia.Print.Integrations.Navision.Mappings;
using Pondres.Omnia.Print.Integrations.Navision.Model;
using Pondres.Omnia.Print.Integrations.Navision.Model.Bundle;
using Pondres.Omnia.Print.Integrations.Navision.Uploader;
using Pondres.Omnia.Print.Navision.Model;
using Serilog;

namespace Pondres.Omnia.Print.Integrations.Navision.Service;

public class NavisionIntegrationService : INavisionIntegrationService
{
    private readonly INavisionXmlUploader xmlUploader;

    public NavisionIntegrationService(INavisionXmlUploader xmlUploader)
    {
        this.xmlUploader = xmlUploader;
    }

    public void SendPrintBundleXmlToNavision(SendBundleInfoToNavisionRequestBundle requestBundle)
    {
        var isExcludedEndLocation = NavisionDataMappings.ExcludedEndLocations.Contains(requestBundle.Primary.BundleMetadata.EndLocation);
        var hasDgCode = !string.IsNullOrWhiteSpace(requestBundle.Primary.DGCode);

        if (isExcludedEndLocation || !hasDgCode)
        {
            Log.Warning("EndLocation {EndLocation} is on the exclusion list or there is no dgCode, not sending anything to navision", requestBundle.Primary.BundleMetadata.EndLocation);
            return;
        }

        var navisionPrintBundle = CreateGroupedBundleForNavision(requestBundle);
        xmlUploader.UploadNavisionPrintBundle(navisionPrintBundle, requestBundle.Primary.GordNumber, isExcludedEndLocation);
    }

    public void SendExcludedLocationPrintBundleXmlToNavision(
        SendBundleInfoToNavisionRequest request,
        NavisionPrintBundle navisionPrintBundle)
    {
        var hasDgCode = !string.IsNullOrWhiteSpace(request.DGCode);

        if (!hasDgCode)
        {
            Log.Warning("There is no dgCode, not sending anything to navision", request.BundleMetadata.EndLocation);
            return;
        }

        xmlUploader.UploadNavisionPrintBundle(navisionPrintBundle, request.GordNumber, true);
    }

    public static NavisionPrintBundle CreateBundleForNavision(SendBundleInfoToNavisionRequest requestBundle)
    {
        var dataRules = new List<NavisionPrintDataRule>();

        var packagingAndAttachmentsDataRules = NavisionDataMappings.GetPackagingDataRules(requestBundle);
        var sheetArticleCodeDataRules = NavisionDataMappings.GetSheetArticleCodeDataRules(requestBundle);

        dataRules.AddRange(packagingAndAttachmentsDataRules);
        dataRules.AddRange(sheetArticleCodeDataRules);

        return requestBundle.ToNavisionPrintBundle(dataRules);
    }

    public static NavisionPrintBundle CreateGroupedBundleForNavision(SendBundleInfoToNavisionRequestBundle requestBundle)
    {
        var dataRules = new List<NavisionPrintDataRule>();

        var packagingAndAttachmentsDataRules = NavisionDataMappings.GetPackagingDataRules(requestBundle.Primary);
        var sheetArticleCodeDataRules = NavisionDataMappings.GetSheetArticleCodeDataRules(requestBundle.Primary);

        dataRules.AddRange(packagingAndAttachmentsDataRules);
        dataRules.AddRange(sheetArticleCodeDataRules);

        foreach (var bundle in requestBundle.Secondary)
        {
            var secondarySheetArticleCodeDataRules = NavisionDataMappings.GetSheetArticleCodeDataRules(bundle);
            dataRules.AddRange(secondarySheetArticleCodeDataRules);
        }

        return requestBundle.Primary.ToNavisionPrintBundle(dataRules);
    }
}
