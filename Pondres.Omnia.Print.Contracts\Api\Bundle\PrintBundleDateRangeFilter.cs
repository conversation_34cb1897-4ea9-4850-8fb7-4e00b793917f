﻿using Pondres.Omnia.Print.Contracts.Api.Enums;
using System;

namespace Pondres.Omnia.Print.Contracts.Api.Bundle
{
    public class PrintBundleDateRangeFilter
    {
        public PrinterType? PrinterType { get; set; }
        public string Customer { get; set; } = string.Empty;
        public DateTimeOffset CreatedOnFrom { get; set; }
        public DateTimeOffset CreatedOnTo { get; set; }
        public bool TestCustomers { get; set; }
    }
}
