﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace Pondres.Omnia.Print.Storage.Entities.Schedule;

public class PrintScheduleDateOverrideEntity
{
    [JsonProperty("date")]
    public DateTime Date { get; set; }

    [JsonProperty("printerTypeName")] public string PrinterTypeName { get; set; } = string.Empty;

    [JsonProperty("runtimeOverrides")]
    public List<PrintScheduleTimeEntity> RuntimeOverrides { get; set; } = [];
}