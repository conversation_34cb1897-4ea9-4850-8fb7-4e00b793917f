﻿using Pondres.Omnia.Print.Common.Model;
using Pondres.Omnia.Print.Contracts.Common;
using Pondres.Omnia.Print.Contracts.PrintDocument;
using System;

namespace Pondres.Omnia.Print.Business.Extensions;

public static class PrintDocumentCreationModelExtensions
{
    public static PrintDocumentSaveCommand ToSaveCommand(
        this PrintDocumentCreationModel model,
        PrintDocumentCreationMetadata metadata,
        Guid bundleId) =>
        new()
        {
            OrderMetadata = model.OrderMetadata,
            DocumentMetadata = metadata.ToPrintDocumentMetadata(model.DocumentId, model.PrimaryDocumentId),
            PrintBundleId = bundleId,
            FileInformation = model.FileInformation,
            MailDate = metadata.MailDate,
            Timestamp = DateTimeOffset.Now
        };
}