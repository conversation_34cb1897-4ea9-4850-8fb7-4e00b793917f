﻿using System;
using System.ComponentModel;
using System.Linq;
using System.Reflection;

namespace Pondres.Omnia.Print.Common.Extensions;

public static class EnumExtensions
{
    public static string GetEnumDescription(this Enum value)
    {
        return ((DescriptionAttribute?)Attribute.GetCustomAttribute(
            value.GetType().GetFields(BindingFlags.Public | BindingFlags.Static)
                .Single(x => value.Equals(x.GetValue(null))),
            typeof(DescriptionAttribute)))?.Description ?? value.ToString();
    }
}
