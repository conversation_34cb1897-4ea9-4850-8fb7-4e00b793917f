﻿using Microsoft.Extensions.Options;
using Pondres.Omnia.Print.Business.Helper;
using Pondres.Omnia.Print.Integrations.Navision.Model;
using Pondres.Omnia.Print.Integrations.Navision.Service;
using Pondres.Omnia.Print.Storage.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Pondres.Omnia.Print.Storage.Entities.PrintBundle;
using Pondres.Omnia.Print.Contracts.Common;
using Pondres.Omnia.Print.Integrations.Navision.Mappings;
using Pondres.Omnia.Print.Integrations.Navision.Model.Bundle;

namespace Pondres.Omnia.Print.Business.Handler.Navision;

public class DGImportHandler(
    IPrintBundleDetailsRepository printBundleDetailsRepository,
    IPrintBundleStatusHistoryRepository printBundleStatusHistoryRepository,
    IOptions<NavisionIntegrationOptions> options) : IDGImportHandler
{
    public async Task<Dictionary<SendBundleInfoToNavisionRequest, NavisionPrintBundle>> GetExcludedLocationBundlesForTimeRangeAsync(DateTimeOffset timeRangeStart, DateTimeOffset timeRangeEnd)
    {
        var bundleStatusesSinceLastRun = await printBundleStatusHistoryRepository.GetBundlesWithStatusWithinTimeRangeAsync(
            timeRangeStart: timeRangeStart,
            timeRangeEnd: timeRangeEnd,
            status: MapPrintBundleState.WaitingForPrint.ToString());

        if (bundleStatusesSinceLastRun.Count == 0)
            return [];

        var bundles = await printBundleDetailsRepository.GetBundlesWithExcludedLocationAsync(
            printBundleIds: bundleStatusesSinceLastRun.Select(x => x.BundleId),
            excludedCustomers: options.Value.ExcludedCustomers,
            excludedEndLocations: NavisionDataMappings.ExcludedEndLocations);

        if (bundles.Count == 0)
            return [];

        var navisionPrintBundleRequests = CreateSendBundleInfoToNavisionRequest(bundles);

        var requestsWithBundle = navisionPrintBundleRequests.ToDictionary(x => x, NavisionIntegrationService.CreateBundleForNavision);

        return requestsWithBundle;
    }

    private static List<SendBundleInfoToNavisionRequest> CreateSendBundleInfoToNavisionRequest(List<PrintBundleDetailsEntity> bundles)
    {
        var groupedBundles = bundles.GroupBy(x => new { x.Metadata.SheetArticleCode, x.Metadata.DGCode });

        var navisionPrintBundleRequests = new List<SendBundleInfoToNavisionRequest>();

        var pointer = 0;

        foreach (var groupedBundle in groupedBundles)
        {
            var firstBundle = groupedBundle.First();

            ArgumentNullException.ThrowIfNull(firstBundle.Metadata.DGCode);

            ArgumentNullException.ThrowIfNull(firstBundle.GordNumber);

            var navisionRequest = new SendBundleInfoToNavisionRequest
            {
                BundleIds = groupedBundle.Select(x => x.Id).ToList(),
                BatchName = PrintBatchHelper.CreateBatchName(DateTimeOffset.Now),
                BundleMetadata = firstBundle.Metadata,
                DGCode = firstBundle.Metadata.DGCode,
                DocumentCount = groupedBundle.Sum(x => x.Quantities.DocumentCount),
                GordNumber = firstBundle.GordNumber,
                SheetCount = groupedBundle.Sum(x =>
                    SheetCountCalculationHelper.CalculateSheetCount(
                        x.Metadata.PrintMode,
                        x.Metadata.SheetPageCount,
                        x.Quantities.PageCount)),
                TaskNumber = pointer
            };

            navisionPrintBundleRequests.Add(navisionRequest);

            pointer++;
        }

        return navisionPrintBundleRequests;
    }
}
