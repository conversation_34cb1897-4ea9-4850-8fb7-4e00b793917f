﻿using Newtonsoft.Json;
using System;

namespace Pondres.Omnia.Print.Storage.Entities.PrintBundle;

public class PrintBundleQuantitiesEntity
{
    [JsonProperty("quantity")]
    public int Quantity { get; set; }

    [JsonProperty("pageCount")]
    public int PageCount { get; set; }

    [JsonProperty("recordCount")]
    public int DocumentCount { get; set; }

    [JsonProperty("timestamp")]
    public DateTimeOffset Timestamp { get; set; }
}