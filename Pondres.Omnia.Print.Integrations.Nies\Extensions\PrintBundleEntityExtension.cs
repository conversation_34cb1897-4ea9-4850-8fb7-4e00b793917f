﻿using ExportServiceClient;
using Pondres.Omnia.Print.Contracts.PrintBundle;
using Pondres.Omnia.Print.Integrations.Nies.Model;
using Pondres.Omnia.Print.Storage.Entities.Mapping;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Pondres.Omnia.Print.Integrations.Nies.Extensions;

public static class PrintBundleEntityExtension
{
    private const string naam = "Printbundels Omnia";
    private const string straat = "Kraaivenstraat";
    private const string huisnr = "19";
    private const string postcode = "5048AB";
    private const string plaats = "Tilburg";
    private const string orderbron = "Omnia";

    public static order[] ToExport(this IEnumerable<PrintBundleCompleted> printBundles, string division)
    {
        return new order[]
        {
            new order()
            {
                divisie = division,
                ordernr = string.Format("{0:yyyyMMddHHmmssfff}", DateTime.Now),
                naam = naam,
                naam2 = string.Format("Run : {0:ddMMyyyy} - {0:HH:mm}", DateTime.Now),
                straat = straat,
                huisnr = huisnr,
                postcode = postcode,
                plaats = plaats,
                orderbron = orderbron,
                datetimestamp = DateTime.Now,
                orderdatum = DateTime.Now,
                orderline = CreateOrderlines(printBundles)
            }
        };
    }

    public static IEnumerable<OrderToSendResult> ToOrdersToSend(this IEnumerable<PrintBundleCompleted> printBundles, IEnumerable<DivisionMappingEntity> mappings)
    {
        return printBundles
            .Where(printbundle => mappings.Select(mapping => mapping.Id).Contains(printbundle.Customer))
            .GroupBy(printbundle => new
            {
                customer = printbundle.Customer,
                division = mappings.First(mapping => mapping.Id == printbundle.Customer).Division
            })
            .Select(group => new OrderToSendResult()
            {
                Orders = group.ToExport(group.Key.division),
                Division = group.Key.division
            });
    }

    private static OrderLine[] CreateOrderlines(IEnumerable<PrintBundleCompleted> printBundles)
    {
        return printBundles
                    .GroupBy(bundle => bundle.SheetArticleCode)
                    .Select(bundlegroup => new OrderLine()
                    {
                        artikelnr = bundlegroup.Key,
                        aantal = bundlegroup.DetermineDocumentCountIncludePaperLoss()
                    }).ToArray();
    }

    private static int DetermineDocumentCountIncludePaperLoss(this IGrouping<string, PrintBundleCompleted> bundlegroup)
    {
        var paperLossFactor = 1.06;
        double documentCount = 0;
        if(UseDocumentCount(bundlegroup.Key))
        {
            documentCount = bundlegroup.Sum(bundle => bundle.DocumentCount);
        }
        else
        {
            documentCount = bundlegroup.Sum(bundle => bundle.SheetPageCount);
        }
        return (int)Math.Ceiling(documentCount * paperLossFactor);
    }

    private static bool UseDocumentCount(string articleNumber)
    {
        string[] countByDocumentsArticleNumbers = { "G2216HAL", "G1104HAL" };
        return countByDocumentsArticleNumbers.Contains(articleNumber);
    }

    public static IEnumerable<string> ToCustomerList(this IEnumerable<PrintBundleCompleted> printBundles) =>
        printBundles.Select(bundle => bundle.Customer).Distinct();

    public static IEnumerable<string> ToCustomersWithoutMappingList(this IEnumerable<PrintBundleCompleted> printBundles, IEnumerable<DivisionMappingEntity> mappingEntities) =>
        printBundles.ToCustomerList().Where(customer => !mappingEntities.Select(mapping => mapping.Id).Contains(customer));
}
