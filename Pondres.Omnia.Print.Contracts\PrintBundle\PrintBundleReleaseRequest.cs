﻿using Pondres.Omnia.Print.Contracts.Common;
using System;

namespace Pondres.Omnia.Print.Contracts.PrintBundle
{
    public class PrintBundleReleaseRequest
    {
        public Guid PrintBundleId { get; set; }
        public PrintBundleOptions? BundleOptions { get; set; }
        public string Username { get; set; } = string.Empty;
        public DateTimeOffset? ReleaseDateTime { get; set; }

        public string? GordNumber { get; set; }
        public int? TaskNumber { get; set; }
    }
}
