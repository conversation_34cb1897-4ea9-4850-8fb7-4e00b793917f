﻿using MassTransit;
using Pondres.Omnia.Print.Business.Activities.Bundle;
using Pondres.Omnia.Print.Business.Extensions;
using Pondres.Omnia.Print.Business.StateMachine.PrintBundle.Model;
using System;

namespace Pondres.Omnia.Print.Business.StateMachine.PrintBundle;

public static class PrintBundleStateMachineExtensions
{
    public static EventActivityBinder<PrintBundleState, TData> ConfirmAndCompleteBundle<TData>(
        this EventActivityBinder<PrintBundleState, TData> source,
        PrintBundleStateMachine stateMachine,
        Func<TData, string> getUserNameFunc)
        where TData : class
    {
        return source
            .TransitionTo(stateMachine.Completed)
            .Activity(x => x.OfInstanceType<PrintBundlePublishDocumentStateEventsActivity>())
            .Publish(context => context.Saga.ToCompletedConfirmedEvent(getUserNameFunc(context.Message)))
            .Activity(x => x.OfInstanceType<PrintBundleUpdateStatusActivity>())
            .Publish(context => context.Saga.ToPrintBundleCompleted(getUserNameFunc(context.Message)))
            .FinishBundle();

    }

    public static EventActivityBinder<PrintBundleState, TData> SelectDocumentsAndStartProcessing<TData>(
        this EventActivityBinder<PrintBundleState, TData> source,
        PrintBundleStateMachine stateMachine)
        where TData : class
    {
        return source
                .Unschedule(stateMachine.StartTimeout)
                .TransitionTo(stateMachine.Started)
                .Activity(x => x.OfInstanceType<PrintBundleStartActivity>())
                .Activity(x => x.OfInstanceType<PrintBundleSelectDocumentsActivity>())
                .Publish(x => x.Saga.ToPrintBundleStarted())
                .IfElse(PrintBundleStateMachine.HasDocumentsToImposition,
                    canStartImpositioning => canStartImpositioning
                        .TransitionTo(stateMachine.WaitingForBatch)
                        .Publish(context => context.Saga.ToBundleBatchAssignRequest())
                        .Activity(x => x.OfInstanceType<PrintBundleUpdateStatusActivity>()),
                    noDocumentsToImposition => noDocumentsToImposition
                        .Publish(context => context.Saga.ToPrintBundleSkipped())
                        .TransitionTo(stateMachine.Skipped)
                        .Activity(x => x.OfInstanceType<PrintBundleUpdateStatusActivity>())
                        .FinishBundle());
    }

    public static EventActivityBinder<PrintBundleState, TData> FinishBundle<TData>(
        this EventActivityBinder<PrintBundleState, TData> source)
        where TData : class
    {
        return source
            .Activity(x => x.OfInstanceType<PrintBundleExpireDocumentsActivity>())
            .Finalize();
    }
    public static EventActivityBinder<PrintBundleState, TData> StartImpositioning<TData>(
        this EventActivityBinder<PrintBundleState, TData> source,
        PrintBundleStateMachine stateMachine)
        where TData : class
    {
        return source
            .Activity(x => x.OfInstanceType<PrintBundleSendToImpositioningActivity>())
            .TransitionTo(stateMachine.Impositioning)
            .Activity(x => x.OfInstanceType<PrintBundleUpdateStatusActivity>());
    }
}