﻿using MassTransit;
using Pondres.Omnia.Print.Business.Handler;
using Pondres.Omnia.Print.Business.Service;
using Pondres.Omnia.Print.Business.StateMachine.PrintBundle;
using Pondres.Omnia.Print.Business.StateMachine.PrintBundle.Model;
using Pondres.Omnia.Print.Storage.Repositories;
using System;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Activities.Bundle;
public class PrintBundleUpdateStatusActivity(
    IPrintBundleEventService printBundleEventService,
    IPrintBatchNotificationHandler printBatchNotificationHandler,
    IPrintBundleEventHistoryUpdateHandler printBundleEventHistoryUpdateHandler) : BaseInstanceActivity<PrintBundleState>
{
    public override string ActivityName => nameof(PrintBundleUpdateStatusActivity);

    protected override async Task ExecuteAsync(BehaviorContext<PrintBundleState> context)
    {
        var updateMessage = new Contracts.PrintBundle.PrintBundleStatusUpdate()
        {
            Timestamp = DateTimeOffset.Now,
            Message = CreateStatusUpdateMessage(context.Saga),
            Status = context.Saga.CurrentState,
            PrintBundleId = context.Saga.CorrelationId,
            WaitingForInput = IsWaitingForInput(context.Saga),
            IsInFailedState = IsInFailedState(context.Saga),
            BatchName = context.Saga.BatchName,
            Customer = context.Saga.Customer
        };

        await printBundleEventHistoryUpdateHandler.RegisterStatusUpdateEventAsync(updateMessage);

        await printBundleEventService.UpdateStatusAsync(updateMessage);

        if (context.Saga.CurrentState == nameof(PrintBundleStateMachine.WaitingForPrint))
            await printBatchNotificationHandler.PublishPrintBatchReadyAsync(
                context: context,
                batchName: context.Saga.BatchName,
                customer: context.Saga.Customer);

        if (context.Saga.CurrentState == nameof(PrintBundleStateMachine.WaitingForStart))
            await printBundleEventService.RegisterWaitingForStartEventAsync(
                printBundleId: context.Saga.CorrelationId,
                timestamp: DateTimeOffset.Now,
                stateType: Contracts.Common.PrintBundleStateType.NotStarted);

        if (context.Saga.CurrentState == nameof(PrintBundleStateMachine.Cancelled))
            await printBundleEventService.RegisterBundleCancellationAsync(
                new Contracts.PrintBundle.PrintBundleCancelled
                {
                    BatchName = context.Saga.BatchName,
                    Customer = context.Saga.Customer,
                    PrintBundleId = context.Saga.CorrelationId,
                    Timestamp = DateTimeOffset.Now,
                    Message = "Cancelled",
                    Status = context.Saga.CurrentState 
                });
    }

    private static bool IsInFailedState(PrintBundleState saga)
        => saga.CurrentState switch
        {
            nameof(PrintBundleStateMachine.BatchCreationFailure) => true,
            nameof(PrintBundleStateMachine.ImpositioningOnHold) => true,
            _ => false
        };

    private static bool IsWaitingForInput(PrintBundleState saga)
        => saga.CurrentState switch
        {
            nameof(PrintBundleStateMachine.BatchCreationFailure) => true,
            nameof(PrintBundleStateMachine.ImpositioningOnHold) => true,
            nameof(PrintBundleStateMachine.WaitingForPrint) => true,
            nameof(PrintBundleStateMachine.WaitingForScan) => true,
            _ => false
        };

    private static string CreateStatusUpdateMessage(PrintBundleState saga)
        => saga.CurrentState switch
        {
            nameof(PrintBundleStateMachine.Initial) => "Bundle initiated",
            nameof(PrintBundleStateMachine.Created) => "Bundle created",
            nameof(PrintBundleStateMachine.WaitingForStart) => "Waiting for start signal",
            nameof(PrintBundleStateMachine.BatchCreationFailure) => "Batch creation failed",
            nameof(PrintBundleStateMachine.WaitingForBatch) => "Waiting for batch creation",
            nameof(PrintBundleStateMachine.Impositioning) => $"Waiting for impositioning to complete",
            nameof(PrintBundleStateMachine.ImpositioningOnHold) => $"ONHOLD: Impositioning failure",
            nameof(PrintBundleStateMachine.WaitingForPrint) => $"Waiting for print confirmation",
            nameof(PrintBundleStateMachine.Printed) => $"Print confirmed",
            nameof(PrintBundleStateMachine.Completed) => $"Complete confirmed",
            nameof(PrintBundleStateMachine.Skipped) => $"Skipped/Empty bundle",
            nameof(PrintBundleStateMachine.Final) => $"Final",
            nameof(PrintBundleStateMachine.Cancelled) => "Cancelled",
            nameof(PrintBundleStateMachine.WaitingForScan) => $"Waiting for scan",
            nameof(PrintBundleStateMachine.Scanned) => $"Scanned",
            _ => $"Unknown status {saga.CurrentState}, please update code",
        };

}
