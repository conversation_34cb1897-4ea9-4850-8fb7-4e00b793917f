﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Cosmos;
using Microsoft.Azure.Cosmos.Linq;
using Microsoft.Extensions.Caching.Memory;
using Solude.StorageBase;
using Pondres.Omnia.Print.Contracts.Api.Bundle;
using Pondres.Omnia.Print.Contracts.Common;
using Pondres.Omnia.Print.Storage.Configuration;
using Pondres.Omnia.Print.Storage.Entities.PrintBundle;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Solude.StorageBase.Model;

namespace Pondres.Omnia.Print.Storage.Repositories;

public class PrintBundleDetailsRepository : SinglePartitionBaseRepository<PrintBundleDetailsEntity>, IPrintBundleDetailsRepository
{

    protected override bool CanWrite => true;

    protected override string PartitionKeyValue => CosmosConfiguration.PrintBundlePartitionValue;

    protected override MemoryCacheEntryOptions CreateCacheOptions() =>
        new()
        {
            SlidingExpiration = TimeSpan.FromMinutes(1)
        };

    public PrintBundleDetailsRepository(CosmosClient cosmosClient, IMemoryCache memoryCache) :
        base(cosmosClient, CosmosConfiguration.PrintDatabaseName, CosmosConfiguration.PrintBundleContainerName, memoryCache)
    {
    }

    public async Task<List<PrintBundleDetailsEntity>> GetBundlesWithExcludedLocationAsync(
        IEnumerable<string> printBundleIds,
        string[] excludedCustomers,
        IEnumerable<string> excludedEndLocations)
    {
        var query = container.GetItemLinqQueryable<PrintBundleDetailsEntity>()
            .Where(x =>
                printBundleIds.Contains(x.Id) &&
                excludedEndLocations.Contains(x.Metadata.EndLocation) &&
                x.Metadata.DGCode != null && x.Metadata.DGCode != ""
                );

        if (excludedCustomers.Length > 0)
            query = query.Where(x => !excludedCustomers.Contains(x.Customer));

        var queryResults = query.OrderByDescending(x => x.CreatedOn);

        var result = await GetEntityListAsync(queryResults, null, printBundleIds.Count());

        return result.Entities;
    }

    public async Task<PrintBundleDetailsEntity?> GetPlannedBundleAsync(string bundleHash, DateTimeOffset? plannedOn, string? primaryBundleId)
    {
        var query = container.GetItemLinqQueryable<PrintBundleDetailsEntity>()
            .Where(x =>
                x.ReleasedOn == null &&
                x.PlannedOn == plannedOn &&
                x.Hash == bundleHash &&
                x.PrimaryBundleId == primaryBundleId &&
                x.StateName != PrintBundleStateType.Completed.ToString())
            .OrderBy(x => x.CreatedOn);

        string? continuationToken = null;

        using var feedIterator = container.GetItemQueryIterator<PrintBundleDetailsEntity>(query.ToQueryDefinition(), continuationToken, new QueryRequestOptions
        {
            MaxItemCount = 1
        });

        PrintBundleDetailsEntity? entity = null;

        while (feedIterator.HasMoreResults)
        {
            var result = await feedIterator.ReadNextAsync();
            entity = result.Resource.SingleOrDefault();
        }

        return entity;
    }

    public async Task<ListResult<PrintBundleDetailsEntity>> GetByFilterAsync(PrintBundleDefaultFilter filter)
    {
        var query = BuildQueryByFilter(filter);

        return await GetEntityListAsync(query, filter.ContinuationToken, filter.MaxPageSize);
    }

    protected override string GetCacheKey(string id, string partitionKeyValue) => $"pbundledetails_{id}_{partitionKeyValue}";

    private IOrderedQueryable<PrintBundleDetailsEntity> BuildQueryByFilter(PrintBundleDefaultFilter filter)
    {
        var query = container.GetItemLinqQueryable<PrintBundleDetailsEntity>()
            .Where(x => !filter.State.HasValue || x.StateName == filter.State.ToString());

        query = filter.IncludeEmptyBundles ? query : query.Where(x => x.Quantities.DocumentCount > 0);

        query = !string.IsNullOrWhiteSpace(filter.BundleId) ? query.Where(x => x.Id == filter.BundleId) : query;
        query = !string.IsNullOrWhiteSpace(filter.Customer) ? query.Where(x => x.Customer == filter.Customer) : query;
        query = !string.IsNullOrWhiteSpace(filter.BatchName) ? query.Where(x => x.BatchName == filter.BatchName) : query;
        query = !string.IsNullOrWhiteSpace(filter.DGCode) ? query.Where(x => x.Metadata.DGCode == filter.DGCode) : query;
        query = !string.IsNullOrWhiteSpace(filter.GordNumber) ? query.Where(x => x.GordNumber == filter.GordNumber) : query;
        query = filter.PrinterType is not null ? query.Where(x => x.Metadata.PrinterType == filter.PrinterType.ToString()) : query;
        query = !string.IsNullOrWhiteSpace(filter.Carrier) ? query.Where(x => x.Metadata.Carrier == filter.Carrier) : query;
        query = !string.IsNullOrWhiteSpace(filter.PrintMode) ? query.Where(x => x.Metadata.PrintMode == filter.PrintMode) : query;
        query = filter.CreatedOnFrom.HasValue ? query.Where(x => x.CreatedOn >= filter.CreatedOnFrom) : query;
        query = filter.CreatedOnTo.HasValue ? query.Where(x => x.CreatedOn <= filter.CreatedOnTo) : query;

        query = filter.MailDateFrom.HasValue ? query.Where(x => x.Metadata.MailDate >= filter.MailDateFrom) : query;
        query = filter.MailDateTo.HasValue ? query.Where(x => x.Metadata.MailDate <= filter.MailDateTo) : query;
        query = filter.IsDirectlyCompleted.HasValue ? query.Where(c => c.IsDirectlyCompleted == filter.IsDirectlyCompleted.Value) : query;

        if (!filter.IncludeHiddenCustomers)
        {
            CustomerConstants.TestCustomers.ToList()
                .ForEach(excludedCustomer => query = query.Where(x => !x.Customer.Equals(excludedCustomer)));
        }

        return query.OrderByDescending(x => x.Metadata.MailDate);
    }

    public async Task<ListResult<PrintBundleDetailsEntity>> GetByBundleBatchFilterAsync(PrintBundleBatchFilter filter)
    {
        var query = BuildQueryByBundleBatchFilter(filter);

        return await GetEntityListAsync(query, filter.ContinuationToken, filter.MaxPageSize);
    }

    private IOrderedQueryable<PrintBundleDetailsEntity> BuildQueryByBundleBatchFilter(PrintBundleBatchFilter filter)
    {
        var query = container.GetItemLinqQueryable<PrintBundleDetailsEntity>()
            .Where(x => !filter.State.HasValue || x.StateName == filter.State.ToString());

        query = filter.PrintBundleIds.Count != 0 ? query.Where(x => filter.PrintBundleIds.Contains(x.Id)) : query;

        query = filter.BatchNames.Count != 0 ? query.Where(x => filter.BatchNames.Contains(x.BatchName)) : query;

        return query.OrderByDescending(x => x.Metadata.MailDate);
    }

    private IOrderedQueryable<PrintBundleDetailsEntity> BuildQueryByPrintBundleIds(IEnumerable<string> printBundleIds)
    {
        return container.GetItemLinqQueryable<PrintBundleDetailsEntity>()
            .Where(x => printBundleIds.Contains(x.Id))
            .OrderByDescending(x => x.Metadata.MailDate);
    }

    public async Task<ListResult<PrintBundleDetailsEntity>> GetByPrintBundleIdsAsync(IEnumerable<string> printBundleIds)
    {
        var query = BuildQueryByPrintBundleIds(printBundleIds);

        return await GetEntityListAsync(query, null, printBundleIds.Count());
    }
}