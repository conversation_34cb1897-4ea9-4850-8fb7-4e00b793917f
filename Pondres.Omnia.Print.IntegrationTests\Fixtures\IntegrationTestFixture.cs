﻿using DotNet.Testcontainers.Builders;
using DotNet.Testcontainers.Networks;
using Microsoft.Extensions.Configuration;
using Pondres.Omnia.Print.IntegrationTest.Services;
using Pondres.Omnia.Print.IntegrationTests.Fixtures.Integration;
using Serilog;

namespace Pondres.Omnia.Print.IntegrationTests.Fixtures;

[CollectionDefinition("IntegrationTests")]
public class IntegrationTestCollection : ICollectionFixture<IntegrationTestFixture>
{
    // This class has no code, and is never created. Its purpose is simply
    // to be the place to apply [CollectionDefinition] and all the
    // ICollectionFixture<> interfaces.
}

public class IntegrationTestFixture : IAsyncLifetime, ITestDependenciesProvider
{
    public INetwork Network { get; }

    public BlobFixture Blobs { get; private set; } = default!;
    public OrderHubIntegrationFixture OrderHubIntegration { get; private set; } = default!;
    public ScalerIntegrationFixture ScalerIntegration { get; private set; } = default!;

    public ContainersFixture Containers { get; }

    public ManagerAppFixture ManagerApp { get; }
    public WorkerAppFixture WorkerApp { get; }

    public IntegrationTestFixture()
    {
        Network = new NetworkBuilder()
            .WithName($"testcontainers-network-{Guid.NewGuid()}")
            .Build();

        var configuration = new ConfigurationBuilder().AddUserSecrets<ManagerAppFixture>().AddEnvironmentVariables().Build();
        var cosmosDbEndpoint = configuration["CosmosDbEndpoint"] ?? string.Empty;

        if (string.IsNullOrWhiteSpace(cosmosDbEndpoint))
            throw new InvalidOperationException("CosmosDbEndpoint is not set, add a CosmosDbEndpoint entry in the user secrets of the integration project, or if on build agent put it in the environment settings");

        Containers = new ContainersFixture();

        ManagerApp = new ManagerAppFixture(this, cosmosDbEndpoint, Containers);
        WorkerApp = new WorkerAppFixture(cosmosDbEndpoint, Containers);

        // Nice to have all the logging of the service during testing
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Warning()
            .WriteTo.Console()
            .WriteTo.File("log.txt")
            .CreateLogger();
    }

    public async Task DisposeAsync()
    {
        await Containers.DisposeAllAsync();

        await Network.DisposeAsync();

        await Log.CloseAndFlushAsync();
    }

    public async Task InitializeAsync()
    {
        await Containers.StartAllAsync();

        Blobs = new BlobFixture(Containers.Azurite.GetConnectionString());
        OrderHubIntegration = new OrderHubIntegrationFixture(this, Blobs);
        ScalerIntegration = new ScalerIntegrationFixture(this);

        await ManagerApp.InitializeAsync();
        await WorkerApp.InitializeAsync();
    }

    public TServiceType ResolveManagerService<TServiceType>() where TServiceType : class => ManagerApp.ResolveService<TServiceType>();

    public TServiceType ResolveWorkerService<TServiceType>() where TServiceType : class => WorkerApp.ResolveService<TServiceType>();
}
