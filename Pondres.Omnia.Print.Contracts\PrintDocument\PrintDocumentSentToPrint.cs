﻿using System;
using Pondres.Omnia.Print.Contracts.Common;

namespace Pondres.Omnia.Print.Contracts.PrintDocument
{
    public class PrintDocumentSentToPrint
    {
        public OrderMetadata OrderMetadata { get; set; } = new();
        public PrintDocumentMetadata DocumentMetadata { get; set; } = new();
        public DateTimeOffset Timestamp { get; set; }
        public int SequenceId { get; set; }
    }
}