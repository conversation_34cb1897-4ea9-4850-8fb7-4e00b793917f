﻿using System;
using System.Net.Http;
using System.Threading.Tasks;
using System.Threading;

namespace Pondres.Omnia.Print.Storage.Handler;

/// <summary>
/// This thing exists to forward all requests to the emulator port number.
/// For some reason the sdk tries to reach (seemingly) arbitrary ports even though it should only be the port given in the connection string.
/// </summary>
public class CosmosDevelopmentHttpMessageHandler : DelegatingHandler
{
    private readonly int portNumber;
    private readonly string host;

    public CosmosDevelopmentHttpMessageHandler(string endpoint, HttpMessageHandler innerHandler)
        : base(innerHandler)
    {
        // Should parse something that looks like this: AccountEndpoint=https://{Host}:{Port}/;AccountKey=<key>;
        var hostAndPortParts = endpoint.Split("/;")[0].Replace("AccountEndpoint=https://", "").Split(":");
        portNumber = int.Parse(hostAndPortParts[1]);
        host = hostAndPortParts[0];
    }

    /// <summary>
    /// Override of the SendAsync method to allow for reconstruction of the request uri to point to the dynamic testcontainer
    /// port number. This needs to be done as otherwise it defaults back to 8081. I assume there is some hard coded port in
    /// the emulator somewhere. If this is not done then the requests are not successful.
    /// </summary>
    /// <param name="request"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(request.RequestUri);

        request.RequestUri = new Uri($"https://{host}:{portNumber}{request.RequestUri.PathAndQuery}");
        var response = await base.SendAsync(request, cancellationToken);
        return response;
    }
}