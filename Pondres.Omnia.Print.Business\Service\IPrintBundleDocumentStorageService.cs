﻿using Pondres.Omnia.Print.Business.Model;
using Pondres.Omnia.Print.Contracts.Common;
using Pondres.Omnia.Print.Contracts.PrintBundle;
using Pondres.Omnia.Print.Storage.PrintBundle;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Service;

public interface IPrintBundleDocumentStorageService
{
    Task<AddDocumentsToBundleResult> AddDocumentsToBundleAsync(Guid bundleId, List<PrintBundleQueuedDocument> documents, bool forceAddIfStarted = false);
    Task<HashSet<PrintBundleQueuedDocument>> GetDocumentsForBundleAsync(Guid bundleId);
    Task UpdateDocumentCollectionAsync(Guid bundleId, Action<PrintBundleDocumentCollection> updateFunc);
    Task<PrintBundleDocumentCollection> RequeueFalloffDocumentsAndMarkAsStartedAsync(Guid bundleId, PrintBundleMode bundleMode, string? printMode, int sheetPageCount);
    Task<DocumentRemovalResult> RemoveDocumentFromBundleAsync(Guid bundleId, string documentId, bool isPrimaryBundle);
    void ExpireDocumentsForBundle(Guid bundleId);
}