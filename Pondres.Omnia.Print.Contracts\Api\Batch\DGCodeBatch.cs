﻿using Pondres.Omnia.Print.Contracts.Api.Bundle;
using System.Collections.Generic;

namespace Pondres.Omnia.Print.Contracts.Api.Batch
{
    public class DGCodeBatch
    {
        public string Customer { get; set; } = string.Empty;
        public string? GordNumber { get; set; }
        public string? DGCode { get; set; }
        public string BatchName { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public List<DGCodeBatchDetails> Details { get; set; } = new();
        public List<PrintBundleListItem> PrintBundles { get; set; } = new();
    }
}
