﻿using Pondres.Omnia.Print.Contracts.Api.Bundle;
using Pondres.Omnia.Print.Storage.Entities.PrintBundle;
using System.Collections.Generic;
using System.Linq;

namespace Pondres.Omnia.Print.Business.Extensions;

public static class PrintBundleManualEventEntityExtensions
{
    public static IEnumerable<PrintBundleManualEvent> ToDto(this IEnumerable<PrintBundleManualEventEntity> entities) => 
        entities.Select(x => x.ToDto());

    public static PrintBundleManualEvent ToDto(this PrintBundleManualEventEntity entity) => 
        new()
        {
            Event = entity.Event,
            Timestamp = entity.Timestamp,
            Username = entity.Username
        };
}