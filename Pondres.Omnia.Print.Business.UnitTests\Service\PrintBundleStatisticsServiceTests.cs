﻿using Moq;
using Moq.AutoMock;
using Pondres.Omnia.Print.Business.Service;
using Pondres.Omnia.Print.Contracts.Api.Bundle;
using Pondres.Omnia.Print.Storage.Entities.PrintBundle;
using Pondres.Omnia.Print.Storage.Entities.Print;
using Pondres.Omnia.Print.Storage.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using Pondres.Omnia.Print.Business.Extensions;
using System.Threading.Tasks;
using Xunit;
using FluentAssertions;
using Pondres.Omnia.Print.Contracts.PrintBundle;

namespace Pondres.Omnia.Print.Business.UnitTests.Service;

public class PrintBundleStatisticsServiceTests
{
    private readonly AutoMocker mocker;
    private readonly PrintBundleStatisticsService instance;

    public PrintBundleStatisticsServiceTests()
    {
        mocker = new AutoMocker(MockBehavior.Strict);

        instance = mocker.CreateInstance<PrintBundleStatisticsService>();
    }

    [Fact]
    public async Task GetTotalsForDateRangeAsync_GroupsDataCorrectly()
    {
        // Arrange
        var filter = new PrintBundleDateRangeFilter();
        var now = DateTime.Now;
        var weekNumber = now.ToIso8601WeekOfYear();

        var entities = new List<PrintBundleBasicEntity>
        {
            new PrintBundleBasicEntity
            {
                CreatedOn = now,
                Quantities = new PrintBundleQuantitiesEntity
                {
                    DocumentCount = 10,
                    PageCount = 10,
                },
                Metadata = new PrintDocumentMetadataEntity
                {
                    SheetPageCount = 2,
                    PageCount = 10,
                    PrintMode = PrintBundlePrintMode.Simplex
                }
            },
            new PrintBundleBasicEntity
            {
                CreatedOn = now,
                Quantities = new PrintBundleQuantitiesEntity
                {
                    DocumentCount = 20,
                    PageCount = 20,
                },
                Metadata = new PrintDocumentMetadataEntity
                {
                    SheetPageCount = 10,
                    PageCount = 20,
                    PrintMode = PrintBundlePrintMode.Simplex
                }
            }
        };

        mocker.GetMock<IPrintBundleBasicRepository>()
            .Setup(x => x.GetAllForDateRangeAsync(filter))
            .ReturnsAsync(entities);

        // Act
        var result = await instance.GetTotalsForDateRangeAsync(filter);

        // Assert
        result.TotalsPerWeek.Should().ContainKey(weekNumber);

        result.TotalsPerWeek[weekNumber].DocumentCount.Should().Be(30);
        result.TotalsPerWeek[weekNumber].SheetCount.Should().Be(7);

        result.TotalsPerHour[now.Hour].DocumentCount.Should().Be(30);
        result.TotalsPerHour[now.Hour].SheetCount.Should().Be(7);

        result.Days.Should().HaveCount(1);

        var dayResult = result.Days.Single();
        dayResult.Hours.Should().HaveCount(1);
        dayResult.Totals.DocumentCount.Should().Be(30);
        dayResult.Totals.SheetCount.Should().Be(7);

        dayResult.Hours.Should().ContainKey(now.Hour);
        var hourResult = dayResult.Hours[now.Hour];
        hourResult.DocumentCount.Should().Be(30);
        hourResult.SheetCount.Should().Be(7);
    }
}
