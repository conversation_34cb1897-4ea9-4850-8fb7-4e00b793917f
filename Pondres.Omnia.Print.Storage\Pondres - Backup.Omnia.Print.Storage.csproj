﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>annotations</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Identity" Version="1.10.4" />
    <PackageReference Include="Azure.Storage.Blobs" Version="12.19.1" />
    <PackageReference Include="Microsoft.Azure.Cosmos" Version="3.36.0" />
    <PackageReference Include="Microsoft.Extensions.Caching.Abstractions" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="7.0.0" />
    <PackageReference Include="Pondres.Cosmos" Version="3.20221025.2" />
    <PackageReference Include="Serilog" Version="3.1.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Pondres.Omnia.Print.Common\Pondres.Omnia.Print.Common.csproj" />
    <ProjectReference Include="..\Pondres.Omnia.Print.Contracts\Pondres.Omnia.Print.Contracts.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Handler\" />
  </ItemGroup>

</Project>
