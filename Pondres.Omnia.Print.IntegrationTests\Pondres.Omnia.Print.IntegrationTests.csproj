﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<IsPackable>false</IsPackable>
		<IsTestProject>true</IsTestProject>
		<UserSecretsId>fbf715bb-af80-4aa2-9147-f3fcf3fe3a49</UserSecretsId>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="FluentAssertions" Version="[7.0.0]" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="9.0.5" />
		<PackageReference Include="Microsoft.Extensions.ApiDescription.Client" Version="9.0.5">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />
		<PackageReference Include="NSwag.ApiDescription.Client" Version="14.4.0">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Serilog.Extensions.Logging" Version="9.0.2" />
		<PackageReference Include="Testcontainers.Azurite" Version="4.5.0" />
		<PackageReference Include="Testcontainers.CosmosDb" Version="4.5.0" />
		<PackageReference Include="Testcontainers.Redis" Version="4.5.0" />
		<PackageReference Include="xunit" Version="2.9.3" />
		<PackageReference Include="xunit.runner.visualstudio" Version="3.1.1">
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
			<PrivateAssets>all</PrivateAssets>
		</PackageReference>
		<PackageReference Include="coverlet.collector" Version="6.0.4">
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
			<PrivateAssets>all</PrivateAssets>
		</PackageReference>
		<PackageReference Include="Testcontainers" Version="4.5.0" />
		<PackageReference Include="Testcontainers.RabbitMq" Version="4.5.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Pondres.Omnia.Print.PrintManager\Pondres.Omnia.Print.PrintManager.csproj" />
		<ProjectReference Include="..\Pondres.Omnia.Print.PrintWorker\Pondres.Omnia.Print.PrintWorker.csproj" />
	</ItemGroup>

	<ItemGroup>
		<OpenApiReference Include="OpenAPIs\print_swagger.json" CodeGenerator="NSwagCSharp" Namespace="Pondres.Omnia.Print.IntegrationTests.PrintClient" ClassName="PrintManagerClient">
			<SourceUri>https://api-omnia-test.pondres.nl/print/swagger/v1/swagger.json</SourceUri>
			<Options>/ExcludedParameterNames:x-token,x-customer,x-tenant /OperationGenerationMode:SingleClientFromPathSegments</Options>
		</OpenApiReference>
	</ItemGroup>

	<ItemGroup>
		<Folder Include="OpenAPIs\" />
	</ItemGroup>

</Project>
