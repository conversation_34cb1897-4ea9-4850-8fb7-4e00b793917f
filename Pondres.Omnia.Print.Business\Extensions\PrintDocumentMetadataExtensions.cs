﻿using Pondres.Omnia.Print.Contracts.Api.Enums;
using Pondres.Omnia.Print.Contracts.Common;
using Pondres.Omnia.Print.Storage.Entities.Print;
using System.Linq;
using System.Security.Cryptography;
using System.Text;

namespace Pondres.Omnia.Print.Business.Extensions;

public static class PrintDocumentMetadataExtensions
{
    public static PrintDocumentCreationMetadata ToPrintDocumentCreationMetadata(this PrintDocumentMetadataEntity entity,
        int quantity = 0,
        string? customerDocumentReference = null)
    {
        return new PrintDocumentCreationMetadata
        {
            MailDate = entity.MailDate,
            Carrier = entity.Carrier,
            PostalDestination = entity.PostalDestination,
            PrinterType = entity.PrinterType.ToEnumOrDefault(PrinterType.FC),
            DocumentFormat = entity.DocumentFormat,
            SheetArticleCode = entity.SheetArticleCode,
            Quantity = quantity,
            CustomerDocumentReference = customerDocumentReference,
            EndLocation = entity.EndLocation,
            SheetFormat = entity.SheetFormat,
            PrintMode = entity.PrintMode,
            Laminate = entity.Laminate,
            SheetPageCount = entity.SheetPageCount,
            DGCode = entity.DGCode,
            PageCount = entity.PageCount,
            Packaging = entity.Packaging,
            PackingType = entity.PackingType ?? PackingType.Overig,
            PackingName = entity.PackingName,
            ReleaseSchedule = entity.ReleaseSchedule,
            SheetArticleDescription = entity.SheetArticleDescription,
            Attachments = entity.Attachments.ToPrintDocumentCreationMetadataAttachments(),
            IsPrimary = entity.IsPrimary,
            PrintBundleSort = entity.PrintBundleSort
        };
    }

    public static PrintDocumentMetadataEntity ToMetadataEntity(this PrintDocumentCreationMetadata metadata) =>
        new()
        {
            MailDate = metadata.MailDate,
            Carrier = metadata.Carrier,
            DocumentFormat = metadata.DocumentFormat,
            Laminate = metadata.Laminate,
            PrintMode = metadata.PrintMode,
            PostalDestination = metadata.PostalDestination,
            PrinterType = metadata.PrinterType?.ToString() ?? PrinterType.FC.ToString(),
            SheetArticleCode = metadata.SheetArticleCode,
            SheetPageCount = metadata.SheetPageCount,
            SheetFormat = metadata.SheetFormat,
            EndLocation = metadata.EndLocation,
            DGCode = metadata.DGCode,
            PageCount = metadata.PageCount,
            Packaging = metadata.Packaging,
            PackingType = metadata.PackingType,
            PackingName = metadata.PackingName,
            IsPrimary = metadata.IsPrimary,
            ReleaseSchedule = metadata.ReleaseSchedule,
            SheetArticleDescription = metadata.SheetArticleDescription,
            Attachments = metadata.Attachments.ToPrintDocumentMetadataAttachmentEntities(),
            PrintBundleSort = metadata.PrintBundleSort
        };

    public static string ToHash(this PrintDocumentCreationMetadata metadata, PrintDocumentCreationMetadata[] relatedMetadataCollection)
    {
        using var sha256 = SHA256.Create();
        var concatenatedMetadata = metadata.ConcatenateGroupPropertiesForHash(relatedMetadataCollection);

        var hashValue = sha256.ComputeHash(Encoding.UTF8.GetBytes(concatenatedMetadata));

        return string.Concat(hashValue.Select(x => x.ToString("X2"))).ToLower();
    }

    private static string ConcatenateGroupPropertiesForHash(
        this PrintDocumentCreationMetadata printMetaData,
        PrintDocumentCreationMetadata[] relatedMetadataCollection)
    {
        var sb = new StringBuilder();
        sb.Append(printMetaData.Carrier);
        sb.Append(printMetaData.DocumentFormat);
        sb.Append(printMetaData.Laminate);
        sb.Append(printMetaData.MailDate);
        sb.Append(printMetaData.PostalDestination);
        sb.Append(printMetaData.PrinterType);
        sb.Append(printMetaData.PrintMode);
        sb.Append(printMetaData.SheetArticleCode);
        sb.Append(printMetaData.SheetFormat);
        sb.Append(printMetaData.EndLocation);
        sb.Append(printMetaData.DGCode);
        sb.Append(printMetaData.Packaging);
        sb.Append(printMetaData.ReleaseSchedule);

        printMetaData.Attachments.OrderBy(x => x.ArticleCode).ToList().ForEach(y => sb.Append(y.ArticleCode));

        foreach (var relatedMetadata in relatedMetadataCollection)
        {
            sb.Append(relatedMetadata.PrintMode);
            sb.Append(relatedMetadata.SheetArticleCode);
            sb.Append(relatedMetadata.Laminate);
        }

        return sb.ToString();
    }
}