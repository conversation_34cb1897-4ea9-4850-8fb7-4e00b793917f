﻿using Pondres.Omnia.Print.Business.Model;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Handler.PrintBundleBuffer;

public class PrintBundleBuffer
{
    public TaskCompletionSource<PrintBundleBufferFlushResult> CompletionSource { get; private set; } = new();

    public List<PrintDocumentGroup> Groups { get; } = new(100);

    public SemaphoreSlim Lock { get; } = new(1, 1);

    public bool MarkedForDeletion { get; internal set; }

    public Guid BundleId { get; }

    public PrintBundleBuffer(Guid bundleId)
    {
        BundleId = bundleId;
    }

    public void AddDocuments(PrintDocumentGroup document)
    {
        Groups.Add(document);
        MarkedForDeletion = false;
    }

    public void Refresh()
    {
        Groups.Clear();
        CompletionSource = new();
    }

    public bool TryMarkForDeletion()
    {
        MarkedForDeletion = Groups.Count == 0;

        return MarkedForDeletion;
    }
}