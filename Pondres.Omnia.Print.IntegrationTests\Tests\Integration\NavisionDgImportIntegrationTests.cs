﻿using FluentAssertions;
using Pondres.Omnia.OrderHub.IntegrationTests.Tests;
using Pondres.Omnia.Print.Business.Handler.Navision;
using Pondres.Omnia.Print.Business.Helper;
using Pondres.Omnia.Print.Integrations.Navision.Mappings;
using Pondres.Omnia.Print.IntegrationTests.Fixtures;
using Pondres.Omnia.Print.IntegrationTests.Fixtures.Integration;
using Pondres.Omnia.Print.PrintManager.Services;
using System.Globalization;

namespace Pondres.Omnia.Print.IntegrationTests.Tests.Integration;
[Collection("IntegrationTests")]
public class NavisionDgImportIntegrationTests : BaseIntegrationTest
{
    public NavisionDgImportIntegrationTests(IntegrationTestFixture fixture) : base(fixture)
    {
    }

    [Fact]
    public async Task GetExcludedLocationBundlesForTimeRangeAsync_HasBundleInTimeRange_ReturnsBundle()
    {
        // Arrange
        var testStart = DateTimeOffset.Now;
        var customer = GenerateCustomer();
        var releaseSchedule = "Di-Do_10.00";

        var creationMetadata = OrderHubIntegrationFixture.CreatePrintDocumentMetadata(releaseScheduleId: releaseSchedule);
        creationMetadata.EndLocation = NavisionDataMappings.ExcludedEndLocations[0];

        var bundle = await CreateAndCompletePrintBundleAsync(customer, creationMetadata);

        var bundleCompleted = DateTimeOffset.Now;

        var importHandler = fixture.ManagerApp.ResolveService<IDGImportHandler>();

        // Act
        var result = await importHandler.GetExcludedLocationBundlesForTimeRangeAsync(testStart, bundleCompleted);
        result.Should().HaveCountGreaterThanOrEqualTo(1);

        var bundleInResult = result.SingleOrDefault(x => x.Key.BundleIds.Contains(bundle.Id));
        bundleInResult.Should().NotBeNull();
    }

    [Fact]
    public async Task ExcludedLocationPeriodicDGImportService_HasBundlesInTimeRange_ExportsXml()
    {
        // Arrange
        var testStart = DateTimeOffset.Now;
        var customer = GenerateCustomer();
        var mailDate = DateTimeOffset.Now;

        var creationMetadata = OrderHubIntegrationFixture.CreatePrintDocumentMetadata(mailDate: mailDate);
        creationMetadata.EndLocation = NavisionDataMappings.ExcludedEndLocations[0];

        var bundle = await CreateAndCompletePrintBundleAsync(customer, creationMetadata);

        var redisCacheHelper = fixture.ManagerApp.ResolveService<IRedisCacheHelper>();
        await redisCacheHelper.GetOrSetAsync(ExcludedLocationPeriodicDGImportService.cacheKey, testStart.AddHours(-24).ToString(CultureInfo.CurrentCulture));

        var importService = fixture.ManagerApp.ResolveService<ExcludedLocationPeriodicDGImportService>();
        importService.RunTime = TimeSpan.Zero;

        var bundleDetails = await fixture.ManagerApp.BundleApi.GetDetailsAsync(Guid.Parse(bundle.Id));
        var dgCode = bundleDetails!.Metadata.DgCode;

        // Act
        await importService.StartAsync(CancellationToken.None);

        await WaitForAsync(() =>
        {
            // Get all files that start with the dgCode and end with .xml
            var files = Directory.GetFiles(".", $"{dgCode}*.xml");

            files.Should().HaveCount(1);

            return Task.CompletedTask;
        });

        await importService.StopAsync(CancellationToken.None);
    }
}
