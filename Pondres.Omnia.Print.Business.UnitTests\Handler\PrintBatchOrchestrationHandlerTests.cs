﻿using FluentAssertions;
using MassTransit;
using Moq;
using Moq.AutoMock;
using Solude.StorageBase.Model;
using Pondres.Omnia.Print.Business.Handler.PrintBatch;
using Pondres.Omnia.Print.Business.Helper;
using Pondres.Omnia.Print.Business.Model;
using Pondres.Omnia.Print.Contracts.Common;
using Pondres.Omnia.Print.Contracts.PrintBatch;
using Pondres.Omnia.Print.Contracts.PrintBundle;
using Pondres.Omnia.Print.Integrations.Navision.Model.Common;
using Pondres.Omnia.Print.Integrations.Navision.Provider;
using Pondres.Omnia.Print.Integrations.Navision.Service;
using Pondres.Omnia.Print.Navision.Model;
using Pondres.Omnia.Print.Storage.Entities.PrintBundle;
using Pondres.Omnia.Print.Storage.Entities.Print;
using Pondres.Omnia.Print.Storage.Repositories;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Xunit;
using Pondres.Omnia.Print.Contracts.Api.Enums;

namespace Pondres.Omnia.Print.Business.UnitTests.Handler;

public class PrintBatchOrchestrationHandlerTests
{
    private readonly AutoMocker mocker;
    private readonly Mock<IPrintBundleBasicRepository> printBundleRepository;
    private readonly Mock<INavisionDataProvider> navisionDataProvider;
    private readonly Mock<INavisionIntegrationService> navisionIntegrationService;
    private readonly Mock<IRedisCacheHelper> redisCacheHelper;
    private readonly Mock<IPrintBundleDetailsRepository> printBundleDetailsRepository;

    private readonly PrintBatchOrchestrationHandler handler;

    public PrintBatchOrchestrationHandlerTests()
    {
        mocker = new AutoMocker(MockBehavior.Strict);
        printBundleRepository = mocker.GetMock<IPrintBundleBasicRepository>();
        navisionDataProvider = mocker.GetMock<INavisionDataProvider>();
        navisionIntegrationService = mocker.GetMock<INavisionIntegrationService>();
        redisCacheHelper = mocker.GetMock<IRedisCacheHelper>();
        printBundleDetailsRepository = mocker.GetMock<IPrintBundleDetailsRepository>();

        handler = mocker.CreateInstance<PrintBatchOrchestrationHandler>();
    }

    [Fact]
    public async Task AddDataToBatchAsync_PrintBatchExists_UpdatesBatch_WithEmptyCache()
    {
        // Arrange
        var assignTaskRequest = new PrintBundleTaskAssignRequest
        {
            BatchName = "BatchName",
            PrintBundleId = Guid.NewGuid(),
            DGCode = "DGCode",
            Timestamp = DateTimeOffset.Now,
            DocumentCount = 1,
            SheetCount = 1,
        };

        var expiration = TimeSpan.FromMinutes(10);
        var navisionGordAndTaskNumber = new NavisionGordAndTaskNumberResponse { GORDNumber = "gordNumber", TaskNumber = 2 };

        var printMetadata = new PrintDocumentMetadataEntity
        {
            Carrier = "carrier",
            DocumentFormat = "documentFormat",
            EndLocation = "endLocation",
            Laminate = true,
            MailDate = DateTime.Today,
            DGCode = "DGCode",
            PostalDestination = "postalDestination",
            PrinterType = PrinterType.FC.ToString(),
            PrintMode = "printMode",
            SheetArticleCode = "sheetArticleCode",
            SheetPageCount = 1,
            SheetFormat = "sheetFormat"
        };

        var printBundleInfoForNavision = new PrintBundleBasicEntity
        {
            Id = assignTaskRequest.PrintBundleId.ToString(),
            BatchName = assignTaskRequest.BatchName,
            CreatedOn = DateTimeOffset.Now,
            Customer = "customer",
            Quantities = new PrintBundleQuantitiesEntity
            {
                DocumentCount = 1
            },
            Files = ["file1", "file2"],
            InitiatedBy = "Tester",
            GordNumber = navisionGordAndTaskNumber.GORDNumber,
            TaskNumber = navisionGordAndTaskNumber.TaskNumber + 1,
            Metadata = printMetadata,
            State = PrintBundleStateType.Active,
            StateName = PrintBundleStateType.Active.ToString()
        };

        redisCacheHelper
            .Setup(x => x.GetGordAndTaskNumberAsync(assignTaskRequest.DGCode))
            .ReturnsAsync((GordAndTaskNumber?)null);

        redisCacheHelper
            .Setup(x => x.SetGordAndTaskNumberAsync(assignTaskRequest.DGCode, navisionGordAndTaskNumber.GORDNumber, navisionGordAndTaskNumber.TaskNumber.Value + 1, expiration))
            .Returns(Task.CompletedTask);

        printBundleRepository
            .Setup(x => x.GetSingleAsync(It.IsAny<string>(), false))
            .ReturnsAsync(new CosmosResponse<PrintBundleBasicEntity>(string.Empty, printBundleInfoForNavision));

        SendBundleInfoToNavisionRequestBundle? sendRequest = null;
        navisionIntegrationService
            .Setup(x => x.SendPrintBundleXmlToNavision(It.IsAny<SendBundleInfoToNavisionRequestBundle>()))
            .Callback((SendBundleInfoToNavisionRequestBundle request) => sendRequest = request)
            .Verifiable();

        navisionDataProvider
            .Setup(x => x.GetGordAndTaskNumberAsync(It.Is<string>(x => x == assignTaskRequest.DGCode)))
            .ReturnsAsync(navisionGordAndTaskNumber);

        printBundleRepository.Setup(x => x.GetAllSecondaryPrintBundlesAsync(assignTaskRequest.PrintBundleId.ToString()))
            .ReturnsAsync([]);

        printBundleDetailsRepository
            .Setup(x => x.GetSingleAsync(assignTaskRequest.PrintBundleId.ToString(), false))
            .ReturnsAsync(new CosmosResponse<PrintBundleDetailsEntity>(string.Empty, new PrintBundleDetailsEntity { Id = assignTaskRequest.PrintBundleId.ToString() }));

        printBundleDetailsRepository
            .Setup(x => x.ReplaceConsistentAsync(It.IsAny<CosmosResponse<PrintBundleDetailsEntity>>(), It.IsAny<Func<PrintBundleDetailsEntity, bool>>()))
            .Returns(Task.CompletedTask);

        var assignNavisionMetadata = new List<PrintBundleAssignTaskCompleted>();
        mocker.GetMock<IPublishEndpoint>()
            .Setup(x => x.Publish(It.IsAny<PrintBundleAssignTaskCompleted>(), It.IsAny<CancellationToken>()))
            .Callback((PrintBundleAssignTaskCompleted x, CancellationToken y) => assignNavisionMetadata.Add(x))
            .Returns(Task.CompletedTask);

        // Act
        await handler.AddDataToBatchAsync(mocker.Get<IPublishEndpoint>(), assignTaskRequest);

        // Assert
        mocker.VerifyAll();

        sendRequest.Should().NotBeNull();

        sendRequest!.Primary.TaskNumber.Should().Be(printBundleInfoForNavision.TaskNumber);
        sendRequest!.Primary.GordNumber.Should().Be(printBundleInfoForNavision.GordNumber);
        sendRequest!.Primary.BatchName.Should().Be(assignTaskRequest.BatchName);
        sendRequest!.Primary.DGCode.Should().Be(assignTaskRequest.DGCode);
        sendRequest!.Primary.BundleMetadata.Should().Be(printMetadata);
        sendRequest!.Primary.DocumentCount.Should().Be(assignTaskRequest.DocumentCount);
        sendRequest!.Primary.SheetCount.Should().Be(assignTaskRequest.SheetCount);

        redisCacheHelper.Verify(x => x.GetGordAndTaskNumberAsync(It.IsAny<string>()), Times.Once());
        redisCacheHelper.Verify(x => x.SetGordAndTaskNumberAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<int>(), It.IsAny<TimeSpan>()), Times.Once());
    }

    [Fact]
    public async Task AddDataToBatchAsync_PrintBatchExists_UpdatesBatch_WithFilledCache()
    {
        // Arrange
        var assignTaskRequest = new PrintBundleTaskAssignRequest
        {
            BatchName = "BatchName",
            PrintBundleId = Guid.NewGuid(),
            DGCode = "DGCode",
            Timestamp = DateTimeOffset.Now,
            DocumentCount = 1,
            SheetCount = 1,
        };

        var expiration = TimeSpan.FromMinutes(10);
        var cachedGordAndTaskNumber = new GordAndTaskNumber { GordNumber = "gordNumber", TaskNumber = 2 };

        var printMetadata = new PrintDocumentMetadataEntity
        {
            Carrier = "carrier",
            DocumentFormat = "documentFormat",
            EndLocation = "endLocation",
            Laminate = true,
            MailDate = DateTime.Today,
            PostalDestination = "postalDestination",
            PrinterType = PrinterType.FC.ToString(),
            PrintMode = "printMode",
            SheetArticleCode = "sheetArticleCode",
            SheetPageCount = 1,
            SheetFormat = "sheetFormat"
        };

        var printBundleNavisionInfo = new PrintBundleBasicEntity
        {
            Id = assignTaskRequest.PrintBundleId.ToString(),
            BatchName = assignTaskRequest.BatchName,
            CreatedOn = DateTimeOffset.Now,
            Customer = "customer",
            Quantities = new PrintBundleQuantitiesEntity
            {
                DocumentCount = 1
            },
            Files = ["file1", "file2"],
            InitiatedBy = "Tester",
            GordNumber = cachedGordAndTaskNumber.GordNumber,
            TaskNumber = cachedGordAndTaskNumber.TaskNumber + 1,
            Metadata = printMetadata,
            State = PrintBundleStateType.Active,
            StateName = PrintBundleStateType.Active.ToString()
        };

        redisCacheHelper
            .Setup(x => x.GetGordAndTaskNumberAsync(assignTaskRequest.DGCode))
            .ReturnsAsync(cachedGordAndTaskNumber);

        redisCacheHelper
            .Setup(x => x.SetGordAndTaskNumberAsync(assignTaskRequest.DGCode, cachedGordAndTaskNumber.GordNumber, cachedGordAndTaskNumber.TaskNumber + 1, expiration))
            .Returns(Task.CompletedTask);

        printBundleRepository
            .Setup(x => x.GetSingleAsync(It.IsAny<string>(), false))
            .ReturnsAsync(new CosmosResponse<PrintBundleBasicEntity>(string.Empty, printBundleNavisionInfo));

        SendBundleInfoToNavisionRequestBundle? sendRequest = null;
        navisionIntegrationService
            .Setup(x => x.SendPrintBundleXmlToNavision(It.IsAny<SendBundleInfoToNavisionRequestBundle>()))
            .Callback((SendBundleInfoToNavisionRequestBundle request) => sendRequest = request)
            .Verifiable();

        var assignNavisionMetadata = new List<PrintBundleAssignTaskCompleted>();
        mocker.GetMock<IPublishEndpoint>()
            .Setup(x => x.Publish(It.IsAny<PrintBundleAssignTaskCompleted>(), It.IsAny<CancellationToken>()))
            .Callback((PrintBundleAssignTaskCompleted x, CancellationToken y) => assignNavisionMetadata.Add(x))
            .Returns(Task.CompletedTask);

        printBundleRepository.Setup(x => x.GetAllSecondaryPrintBundlesAsync(assignTaskRequest.PrintBundleId.ToString()))
            .ReturnsAsync([]);

        printBundleDetailsRepository
            .Setup(x => x.GetSingleAsync(assignTaskRequest.PrintBundleId.ToString(), false))
            .ReturnsAsync(new CosmosResponse<PrintBundleDetailsEntity>(string.Empty, new PrintBundleDetailsEntity { Id = assignTaskRequest.PrintBundleId.ToString() }));

        printBundleDetailsRepository
            .Setup(x => x.ReplaceConsistentAsync(It.IsAny<CosmosResponse<PrintBundleDetailsEntity>>(), It.IsAny<Func<PrintBundleDetailsEntity, bool>>()))
            .Returns(Task.CompletedTask);
        // Act
        await handler.AddDataToBatchAsync(mocker.Get<IPublishEndpoint>(), assignTaskRequest);

        // Assert
        mocker.VerifyAll();

        sendRequest.Should().NotBeNull();

        sendRequest!.Primary.TaskNumber.Should().Be(printBundleNavisionInfo.TaskNumber);
        sendRequest!.Primary.GordNumber.Should().Be(printBundleNavisionInfo.GordNumber);
        sendRequest!.Primary.BatchName.Should().Be(assignTaskRequest.BatchName);
        sendRequest!.Primary.DGCode.Should().Be(assignTaskRequest.DGCode);
        sendRequest!.Primary.BundleMetadata.Should().Be(printMetadata);
        sendRequest!.Primary.DocumentCount.Should().Be(assignTaskRequest.DocumentCount);
        sendRequest!.Primary.SheetCount.Should().Be(assignTaskRequest.SheetCount);

        redisCacheHelper.Verify(x => x.GetGordAndTaskNumberAsync(It.IsAny<string>()), Times.Once());
        redisCacheHelper.Verify(x => x.SetGordAndTaskNumberAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<int>(), It.IsAny<TimeSpan>()), Times.Once());
    }

    [Fact]
    public async Task AddDataToBatchAsync_PrintBatchDoesNotExist_CreateBatch_WithEmptyCache()
    {
        // Arrange
        var assignTaskRequest = new PrintBundleTaskAssignRequest
        {
            BatchName = "BatchName",
            PrintBundleId = Guid.NewGuid(),
            DGCode = "DGCode",
            Timestamp = DateTimeOffset.Now,
            DocumentCount = 1,
            SheetCount = 1,
        };

        var expiration = TimeSpan.FromMinutes(10);
        var navisionGordAndTaskNumber = new NavisionGordAndTaskNumberResponse { GORDNumber = "gordNumber", TaskNumber = 2 };

        var printMetadata = new PrintDocumentMetadataEntity
        {
            Carrier = "carrier",
            DocumentFormat = "documentFormat",
            EndLocation = "endLocation",
            Laminate = true,
            MailDate = DateTime.Today,
            PostalDestination = "postalDestination",
            PrinterType = PrinterType.FC.ToString(),
            PrintMode = "printMode",
            SheetArticleCode = "sheetArticleCode",
            SheetPageCount = 1,
            SheetFormat = "sheetFormat"
        };

        var printBundleNavisionInfo = new PrintBundleBasicEntity
        {
            Id = assignTaskRequest.PrintBundleId.ToString(),
            BatchName = assignTaskRequest.BatchName,
            CreatedOn = DateTimeOffset.Now,
            Customer = "customer",
            Quantities = new PrintBundleQuantitiesEntity
            {
                DocumentCount = 1
            },
            Files = ["file1", "file2"],
            InitiatedBy = "Tester",
            GordNumber = navisionGordAndTaskNumber.GORDNumber,
            TaskNumber = navisionGordAndTaskNumber.TaskNumber + 1,
            Metadata = printMetadata,
            State = PrintBundleStateType.Active,
            StateName = PrintBundleStateType.Active.ToString()
        };

        redisCacheHelper
            .Setup(x => x.GetGordAndTaskNumberAsync(assignTaskRequest.DGCode))
            .ReturnsAsync((GordAndTaskNumber?)null);

        redisCacheHelper
            .Setup(x => x.SetGordAndTaskNumberAsync(assignTaskRequest.DGCode, navisionGordAndTaskNumber.GORDNumber, navisionGordAndTaskNumber.TaskNumber.Value + 1, expiration))
            .Returns(Task.CompletedTask);

        printBundleRepository
            .Setup(x => x.GetSingleAsync(It.IsAny<string>(), false))
            .ReturnsAsync(new CosmosResponse<PrintBundleBasicEntity>(string.Empty, printBundleNavisionInfo));

        SendBundleInfoToNavisionRequestBundle? sendRequest = null;
        navisionIntegrationService
            .Setup(x => x.SendPrintBundleXmlToNavision(It.IsAny<SendBundleInfoToNavisionRequestBundle>()))
            .Callback((SendBundleInfoToNavisionRequestBundle request) => sendRequest = request)
            .Verifiable();

        navisionDataProvider
            .Setup(x => x.GetGordAndTaskNumberAsync(It.Is<string>(x => x == assignTaskRequest.DGCode)))
            .ReturnsAsync(navisionGordAndTaskNumber);

        var assignNavisionMetadata = new List<PrintBundleAssignTaskCompleted>();
        mocker.GetMock<IPublishEndpoint>()
            .Setup(x => x.Publish(It.IsAny<PrintBundleAssignTaskCompleted>(), It.IsAny<CancellationToken>()))
            .Callback((PrintBundleAssignTaskCompleted x, CancellationToken y) => assignNavisionMetadata.Add(x))
            .Returns(Task.CompletedTask);

        printBundleRepository.Setup(x => x.GetAllSecondaryPrintBundlesAsync(assignTaskRequest.PrintBundleId.ToString()))
            .ReturnsAsync([]);

        printBundleDetailsRepository
            .Setup(x => x.GetSingleAsync(assignTaskRequest.PrintBundleId.ToString(), false))
            .ReturnsAsync(new CosmosResponse<PrintBundleDetailsEntity>(string.Empty, new PrintBundleDetailsEntity { Id = assignTaskRequest.PrintBundleId.ToString() }));

        printBundleDetailsRepository
            .Setup(x => x.ReplaceConsistentAsync(It.IsAny<CosmosResponse<PrintBundleDetailsEntity>>(), It.IsAny<Func<PrintBundleDetailsEntity, bool>>()))
            .Returns(Task.CompletedTask);

        // Act
        await handler.AddDataToBatchAsync(mocker.Get<IPublishEndpoint>(), assignTaskRequest);

        // Assert
        mocker.VerifyAll();

        sendRequest.Should().NotBeNull();

        sendRequest!.Primary.TaskNumber.Should().Be(printBundleNavisionInfo.TaskNumber);
        sendRequest!.Primary.GordNumber.Should().Be(printBundleNavisionInfo.GordNumber);
        sendRequest!.Primary.BatchName.Should().Be(assignTaskRequest.BatchName);
        sendRequest!.Primary.DGCode.Should().Be(assignTaskRequest.DGCode);
        sendRequest!.Primary.BundleMetadata.Should().Be(printMetadata);
        sendRequest!.Primary.DocumentCount.Should().Be(assignTaskRequest.DocumentCount);
        sendRequest!.Primary.SheetCount.Should().Be(assignTaskRequest.SheetCount);

        redisCacheHelper.Verify(x => x.GetGordAndTaskNumberAsync(It.IsAny<string>()), Times.Once());
        redisCacheHelper.Verify(x => x.SetGordAndTaskNumberAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<int>(), It.IsAny<TimeSpan>()), Times.Once());
    }

    [Fact]
    public async Task AddDataToBatchAsync_PrintBatchDoesNotExist_CreateBatch_WithFilledCache()
    {
        // Arrange
        var assignTaskRequest = new PrintBundleTaskAssignRequest
        {
            BatchName = "BatchName",
            PrintBundleId = Guid.NewGuid(),
            DGCode = "DGCode",
            Timestamp = DateTimeOffset.Now,
            DocumentCount = 1,
            SheetCount = 1,
        };

        var expiration = TimeSpan.FromMinutes(10);
        var cachedGordAndTaskNumber = new GordAndTaskNumber { GordNumber = "gordNumber", TaskNumber = 2 };

        var printMetadata = new PrintDocumentMetadataEntity
        {
            Carrier = "carrier",
            DocumentFormat = "documentFormat",
            EndLocation = "endLocation",
            Laminate = true,
            MailDate = DateTime.Today,
            PostalDestination = "postalDestination",
            PrinterType = PrinterType.FC.ToString(),
            PrintMode = "printMode",
            SheetArticleCode = "sheetArticleCode",
            SheetPageCount = 1,
            SheetFormat = "sheetFormat"
        };

        var printBundleForNavision = new PrintBundleBasicEntity
        {
            Id = assignTaskRequest.PrintBundleId.ToString(),
            BatchName = assignTaskRequest.BatchName,
            CreatedOn = DateTimeOffset.Now,
            Customer = "customer",
            Quantities = new PrintBundleQuantitiesEntity
            {
                DocumentCount = 1
            },
            Files = ["file1", "file2"],
            InitiatedBy = "Tester",
            GordNumber = cachedGordAndTaskNumber.GordNumber,
            TaskNumber = cachedGordAndTaskNumber.TaskNumber + 1,
            Metadata = printMetadata,
            State = PrintBundleStateType.Active,
            StateName = PrintBundleStateType.Active.ToString()
        };

        redisCacheHelper
            .Setup(x => x.GetGordAndTaskNumberAsync(assignTaskRequest.DGCode))
            .ReturnsAsync(cachedGordAndTaskNumber);

        redisCacheHelper
            .Setup(x => x.SetGordAndTaskNumberAsync(assignTaskRequest.DGCode, cachedGordAndTaskNumber.GordNumber, cachedGordAndTaskNumber.TaskNumber + 1, expiration))
            .Returns(Task.CompletedTask);

        printBundleRepository
            .Setup(x => x.GetSingleAsync(It.IsAny<string>(), false))
            .ReturnsAsync(new CosmosResponse<PrintBundleBasicEntity>(string.Empty, printBundleForNavision));

        SendBundleInfoToNavisionRequestBundle? sendRequest = null;
        navisionIntegrationService
            .Setup(x => x.SendPrintBundleXmlToNavision(It.IsAny<SendBundleInfoToNavisionRequestBundle>()))
            .Callback((SendBundleInfoToNavisionRequestBundle request) => sendRequest = request)
            .Verifiable();

        var assignNavisionMetadata = new List<PrintBundleAssignTaskCompleted>();
        mocker.GetMock<IPublishEndpoint>()
            .Setup(x => x.Publish(It.IsAny<PrintBundleAssignTaskCompleted>(), It.IsAny<CancellationToken>()))
            .Callback((PrintBundleAssignTaskCompleted x, CancellationToken y) => assignNavisionMetadata.Add(x))
            .Returns(Task.CompletedTask);

        printBundleRepository.Setup(x => x.GetAllSecondaryPrintBundlesAsync(assignTaskRequest.PrintBundleId.ToString()))
            .ReturnsAsync([]);

        printBundleDetailsRepository
            .Setup(x => x.GetSingleAsync(assignTaskRequest.PrintBundleId.ToString(), false))
            .ReturnsAsync(new CosmosResponse<PrintBundleDetailsEntity>(string.Empty, new PrintBundleDetailsEntity { Id = assignTaskRequest.PrintBundleId.ToString() }));

        printBundleDetailsRepository
            .Setup(x => x.ReplaceConsistentAsync(It.IsAny<CosmosResponse<PrintBundleDetailsEntity>>(), It.IsAny<Func<PrintBundleDetailsEntity, bool>>()))
            .Returns(Task.CompletedTask);
        // Act
        await handler.AddDataToBatchAsync(mocker.Get<IPublishEndpoint>(), assignTaskRequest);

        // Assert
        mocker.VerifyAll();

        sendRequest.Should().NotBeNull();

        sendRequest!.Primary.TaskNumber.Should().Be(printBundleForNavision.TaskNumber);
        sendRequest!.Primary.GordNumber.Should().Be(printBundleForNavision.GordNumber);
        sendRequest!.Primary.BatchName.Should().Be(assignTaskRequest.BatchName);
        sendRequest!.Primary.DGCode.Should().Be(assignTaskRequest.DGCode);
        sendRequest!.Primary.BundleMetadata.Should().Be(printMetadata);
        sendRequest!.Primary.DocumentCount.Should().Be(assignTaskRequest.DocumentCount);
        sendRequest!.Primary.SheetCount.Should().Be(assignTaskRequest.SheetCount);

        redisCacheHelper.Verify(x => x.GetGordAndTaskNumberAsync(It.IsAny<string>()), Times.Once());
        redisCacheHelper.Verify(x => x.SetGordAndTaskNumberAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<int>(), It.IsAny<TimeSpan>()), Times.Once());
    }
}
