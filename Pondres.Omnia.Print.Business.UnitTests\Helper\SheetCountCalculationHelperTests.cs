﻿using FluentAssertions;
using Pondres.Omnia.Print.Business.Helper;
using Pondres.Omnia.Print.Contracts.PrintBundle;
using Pondres.Omnia.Print.Storage.Entities.Print;
using Xunit;

namespace Pondres.Omnia.Print.Business.UnitTests.Helper;

public class SheetCountCalculationHelperTests
{
    [Theory]
    [MemberData(nameof(TestData))]
    public void SheetCountCalculationHelper_CalculatesCorrectSheetCount(PrintDocumentMetadataEntity metadata, int totalPageCount, int expectedSheetCount)
    {
        var result = SheetCountCalculationHelper.CalculateSheetCount(
            metadata.PrintMode,
            metadata.SheetPageCount,
            totalPageCount);

        result.Should().Be(expectedSheetCount);
    }

    public static object[][] TestData => new object[][] {
        new object[] {
            new PrintDocumentMetadataEntity()
            {
                PageCount = 3,
                SheetPageCount = 3,
                PrintMode = PrintBundlePrintMode.Duplex
            },
            3,
            1
        },

        new object[] {
            new PrintDocumentMetadataEntity()
            {
                PageCount = 3,
                SheetPageCount = 3,
                PrintMode = PrintBundlePrintMode.Simplex

            },
            3,
            1
        },

        new object[] {
            new PrintDocumentMetadataEntity()
            {
                PageCount = 6,
                SheetPageCount = 3,
                PrintMode = PrintBundlePrintMode.Duplex
            },
            6,
            1
        },

        new object[] {
            new PrintDocumentMetadataEntity()
            {
                PageCount = 6,
                SheetPageCount = 3,
                PrintMode = PrintBundlePrintMode.Simplex
            },
            6,
            2
        },

        new object[] {
            new PrintDocumentMetadataEntity()
            {
                PageCount = 6,
                SheetPageCount = 1,
                PrintMode = PrintBundlePrintMode.Duplex
            },
            6,
            3
        },

        new object[] {
            new PrintDocumentMetadataEntity()
            {
                PageCount = 6,
                SheetPageCount = 1,
                PrintMode = PrintBundlePrintMode.Simplex
            },
            6,
            6
        },

        new object[] {
            new PrintDocumentMetadataEntity()
            {
                PageCount = 4,
                SheetPageCount = 3,
                PrintMode = PrintBundlePrintMode.Duplex
            },
            4,
            1
        },

        new object[] {
            new PrintDocumentMetadataEntity()
            {
                PageCount = 4,
                SheetPageCount = 3,
                PrintMode = PrintBundlePrintMode.Simplex
            },
            4,
            2
        },

        new object[] {
            new PrintDocumentMetadataEntity()
            {
                PageCount = 4,
                SheetPageCount = 3,
            },
            4,
            1
        },

        new object[] {
            new PrintDocumentMetadataEntity()
            {
                PageCount = 6,
                SheetPageCount = 3,
            },
            6,
            1
        },

        new object[] {
            new PrintDocumentMetadataEntity()
            {
                PageCount = 9,
                SheetPageCount = 3,
            },
            9,
            2
        }
    };
}
