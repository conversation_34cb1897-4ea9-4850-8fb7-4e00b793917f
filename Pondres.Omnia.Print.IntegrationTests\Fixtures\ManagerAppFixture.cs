﻿using FluentAssertions;
using MassTransit;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.AspNetCore.TestHost;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Pondres.Omnia.OrderHub.IntegrationTests.Helper;
using Pondres.Omnia.Print.IntegrationTests.Fixtures.Api;
using Pondres.Omnia.Print.IntegrationTests.PrintClient;
using Pondres.Omnia.Print.PrintManager.Services;
using Solude.ApiBase.Contracts;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Pondres.Omnia.Print.IntegrationTests.Fixtures;

public class ManagerAppFixture : WebApplicationFactory<PrintManager.Program>
{
    private readonly ITestDependenciesProvider testDependenciesProvider;
    private readonly string cosmosDbEndpoint;

    public HttpClient Client { get; private set; } = default!;

    public ContainersFixture Containers { get; }

    public DocumentApiFixture DocumentApi { get; private set; } = default!;
    public BundleApiFixture BundleApi { get; private set; } = default!;
    public ReleaseScheduleApiFixture ReleaseScheduleApi { get; private set; } = default!;

    public ManagerAppFixture(
        ITestDependenciesProvider testDependenciesProvider,
        string cosmosDbEndpoint,
        ContainersFixture containers)
    {
        this.testDependenciesProvider = testDependenciesProvider;
        this.cosmosDbEndpoint = cosmosDbEndpoint;

        Containers = containers;
    }

    public TService ResolveService<TService>() where TService : notnull
    {
        var scope = Services.CreateScope();

        return scope.ServiceProvider.GetRequiredService<TService>();
    }

    public async Task InitializeAsync()
    {
        Client = CreateClient();
        Client.DefaultRequestHeaders.Add("x-token", "dev");

        var managerClient = new PrintManagerClient(Client);

        DocumentApi = new DocumentApiFixture(managerClient);
        BundleApi = new BundleApiFixture(managerClient, testDependenciesProvider);
        ReleaseScheduleApi = new ReleaseScheduleApiFixture(managerClient);

        var jsonOptions = new JsonSerializerOptions(JsonSerializerDefaults.Web);
        jsonOptions.Converters.Add(new JsonStringEnumConverter());

        var startupTasks = ResolveService<IEnumerable<IStartupTask>>();
        foreach (var startupTask in startupTasks)
        {
            await startupTask.ExecuteAsync();
        }

        await TestHelper.WaitForAsync(async () =>
        {
            Console.Out.WriteLine($"Checking if the service is ready");
            var readyResult = await Client.GetAsync("health/ready");
            readyResult.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
        });
    }

    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        builder.ConfigureAppConfiguration((context, builder) =>
        {
            var configuration = new Dictionary<string, string?>
            {
                // Overwrite configuration with connectionstrings to the created services
                { "DataSeedDisabled", "true" },
                { "NavisionXMLInfoLogicAppConnectionString", "debug" },
                { "RabbitConnectionString", Containers.RabbitMq.GetConnectionString() },
                { "RedisConnectionString", Containers.Redis.GetConnectionString() },
                { "CosmosDbEndpoint", cosmosDbEndpoint }
            };

            builder.AddInMemoryCollection(configuration);
        });
        builder.ConfigureTestServices(services =>
        {
            // Remove and add as transient so it only runs when we feel like it.
            services.RemoveHostedService<ExcludedLocationPeriodicDGImportService>();
            services.AddTransient<ExcludedLocationPeriodicDGImportService>();

            services.RemoveHostedService<LocalFileStorePeriodicCleanupHostedService>();
            services.AddTransient<LocalFileStorePeriodicCleanupHostedService>();
        });

        builder.UseEnvironment("Development");
    }
}
