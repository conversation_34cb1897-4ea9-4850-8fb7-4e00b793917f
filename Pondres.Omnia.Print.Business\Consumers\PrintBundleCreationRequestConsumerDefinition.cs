﻿using MassTransit;
using System;

namespace Pondres.Omnia.Print.Business.Consumers;

public class PrintBundleCreationRequestConsumerDefinition : ConsumerDefinition<PrintBundleCreationRequestConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<PrintBundleCreationRequestConsumer> consumerConfigurator, IRegistrationContext context)
    {
        consumerConfigurator.ConcurrentMessageLimit = 1;
        consumerConfigurator.UseMessageRetry(config => config.Interval(3, TimeSpan.FromSeconds(30)));
    }
}
