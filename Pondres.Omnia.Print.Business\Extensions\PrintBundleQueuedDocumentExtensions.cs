﻿using Pondres.Omnia.Print.Contracts.PrintBundle;
using Pondres.Omnia.Print.Contracts.PrintDocument;
using System;

namespace Pondres.Omnia.Print.Business.Extensions;

public static class PrintBundleQueuedDocumentExtensions
{
    public static PrintDocumentSentToPrint ToPrintDocumentSentToPrint(this PrintBundleQueuedDocument document) =>
        new()
        {
            DocumentMetadata = document.DocumentMetadata,
            OrderMetadata = document.OrderMetadata,
            Timestamp = DateTimeOffset.Now,
            SequenceId = document.SequenceId
        };

    public static PrintDocumentPrinted ToPrintDocumentPrinted(this PrintBundleQueuedDocument document) =>
        new()
        {
            Timestamp = DateTimeOffset.Now,
            DocumentMetadata = document.DocumentMetadata,
            OrderMetadata = document.OrderMetadata,
            SequenceId = document.SequenceId
        };

    public static PrintDocumentPrintConfirmed ToPrintDocumentPrintConfirmed(this PrintBundleQueuedDocument document) =>
        new()
        {
            DocumentMetadata = document.DocumentMetadata,
            OrderMetadata = document.OrderMetadata,
            Timestamp = DateTimeOffset.Now,
            SequenceId = document.SequenceId
        };

    public static PrintDocumentCompletedConfirmed ToPrintDocumentCompletedConfirmed(this PrintBundleQueuedDocument document) =>
        new()
        {
            DocumentMetadata = document.DocumentMetadata,
            OrderMetadata = document.OrderMetadata,
            Timestamp = DateTimeOffset.Now
        };
}