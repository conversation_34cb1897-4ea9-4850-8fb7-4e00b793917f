﻿using MassTransit;
using Pondres.Omnia.OrderHub.Contracts.OrderTask;
using Pondres.Omnia.Print.IntegrationTest.Services;
using System.Text.Json;

namespace Pondres.Omnia.Print.IntegrationTests.Fixtures.Integration;

public class OrderHubIntegrationFixture
{
    private readonly BlobFixture blobFixture;
    private readonly ITestDependenciesProvider testDependenciesProvider;

    public OrderHubIntegrationFixture(
        ITestDependenciesProvider testDependenciesProvider,
        BlobFixture blobFixture)
    {
        this.testDependenciesProvider = testDependenciesProvider;
        this.blobFixture = blobFixture;
    }

    public static Contracts.Common.PrintDocumentCreationMetadata CreatePrintDocumentMetadata(
        Guid? sheetArticleCode = null,
        DateTimeOffset? mailDate = null,
        string? releaseScheduleId = null,
        string? dgCode = null,
        bool isPrimary = true)
    {
        return new Contracts.Common.PrintDocumentCreationMetadata
        {
            CustomerDocumentReference = Guid.NewGuid().ToString(),
            Carrier = "carrier",
            DocumentFormat = "DocumentFormat",
            Laminate = true,
            PrintMode = "Duplex",
            MailDate = mailDate ?? DateTimeOffset.Now,
            PostalDestination = "PostalDestination",
            PrinterType = Contracts.Api.Enums.PrinterType.FC,
            SheetArticleCode = $"SheetArticleCode{sheetArticleCode ?? Guid.NewGuid():N}",
            PageCount = new Random().Next(2, 8),
            SheetPageCount = 2,
            SheetFormat = "SheetFormat",
            Quantity = 1,
            ReleaseSchedule = releaseScheduleId,
            EndLocation = "TestEndLocation",
            DGCode = dgCode ?? Guid.NewGuid().ToString(),
            IsPrimary = isPrimary
        };
    }

    public async Task<OrderTaskBatchStartCommand> SubmitSinglePrintDocumentOrder(
        string customer,
        Contracts.Common.PrintDocumentCreationMetadata metadataCollection,
        bool expectsSuccess = true)
    {
        return await SubmitPrintDocumentOrder(customer, new Contracts.Common.PrintDocumentCreationMetadata[] { metadataCollection }, "printservice_process_printdocument", expectsSuccess);
    }

    public async Task<OrderTaskBatchStartCommand> SubmitMultiPrintDocumentOrder(
        string customer,
        ICollection<Contracts.Common.PrintDocumentCreationMetadata> metadataCollection,
        bool expectsSuccess = true)
    {
        return await SubmitPrintDocumentOrder(customer, metadataCollection, "printservice_process_multi_printdocument", expectsSuccess);
    }

    private async Task<OrderTaskBatchStartCommand> SubmitPrintDocumentOrder(
        string customer,
        ICollection<Contracts.Common.PrintDocumentCreationMetadata> metadataCollection,
        string queueName,
        bool expectsSuccess)
    {
        var bus = testDependenciesProvider.ResolveWorkerService<IBus>();
        var completionObserver = bus.CreatePublishObserver<OrderTaskBatchItemCompleted>();
        var failureObserver = bus.CreatePublishObserver<OrderTaskBatchItemFailed>();

        var groupIndex = 0;

        var storageContainerName = "printdocument";

        var documentPath = await StoreRandomDataFile(storageContainerName);

        var metadataFilePaths = new List<string>();
        foreach (var metadata in metadataCollection)
        {
            var metadataFilePath = $"{Guid.NewGuid()}.json";
            await StoreMetadata(metadataFilePath, storageContainerName, metadata);
            metadataFilePaths.Add(metadataFilePath);
        }

        var message = CreateOrderTask(
            customer: customer,
            storageContainerName: storageContainerName,
            groupIndex: groupIndex,
            recordIndex: 0,
            documentFilePath: documentPath,
            metadataFilePaths: metadataFilePaths);

        var sendEndpoint = await bus.GetSendEndpoint(new Uri($"queue:{queueName}"));
        await sendEndpoint.SendAndWaitForConsumeAsync(bus, message);

        if (expectsSuccess)
        {
            completionObserver.Should().HaveSeenMessage(x => x.TaskId == message.Metadata.TaskId);
            failureObserver.Should().NotHaveSeenMessage(x => x.TaskId == message.Metadata.TaskId);
        }
        else
        {
            completionObserver.Should().NotHaveSeenMessage(x => x.TaskId == message.Metadata.TaskId);
            failureObserver.Should().HaveSeenMessage(x => x.TaskId == message.Metadata.TaskId);
        }

        return message;
    }

    private static OrderTaskBatchStartCommand CreateOrderTask(
        string customer,
        string storageContainerName,
        int groupIndex,
        int recordIndex,
        string documentFilePath,
        ICollection<string> metadataFilePaths)
    {
        var command = new OrderTaskBatchStartCommand
        {
            Input = new OrderTaskBatchStartInput
            {
                Data = new OrderTaskBatchInputData
                {
                    DataContainerName = storageContainerName,
                    DataStorageAccountName = "LOCALDEVELOPMENT",
                    FileGroups = metadataFilePaths.Select(metadataFilePath => new OrderTaskBatchSourceDataFileGroup
                    {
                        Items =
                        [
                            new OrderTaskBatchSourceDataFileGroupItem(
                                new OrderHub.Contracts.Shared.TemporaryFileData
                                {
                                    FilePath = documentFilePath,
                                },
                                OrderTaskBatchSourceDataType.DocumentFile),
                            new OrderTaskBatchSourceDataFileGroupItem(
                                new OrderHub.Contracts.Shared.TemporaryFileData
                                {
                                    FilePath = metadataFilePath,
                                },
                                OrderTaskBatchSourceDataType.DocumentMetadata)
                        ]
                    }).ToList()
                }
            },
            Metadata = new OrderTaskBatchMetadata
            {
                BatchId = $"{groupIndex}_{recordIndex}",
                Customer = customer,
                OrderId = Guid.NewGuid(),
                CustomerReference = Guid.NewGuid().ToString(),
                Flow = "Flow",
                TaskId = Guid.NewGuid(),
                TaskTypeName = OrderTaskType.Output.ToString(),
                OrderCategories = new OrderHub.Contracts.Order.OrderCategories
                {
                    One = "CategoryOne",
                    Two = "CategoryTwo",
                    Three = "CategoryThree"
                }
            },
            Output = new OrderTaskBatchStartOutput
            {
                DirectDataSave = false,
                TemporaryBlobContainerData = null
            }
        };

        return command;
    }

    private async Task<Contracts.Common.PrintDocumentCreationMetadata> StoreMetadata(
        string metadataFilePath,
        string storageContainerName,
        Contracts.Common.PrintDocumentCreationMetadata printMetadata)
    {
        await blobFixture.SaveContentToContainer(
            storageContainerName,
            metadataFilePath,
            JsonSerializer.Serialize(printMetadata, new JsonSerializerOptions(JsonSerializerDefaults.Web)));

        return printMetadata;
    }

    private async Task<string> StoreRandomDataFile(
        string storageContainerName) =>
        await blobFixture.SaveContentToContainer(
            containerName: storageContainerName,
            fileName: $"{Guid.NewGuid()}.tno",
            content: Guid.NewGuid().ToString());
}
