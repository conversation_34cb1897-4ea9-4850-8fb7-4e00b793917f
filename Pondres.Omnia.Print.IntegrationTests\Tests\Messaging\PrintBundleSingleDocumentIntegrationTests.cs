﻿using FluentAssertions;
using Pondres.Omnia.OrderHub.IntegrationTests.Tests;
using Pondres.Omnia.Print.Business.Handler.PrintBatch;
using Pondres.Omnia.Print.IntegrationTests.Fixtures;
using Pondres.Omnia.Print.IntegrationTests.Fixtures.Api;
using Pondres.Omnia.Print.IntegrationTests.Fixtures.Integration;
using Pondres.Omnia.Print.IntegrationTests.PrintClient;

namespace Pondres.Omnia.Print.IntegrationTests.Tests.Messaging;

[Collection("IntegrationTests")]
public class PrintBundleSingleDocumentIntegrationTests : BaseIntegrationTest
{
    public PrintBundleSingleDocumentIntegrationTests(IntegrationTestFixture fixture) : base(fixture)
    {
    }

    [Fact]
    public async Task ManualRelease_EmptyGroup_CompletesBundle()
    {
        // Arrange
        var customer = GenerateCustomer();
        var releaseSchedule = "Di-Do_10.00";

        var printDocumentMetadata = OrderHubIntegrationFixture.CreatePrintDocumentMetadata(releaseScheduleId: releaseSchedule);

        var bundleId = (await SubmitPrintDocumentOrderAsync(customer, printDocumentMetadata)).LastBundle.BundleId;

        await fixture.ManagerApp.BundleApi.WaitForBundleToBeInState(bundleId, MapPrintBundleState.WaitingForStart);

        // Act
        await fixture.ManagerApp.BundleApi.ReleaseBundleAsync(bundleId, PrintBundleMode.EmptyGroup);

        // Assert
        await fixture.ManagerApp.BundleApi.WaitForBundleToBeInState(bundleId, MapPrintBundleState.Impositioning);
        await fixture.ScalerIntegration.PublishImpositioningCompletedReplyAsync(bundleId, customer);
        await fixture.ManagerApp.BundleApi.WaitForBundleToBeInState(bundleId, MapPrintBundleState.WaitingForPrint);
        await fixture.ManagerApp.BundleApi.ConfirmBundleByPrintAsync(bundleId);
        await fixture.ManagerApp.BundleApi.WaitForBundleToBeInState(bundleId, MapPrintBundleState.Completed);
    }

    [Fact]
    public async Task Release_MaxFullSheetsHasNoFullSheets_NewBundleCorrectlyMade()
    {
        var customer = GenerateCustomer();

        var scheduleTime = DateTime.Now.AddMinutes(5);
        scheduleTime = scheduleTime.AddTicks(-scheduleTime.Ticks % TimeSpan.TicksPerMinute);
        var schedule = ReleaseScheduleApiFixture.CreateReleaseSchedule(scheduleTime: scheduleTime.ToString("HH:mm"));
        await fixture.ManagerApp.ReleaseScheduleApi.CreateReleaseScheduleAsync(schedule);

        var printDocumentMetadata = OrderHubIntegrationFixture.CreatePrintDocumentMetadata(releaseScheduleId: schedule.Id);
        printDocumentMetadata.PrintMode = Contracts.PrintBundle.PrintBundlePrintMode.Simplex;
        printDocumentMetadata.PageCount = 1;
        printDocumentMetadata.SheetPageCount = 2;
        printDocumentMetadata.Quantity = 1;

        var documentListItem = await SubmitPrintDocumentOrderAsync(customer, printDocumentMetadata);

        var firstBundleId = documentListItem.LastBundle.BundleId;

        await fixture.ManagerApp.BundleApi.WaitForBundleToBeInState(firstBundleId, MapPrintBundleState.WaitingForStart);

        // Act
        await fixture.ManagerApp.BundleApi.ReleaseBundleAsync(firstBundleId, PrintBundleMode.MaxFullSheets);
        await fixture.ManagerApp.BundleApi.WaitForBundleToBeInState(firstBundleId, MapPrintBundleState.Skipped);

        var newBundleId = await WaitForAsync(async () =>
        {
            var document = await fixture.ManagerApp.DocumentApi.GetSingleDocumentByCustomerReferenceAsync(printDocumentMetadata.CustomerDocumentReference!, customer);

            document.Should().NotBeNull();

            document!.LastBundle.BundleId.Should().NotBe(firstBundleId, because: "BundleId should have changed to a new bundle");

            return document.LastBundle.BundleId;
        });

        await fixture.ManagerApp.BundleApi.WaitForBundleToBeInState(newBundleId, MapPrintBundleState.WaitingForStart);

        var bundleDetails = await fixture.ManagerApp.BundleApi.GetDetailsAsync(newBundleId);
        bundleDetails!.PlannedOn.Should().Be(scheduleTime);

        bundleDetails.Sheets.Should().Be(1);
        bundleDetails.TotalPageCount.Should().Be(1);
    }

    [Fact]
    public async Task ManualRelease_MaxFullSheetsHasNoFullSheets_MovesDocumentsToCorrectNewBundle()
    {
        // Arrange
        var customer = GenerateCustomer();

        var scheduleTime = DateTime.Now.AddMinutes(5);
        scheduleTime = scheduleTime.AddTicks(-scheduleTime.Ticks % TimeSpan.TicksPerMinute);
        var schedule = ReleaseScheduleApiFixture.CreateReleaseSchedule(scheduleTime: scheduleTime.ToString("HH:mm"));
        await fixture.ManagerApp.ReleaseScheduleApi.CreateReleaseScheduleAsync(schedule);

        var printDocumentMetadata = OrderHubIntegrationFixture.CreatePrintDocumentMetadata(releaseScheduleId: schedule.Id);
        printDocumentMetadata.PrintMode = Contracts.PrintBundle.PrintBundlePrintMode.Simplex;
        printDocumentMetadata.PageCount = 1;
        printDocumentMetadata.SheetPageCount = 2;
        printDocumentMetadata.Quantity = 1;

        var documentListItem = await SubmitPrintDocumentOrderAsync(customer, printDocumentMetadata);

        var firstBundleId = documentListItem.LastBundle.BundleId;

        // Act & Assert
        await fixture.ManagerApp.BundleApi.WaitForBundleToBeInState(firstBundleId, MapPrintBundleState.WaitingForStart);
        await fixture.ManagerApp.BundleApi.ReleaseBundleAsync(firstBundleId, PrintBundleMode.MaxFullSheets);
        await fixture.ManagerApp.BundleApi.WaitForBundleToBeInState(firstBundleId, MapPrintBundleState.Skipped);

        var newBundleId = await WaitForAsync(async () =>
        {
            var document = await fixture.ManagerApp.DocumentApi.GetSingleDocumentByCustomerReferenceAsync(printDocumentMetadata.CustomerDocumentReference!, customer);

            document.Should().NotBeNull();

            document!.LastBundle.BundleId.Should().NotBe(firstBundleId, because: "BundleId should have changed to a new bundle");

            return document.LastBundle.BundleId;
        });

        var bundleDetails = await fixture.ManagerApp.BundleApi.GetDetailsAsync(newBundleId);
        bundleDetails!.PlannedOn.Should().Be(scheduleTime);

        // Release it with a release time:
        var newReleaseTime = DateTimeOffset.Now.AddYears(10);

        var result = await fixture.ManagerApp.BundleApi.ReleaseBundleAsync(newBundleId, null, newReleaseTime);

        result.Success.Should().BeTrue();

        var documents = await fixture.ManagerApp.DocumentApi.GetDocumentPagedAsync(new PrintDocumentListFilter { BundleId = newBundleId, MaxPageSize = 100 });
        documents.Items.Should().NotBeEmpty();

        var updatedBundleDetails = await fixture.ManagerApp.BundleApi.GetDetailsAsync(newBundleId);

        documents.Items.Should().AllSatisfy(c => c.LastBundle.BatchName.Should().Be(updatedBundleDetails?.BatchName));

    }

    [Fact]
    public async Task ManualRelease_MaxFullSheetsHasNoFullSheets_SkipsBundle()
    {
        // Arrange
        var customer = GenerateCustomer();
        var mailDate = DateTimeOffset.Now;

        var printDocumentMetadata = OrderHubIntegrationFixture.CreatePrintDocumentMetadata(mailDate: mailDate);
        printDocumentMetadata.PrintMode = Contracts.PrintBundle.PrintBundlePrintMode.Simplex;
        printDocumentMetadata.PageCount = 1;
        printDocumentMetadata.SheetPageCount = 2;
        printDocumentMetadata.Quantity = 1;

        var bundleId = (await SubmitPrintDocumentOrderAsync(customer, printDocumentMetadata)).LastBundle.BundleId;

        // Act & Assert
        await fixture.ManagerApp.BundleApi.WaitForBundleToBeInState(bundleId, MapPrintBundleState.WaitingForStart);
        await fixture.ManagerApp.BundleApi.ReleaseBundleAsync(bundleId, PrintBundleMode.MaxFullSheets);
        await fixture.ManagerApp.BundleApi.WaitForBundleToBeInState(bundleId, MapPrintBundleState.Skipped);
    }

    [Fact]
    public async Task ManualRelease_MaxFullSheetsHasSheets_CompletesBundle()
    {
        // Arrange
        var customer = GenerateCustomer();
        var mailDate = DateTimeOffset.Now;

        var printDocumentMetadata = OrderHubIntegrationFixture.CreatePrintDocumentMetadata(mailDate: mailDate);
        printDocumentMetadata.PrintMode = Contracts.PrintBundle.PrintBundlePrintMode.Simplex;
        printDocumentMetadata.PageCount = 2;
        printDocumentMetadata.SheetPageCount = 1;
        printDocumentMetadata.Quantity = 1;

        var bundleId = (await SubmitPrintDocumentOrderAsync(customer, printDocumentMetadata)).LastBundle.BundleId;

        // Act & Assert
        await fixture.ManagerApp.BundleApi.WaitForBundleToBeInState(bundleId, MapPrintBundleState.WaitingForStart);
        await fixture.ManagerApp.BundleApi.ReleaseBundleAsync(bundleId, PrintBundleMode.MaxFullSheets);
        await fixture.ManagerApp.BundleApi.WaitForBundleToBeInState(bundleId, MapPrintBundleState.Impositioning);
        await fixture.ScalerIntegration.PublishImpositioningCompletedReplyAsync(bundleId, customer);
        await fixture.ManagerApp.BundleApi.WaitForBundleToBeInState(bundleId, MapPrintBundleState.WaitingForPrint);
        await fixture.ManagerApp.BundleApi.ConfirmBundleByPrintAsync(bundleId);
        await fixture.ManagerApp.BundleApi.WaitForBundleToBeInState(bundleId, MapPrintBundleState.Completed);
    }

    [Fact]
    public async Task ManualRelease_ValidRequest_StartsBundle()
    {
        // Arrange
        var customer = GenerateCustomer();
        var sheetArticleCode = Guid.NewGuid();
        var mailDate = DateTime.Now;

        var printDocumentMetadata = OrderHubIntegrationFixture.CreatePrintDocumentMetadata(sheetArticleCode: sheetArticleCode, mailDate: mailDate);

        var printDocument = await SubmitPrintDocumentOrderAsync(customer, printDocumentMetadata);

        // Act
        var releaseResponse = await fixture.ManagerApp.BundleApi.ReleaseBundleAsync(
            id: printDocument.LastBundle.BundleId,
            printBundleMode: PrintBundleMode.EmptyGroup);

        // Assert
        releaseResponse.Success.Should().BeTrue();
    }

    [Fact]
    public async Task Reprint_WaitingBundle_CancelsBundleAndAddsDocumentToNewBundle()
    {
        // Arrange
        var customer = GenerateCustomer();
        var mailDate = DateTimeOffset.Now;
        var printDocumentMetaData = OrderHubIntegrationFixture.CreatePrintDocumentMetadata(mailDate: mailDate);

        var printDocument = await SubmitPrintDocumentOrderAsync(customer, printDocumentMetaData);
        var cancelCommand = new PrintBundleCancelCommand
        {
            PrintBundleId = printDocument.LastBundle.BundleId,
            Force = true,
            Reprint = true,
            Username = "Integration test"
        };
        var documentListFilter = DocumentApiFixture.CreateListFilter(customer);

        // Act
        var cancelResponse = await fixture.ManagerApp.BundleApi.CancelBundleAsync(cancelCommand);

        // Assert
        var document = await fixture.ManagerApp.DocumentApi.GetDocumentPagedAsync(documentListFilter);
        document.Items.First().LastBundle.BundleId.Should().Be(cancelResponse.PrintBundleId);
    }

    [Fact]
    public async Task CancelledBundleNoReprint_NewlySubmitted_AddsDocumentToNewBundle()
    {
        // Arrange
        var customer = GenerateCustomer();
        var mailDate = DateTimeOffset.Now;
        var printDocumentMetaData = OrderHubIntegrationFixture.CreatePrintDocumentMetadata(mailDate: mailDate);

        var printDocument = await SubmitPrintDocumentOrderAsync(customer, printDocumentMetaData);
        var cancelCommand = new PrintBundleCancelCommand
        {
            PrintBundleId = printDocument.LastBundle.BundleId,
            Force = true,
            Reprint = false,
            Username = "Integration test"
        };
        var documentListFilter = DocumentApiFixture.CreateListFilter(customer);

        // Act
        var cancelResponse = await fixture.ManagerApp.BundleApi.CancelBundleAsync(cancelCommand);

        printDocumentMetaData.CustomerDocumentReference = Guid.NewGuid().ToString();

        await SubmitPrintDocumentOrderAsync(customer, printDocumentMetaData);

        // Assert
        var documents = await fixture.ManagerApp.DocumentApi.GetDocumentPagedAsync(documentListFilter);
        documents.Items.OrderBy(c => c.CreatedOn).Last().LastBundle.BundleId.Should().NotBe(cancelResponse.PrintBundleId);
    }

    [Fact]
    public async Task Reprint_WaitingBundle_CancelsOldBundle()
    {
        // Arrange
        var customer = GenerateCustomer();
        var releaseSchedule = "Di-Do_10.00";

        var printDocumentMetadata = OrderHubIntegrationFixture.CreatePrintDocumentMetadata(releaseScheduleId: releaseSchedule);

        var printDocument = await SubmitPrintDocumentOrderAsync(customer, printDocumentMetadata);
        var originalBundleId = printDocument.LastBundle.BundleId;
        await fixture.ManagerApp.BundleApi.WaitForBundleToBeInState(originalBundleId, MapPrintBundleState.WaitingForStart);

        var cancelCommand = new PrintBundleCancelCommand
        {
            PrintBundleId = originalBundleId,
            Force = true,
            Reprint = true,
            Username = "Integration test"
        };

        // Act
        var cancelResponse = await fixture.ManagerApp.BundleApi.CancelBundleAsync(cancelCommand);

        // Assert
        cancelResponse.Success.Should().BeTrue($"It should succeed without error message: {cancelResponse.Message}");

        await WaitForAsync(async () =>
        {
            var bundle = await fixture.ManagerApp.BundleApi.GetDetailsAsync(originalBundleId);
            bundle.Should().NotBeNull();
            bundle!.Status.Status.Should().Be(MapPrintBundleState.Cancelled);
        });
    }

    [Fact]
    public async Task GetBatches_WhenBundlesFound_ReturnBatch()
    {
        // Arrange
        var DGCode = "dgCode";
        var customer = GenerateCustomer();
        var batchName = DateTimeOffset.UtcNow.Date;
        var releaseSchedule = "Di-Do_10.00";

        var creationMetadata = OrderHubIntegrationFixture.CreatePrintDocumentMetadata(dgCode: DGCode, releaseScheduleId: releaseSchedule);
        var bundle = await CreateAndCompletePrintBundleAsync(customer, creationMetadata, batchName);

        var batchFilter = new Contracts.Api.Bundle.PrintBundleDefaultFilter
        {
            Customer = customer,
            BatchName = bundle.BatchName.ToString(),
            ContinuationToken = null!,
            MaxPageSize = 1
        };

        var batchHandler = fixture.ManagerApp.ResolveService<IPrintBatchActionHandler>();

        // Act
        var result = await batchHandler.GetBatchesAsync(batchFilter);

        // Assert
        result.Items.Should().HaveCount(1);
        result.Items.Should().Contain(x => x.Customer == customer);

        var bundleInResult = result.Items.SingleOrDefault(x => x.BatchName.Contains(bundle.BatchName.ToString()));
        bundleInResult.Should().NotBeNull();
    }

    [Fact]
    public async Task GetBatches_WithMultipleBatchNamesWhenBundlesFound_ReturnBatches()
    {
        // Arrange
        var DGCode = "dgCode";
        var customer = GenerateCustomer();
        var batchName1 = DateTimeOffset.UtcNow.Date;
        var batchName2 = DateTimeOffset.UtcNow.Date.AddDays(1);
        var releaseSchedule = "Di-Do_10.00";

        var creationMetadata1 = OrderHubIntegrationFixture.CreatePrintDocumentMetadata(dgCode: DGCode, releaseScheduleId: releaseSchedule);
        var creationMetadata2 = OrderHubIntegrationFixture.CreatePrintDocumentMetadata(dgCode: DGCode, releaseScheduleId: releaseSchedule);
        var bundle1 = await CreateAndCompletePrintBundleAsync(customer, creationMetadata1, batchName1);
        var bundle2 = await CreateAndCompletePrintBundleAsync(customer, creationMetadata2, batchName2);

        var batchFilter = new Contracts.Api.Bundle.PrintBundleDefaultFilter
        {
            Customer = customer,
            ContinuationToken = null!,
            MaxPageSize = 2
        };

        var batchHandler = fixture.ManagerApp.ResolveService<IPrintBatchActionHandler>();

        // Act
        var result = await batchHandler.GetBatchesAsync(batchFilter);

        // Assert
        result.Items.Should().HaveCount(2);
        result.Items.Should().Contain(x => x.Customer == customer);

        var bundle1InResult = result.Items.SingleOrDefault(x => x.BatchName.Contains(bundle1.BatchName.ToString()));
        var bundle2InResult = result.Items.SingleOrDefault(x => x.BatchName.Contains(bundle2.BatchName.ToString()));
        bundle1InResult.Should().NotBeNull();
        bundle2InResult.Should().NotBeNull();
    }

    [Fact]
    public async Task GetBatches_WithDGCodeWhenBundlesFound_ReturnBatches()
    {
        // Arrange
        var DGCode = "dgCode";
        var customer = GenerateCustomer();
        var batchName1 = DateTimeOffset.UtcNow.Date;
        var batchName2 = DateTimeOffset.UtcNow.Date.AddDays(1);
        var mailDate = DateTimeOffset.Now;

        var creationMetadata1 = OrderHubIntegrationFixture.CreatePrintDocumentMetadata(dgCode: DGCode, mailDate: mailDate);
        var creationMetadata2 = OrderHubIntegrationFixture.CreatePrintDocumentMetadata(dgCode: DGCode, mailDate: mailDate);
        var bundle1 = await CreateAndCompletePrintBundleAsync(customer, creationMetadata1, batchName1);
        var bundle2 = await CreateAndCompletePrintBundleAsync(customer, creationMetadata2, batchName2);

        var batchFilter = new Contracts.Api.Bundle.PrintBundleDefaultFilter
        {
            DGCode = DGCode,
            Customer = customer,
            ContinuationToken = null!,
            MaxPageSize = 2
        };

        var batchHandler = fixture.ManagerApp.ResolveService<IPrintBatchActionHandler>();

        // Act
        var result = await batchHandler.GetBatchesAsync(batchFilter);

        // Assert
        result.Items.Should().HaveCount(2);
        result.Items.Should().Contain(x => x.DGCode == DGCode);
        result.Items.Should().Contain(x => x.Customer == customer);

        var bundle1InResult = result.Items.SingleOrDefault(x => x.BatchName.Contains(bundle1.BatchName.ToString()));
        var bundle2InResult = result.Items.SingleOrDefault(x => x.BatchName.Contains(bundle2.BatchName.ToString()));
        bundle1InResult.Should().NotBeNull();
        bundle2InResult.Should().NotBeNull();
    }
}