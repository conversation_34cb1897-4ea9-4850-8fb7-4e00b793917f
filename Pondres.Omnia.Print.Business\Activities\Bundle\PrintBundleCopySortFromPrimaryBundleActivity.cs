﻿using MassTransit;
using Pondres.Omnia.Print.Business.Service;
using Pondres.Omnia.Print.Business.StateMachine.PrintBundle.Model;
using Pondres.Omnia.Print.Contracts.PrintBundle;
using Pondres.Omnia.Print.Contracts.PrintDocument;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Activities.Bundle;

public class PrintBundleCopyInformationPrimaryBundleActivity : BaseActivity<PrintBundleState, PrintBundleReleaseRequest>
{
    private readonly IPrintBundleDocumentStorageService bundleDocumentStorageService;

    public PrintBundleCopyInformationPrimaryBundleActivity(
        IPrintBundleDocumentStorageService bundleDocumentStorageService
        )
    {
        this.bundleDocumentStorageService = bundleDocumentStorageService;
    }

    public override string ActivityName => "sort-print-document-activity";

    protected override async Task ExecuteAsync(BehaviorContext<PrintBundleState, PrintBundleReleaseRequest> context)
    {
        CopyBatchInformation(context);

        await CopySortingAsync(context);
    }

    private static void CopyBatchInformation(BehaviorContext<PrintBundleState, PrintBundleReleaseRequest> context)
    {
        context.Saga.GordNumber = context.Message.GordNumber;
        context.Saga.TaskNumber = context.Message.TaskNumber.GetValueOrDefault(-1);
    }

    private async Task CopySortingAsync(BehaviorContext<PrintBundleState> context)
    {
        var assignedEvents = new List<PrintDocumentAssignSequenceId>();

        var primaryBundleDocumentCollection = await bundleDocumentStorageService.GetDocumentsForBundleAsync(context.Saga.PrimaryBundleId);

        await bundleDocumentStorageService.UpdateDocumentCollectionAsync(context.Saga.CorrelationId, collection =>
        {
            assignedEvents = new List<PrintDocumentAssignSequenceId>(collection.Documents.Count);

            foreach (var secondaryDocument in collection.Documents)
            {
                var primaryDocument = primaryBundleDocumentCollection.Single(d => d.DocumentMetadata.DocumentId == secondaryDocument.DocumentMetadata.PrimaryDocumentId);

                secondaryDocument.SequenceId = primaryDocument.SequenceId;

                assignedEvents.Add(new PrintDocumentAssignSequenceId
                {
                    PrintBundleId = context.Saga.CorrelationId,
                    DocumentId = secondaryDocument.DocumentMetadata.DocumentId,
                    SequenceId = secondaryDocument.SequenceId,
                    GordNumber = context.Saga.GordNumber,
                    TaskNumber = context.Saga.TaskNumber
                });
            }
        });

        var publishTasks = assignedEvents.Select(assignSequenceIdToDocumentCommand => context.Publish(assignSequenceIdToDocumentCommand)).ToList();
        await Task.WhenAll(publishTasks);
    }
}