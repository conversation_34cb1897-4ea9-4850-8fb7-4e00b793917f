# generic api chart

replicaCount: 1

namespace: omnia

image:
  repository: "#|ContainerRegistry|#.azurecr.io/omnia-print-printmanager"
  pullPolicy: IfNotPresent
  tag: "#|Build.BuildNumber|#"

imagePullSecrets: []
nameOverride: "print-manager" # name of application
fullnameOverride: "print-manager" # name of application

config:
  ASPNETCORE_ENVIRONMENT: "#|ASPNETCORE_ENVIRONMENT|#"
  RELEASE_NAME: "#|Release.ReleaseName|##|Release.AttemptNumber|#"
  VaultName: "#|AZURE_KEYVAULT_NAME|#"
  TZ: "Europe/Amsterdam"
  NiesEnabled: "#|NIES_ENABLED|#"
  NiesEndpointAddress: "#|NIES_ENDPOINT_ADDRESS|#"
  FileStoragePath: "#|FILESTORAGE_PATH|#"
  PrintFolderPath: "#|PRINT_FOLDER_PATH|#"
  CustomersExcludedFromDirectComplete: "#|CUSTOMERS_EXCLUDED_FROM_DIRECTCOMPLETE|#"
  
secrets:
  AppInsightsConnectionString : "#|AppInsightsConnectionString|#"
  NavisionXMLInfoLogicAppConnectionString: "#|NAVISION_XML_INFO_LOGIC_APP_URI|#"

podAnnotations: {}
podLabels:
  aadpodidbinding: "#|AKS_IDENTITY_NAME|#"

service:
  type: ClusterIP
  port: 80
  containerPort: 8080

probes:
  initialDelaySeconds: 0
  periodSeconds: 10
  timeoutSeconds: 2
  successThreshold: 1
  failureThreshold: 5
  http:
    livenessPath: /health/live
    readinessPath: /health/ready
    scheme: HTTP

hostAliases:
  - ip: "#|NIES_IPADDRESS|#"
    hostnames:
      - "#|NIES_HOSTALIASES|#"

ingress:
  enabled: false # enable if API needs to be accesible from outside the cluster
  className: "nginx-external"
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
  hosts:
    - host: "#|Omnia.BaseApiDomain|#"
      paths:
        - path: /print
          pathType: Prefix
  tls:
    - secretName: ingress-tls-csi
      hosts:
        - "#|Omnia.BaseApiDomain|#"

resources:
  limits:
    cpu: 250m
    memory: 512Mi
  requests:
    cpu: 100m
    memory: 512Mi

volumes:
  - name: pondressync-storage
    azureFile:
      secretName: pondressync-shared-storage-secret
      shareName: "scaler-pondressync-storage/PondresShare"
      readOnly: false               
  - name: secrets-store-inline
    csi:
      driver: secrets-store.csi.k8s.io
      readOnly: true
      volumeAttributes:
        secretProviderClass: "azure-tls"
  - name: tmp
    emptyDir: {}
  - name: inspire-scaler-shared-storage
    azureFile:
      secretName: quadient-shared-storage-secret
      shareName: scaler-shared-storage
      readOnly: false    

volumeMounts:
  - name: secrets-store-inline
    mountPath: "/mnt/secrets-store"
    readOnly: true   
  - name: pondressync-storage
    mountPath: "/mnt/PondresShare"
  - name: tmp
    mountPath: /tmp
  - name: inspire-scaler-shared-storage
    mountPath: "/mnt/scaler"    

nodeSelector: {}

tolerations: []

affinity: {}
