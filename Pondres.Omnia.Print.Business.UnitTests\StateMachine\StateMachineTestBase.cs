﻿using FluentAssertions;
using MassTransit;
using MassTransit.Saga;
using MassTransit.Testing;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using Moq.AutoMock;
using Pondres.Omnia.Print.Business.Handler;
using Pondres.Omnia.Print.Business.Service;
using Pondres.Omnia.Print.Business.UnitTests.Helper;
using System;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.UnitTests.StateMachine;

public class StateMachineTestBase<TStateMachine, TState>
    where TState : class, SagaStateMachineInstance
    where TStateMachine : MassTransitStateMachine<TState>
{
    protected readonly AutoMocker mocker;
    private readonly ServiceCollection services;

    public StateMachineTestBase()
    {
        mocker = new AutoMocker(MockBehavior.Loose);

        services = new ServiceCollection();
    }

    protected async Task RunHarnessTest<TMessage>(
        TMessage message,
        TState initialState,
        Func<ISagaStateMachineTestHarness<TStateMachine, TState>, ITestHarness, Task> assertFunc)
        where TMessage : class
    {
        var (testHarness, sagaHarness) = SetupStateMachineHarness(initialState);

        await testHarness.Start();

        try
        {
            // Act
            await PublishAndVerifyMessageAsync(testHarness, sagaHarness, message);

            await assertFunc.Invoke(sagaHarness, testHarness);
        }
        finally
        {
            await testHarness.Stop();
        }
    }

    private (ITestHarness testHarness, ISagaStateMachineTestHarness<TStateMachine, TState> sagaHarness) SetupStateMachineHarness(
        TState? initialState)
    {
        var provider = services
            .AddSingleton(mocker.GetMock<IPrintBundleDocumentStorageService>().Object)
            .AddSingleton(mocker.GetMock<IPrintBundleImpositioningHandler>().Object)
            .AddSingleton(mocker.GetMock<IPrintBundleService>().Object)
            .AddSingleton(mocker.GetMock<IPrintBundleEventService>().Object)
            .AddSingleton(mocker.GetMock<IPrintBatchNotificationHandler>().Object)
            .AddSingleton(mocker.GetMock<IPrintBundleEventHistoryUpdateHandler>().Object)
            .Configure<PrintBundleConfiguration>(
                options =>
                {
                    options.CustomersExcludedFromDirectComplete = [TestPrintBundleHelper.DirectCompleteDisabledCustomer];
                })

            .AddMassTransitTestHarness(cfg =>
            {
                cfg.AddSagaStateMachine<TStateMachine, TState>().InMemoryRepository();

                cfg.AddPublishMessageScheduler();
            })
            .BuildServiceProvider(true);

        var harness = provider.GetTestHarness();

        var sagaHarness = harness.GetSagaStateMachineHarness<TStateMachine, TState>();

        if (initialState != null)
        {
            var repository = provider.GetRequiredService<IndexedSagaDictionary<TState>>();
            repository.Add(new SagaInstance<TState>(initialState));
        }

        return (harness, sagaHarness);
    }

    private static async Task PublishAndVerifyMessageAsync<TMessage>(
        ITestHarness testHarness,
        ISagaStateMachineTestHarness<TStateMachine, TState> sagaHarness,
        TMessage message) where TMessage : class
    {
        // Act
        await testHarness.Bus.Publish(message);

        // Assert
        (await testHarness.Published.Any<TMessage>()).Should().BeTrue();
        (await testHarness.Consumed.Any<TMessage>()).Should().BeTrue();
        (await sagaHarness.Consumed.Any<TMessage>()).Should().BeTrue();
    }

    protected async static Task WaitFor(Func<Task> task, int seconds = 5)
    {
        var start = DateTimeOffset.Now;

        while (true)
        {
            try
            {
                await task();

                return;
            }
            catch (Exception)
            {
                if (start + TimeSpan.FromSeconds(seconds) < DateTimeOffset.Now)
                {
                    throw;
                }

                await Task.Delay(500);
            }
        }
    }
}