﻿using Azure;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Pondres.Omnia.Print.Contracts.Common;
using Pondres.Omnia.Print.Storage.DataStorage;
using Pondres.Omnia.Print.Storage.Repositories;
using Serilog;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Service;

public class PrintDocumentLocalFileStoreServiceOptins
{
    public string ScalerMountFolderPrefixPath { get; set; } = string.Empty;
}

/// <summary>
/// Used to manage the local file store of print files. Scaler mounts to the same local path and reads the files directly.
/// Scaler is unable to download files using a temporary url because it can only download sequentally and that's too slow.
/// Currently parallel download is not supported in Sclaer 09/12/2024
/// </summary>
public class PrintDocumentLocalFileStoreService(
    IOptions<PrintDocumentLocalFileStoreServiceOptins> options,
    IPrintBundleBasicRepository printBundleBasicRepository,
    IDataStorageRepositoryFactory storageRepositoryFactory,
    IHostEnvironment hostEnvironment)
{

    public async Task<List<Guid>> CleanupLocalStorageForCompletedBundlesAsync()
    {
        var bundleIds = Directory
            .GetDirectories(options.Value.ScalerMountFolderPrefixPath)
            .Select(fullDirectoryPath => Guid.Parse(Path.GetFileName(fullDirectoryPath)))
            .ToList();

        var cleanedUpBundles = new List<Guid>();

        foreach (var bundleId in bundleIds)
        {
            var bundle = await printBundleBasicRepository.GetSingleOrDefaultAsync(bundleId.ToString());

            if (bundle != null && bundle.Resource.State != PrintBundleStateType.Completed)
                continue; // Bundle not done yet, keep the files. If the bundle is gone, we also clear the files

            var direrctoryPath = GetBundleFolderName(bundleId);

            Directory.Delete(direrctoryPath, recursive: true);

            cleanedUpBundles.Add(bundleId);
        }

        return cleanedUpBundles;
    }

    private string GetBundleFolderName(Guid bundleId) =>
        Path.Combine(options.Value.ScalerMountFolderPrefixPath, bundleId.ToString());

    public async Task StoreDocumentFileToLocalFileStoreAsync(
        Guid bundleId,
        string storageAccountName,
        string storageContainerName,
        string filePath)
    {
        var container = await storageRepositoryFactory.GetStorageContainerAsync(
            storageAccountName: storageAccountName,
            containerName: storageContainerName);

        var printFileName = Path.GetFileName(filePath);
        var bundleDirectory = GetBundleFolderName(bundleId);
        var localPrintFilePath = Path.Combine(bundleDirectory, printFileName);

        Directory.CreateDirectory(bundleDirectory);

        using var fileStream = new FileStream(localPrintFilePath, FileMode.Create, FileAccess.Write);

        try
        {
            await container.DownloadToStreamAsync(fileStream, filePath);
        }
        catch (RequestFailedException ex) when (ex.Status == 404)
        {
            var bundle = await printBundleBasicRepository.GetSingleOrDefaultAsync(bundleId.ToString());

            var isTestCustomer = bundle?.Resource.Customer?.EndsWith("-TEST", StringComparison.OrdinalIgnoreCase) == true;

            var isTestEnvironment =
                hostEnvironment.EnvironmentName.Equals("Staging", StringComparison.OrdinalIgnoreCase) ||
                hostEnvironment.EnvironmentName.Equals("Test", StringComparison.OrdinalIgnoreCase);

            if (isTestEnvironment || isTestCustomer)
            {
                Log.Warning(ex, "Blob not found for test environment or test customer. BundleId: {BundleId}, Customer: {Customer}, Environment: {Environment}, StorageAccount: {StorageAccount}, Container: {Container}, FilePath: {FilePath}",
                    bundleId, bundle?.Resource.Customer, hostEnvironment.EnvironmentName, storageAccountName, storageContainerName, filePath);
            }
            else
            {
                Log.Error(ex, "Blob not found during download for bundle. BundleId: {BundleId}, Customer: {Customer}, Environment: {Environment}, StorageAccount: {StorageAccount}, Container: {Container}, FilePath: {FilePath}",
                    bundleId, bundle?.Resource.Customer, hostEnvironment.EnvironmentName, storageAccountName, storageContainerName, filePath);
                throw; // Re-throw for production bundles
            }
        }
    }
}
