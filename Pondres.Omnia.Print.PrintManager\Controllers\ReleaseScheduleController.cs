﻿using FluentValidation;
using Microsoft.AspNetCore.Mvc;
using Pondres.Omnia.Print.Business.Service;
using Pondres.Omnia.Print.Contracts.Api.ReleaseSchedule;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.PrintManager.Controllers;

[Route("releaseSchedules")]
public class ReleaseScheduleController : Controller
{
    private readonly IReleaseScheduleService releaseScheduleService;

    public ReleaseScheduleController(IReleaseScheduleService releaseScheduleService)
    {
        this.releaseScheduleService = releaseScheduleService;
    }

    [HttpGet]
    [Route("getAll")]
    [ProducesResponseType(200, Type = typeof(List<ReleaseSchedules>))]
    [ProducesResponseType(404, Type = typeof(string))]
    public async Task<IActionResult> GetAllSchedules()
    {
        var schedules = await releaseScheduleService.GetAllReleaseSchedulesAsync();
        return Ok(schedules);
    }

    [HttpPost]
    [Route("create")]
    [ProducesResponseType(200, Type = typeof(ReleaseScheduleResult))]
    [ProducesResponseType(400, Type = typeof(string))]
    [ProducesResponseType(404, Type = typeof(string))]
    public async Task<IActionResult> CreateReleaseScheduleAsync([FromBody] ReleaseSchedules entity)
    {
        try
        {
            await releaseScheduleService.CreateReleaseScheduleAsync(entity);
            return Ok(new ReleaseScheduleResult { IsValid = true });
        }
        catch (ValidationException validationException)
        {
            return Ok(new ReleaseScheduleResult { IsValid = false, Message = validationException.Message });
        }
    }

    [HttpPost]
    [Route("update")]
    [ProducesResponseType(200, Type = typeof(ReleaseScheduleResult))]
    [ProducesResponseType(404, Type = typeof(string))]
    public async Task<IActionResult> UpdateReleaseScheduleAsync([FromBody] ReleaseSchedules entity)
    {
        try
        {
            await releaseScheduleService.UpdateReleaseScheduleAsync(entity);
            return Ok(new ReleaseScheduleResult { IsValid = true });
        }
        catch (ValidationException validationException)
        {
            return Ok(new ReleaseScheduleResult { IsValid = false, Message = validationException.Message });
        }
    }

    [HttpDelete]
    [Route("delete")]
    [ProducesResponseType(200)]
    [ProducesResponseType(404, Type = typeof(string))]
    public async Task<IActionResult> DeleteReleaseScheduleAsync(string id)
    {
        await releaseScheduleService.DeleteReleaseScheduleAsync(id);
        return Ok();
    }
}
