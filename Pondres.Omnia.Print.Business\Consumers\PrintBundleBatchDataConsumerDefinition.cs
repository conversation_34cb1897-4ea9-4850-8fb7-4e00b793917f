﻿using MassTransit;
using System;

namespace Pondres.Omnia.Print.Business.Consumers;

public class PrintBundleBatchDataConsumerDefinition : ConsumerDefinition<PrintBundleBatchAssignConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<PrintBundleBatchAssignConsumer> consumerConfigurator, IRegistrationContext context)
    {
        consumerConfigurator.UseMessageRetry(config => config.Interval(3, TimeSpan.FromSeconds(30)));
    }
}
