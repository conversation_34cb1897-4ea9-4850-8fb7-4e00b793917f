﻿using Azure.Identity;
using System;
using Azure.Core;
using Azure.Storage.Blobs;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Options;

namespace Pondres.Omnia.Print.Storage.DataStorage;

public class DataStorageOptions
{
    public bool IsDevelopment { get; set; }
    public string DevelopmentConnectionString { get; set; } = string.Empty;
}

public class DataStorageRepositoryFactory : IDataStorageRepositoryFactory
{
    private readonly DefaultAzureCredential azureCredential;

    private readonly DataStorageOptions storageOptions;

    private readonly ISharedAccessUrlGenerator sharedAccessUrlGenerator;

    private readonly SemaphoreSlim clientAddSemaphore = new(1, 1);
    private readonly Dictionary<string, BlobServiceClient> serviceClients = [];

    public DataStorageRepositoryFactory(IOptions<DataStorageOptions> options)
    {
        storageOptions = options.Value;

        sharedAccessUrlGenerator = storageOptions.IsDevelopment ?
            new EmulatorSharedAccessUrlGenerator(DataStorageConstants.DevelopmentAccountSharedKey) :
            new AzureSharedAccessUrlGenerator();

        azureCredential = new DefaultAzureCredential();
    }

    public async Task<IDataStorageContainerRepository> GetStorageContainerAsync(string storageAccountName, string containerName)
    {
        var serviceUri = GetServiceUrl(storageAccountName);

        var client = await GetClientForUriAsync(serviceUri);

        return new DataStorageContainerRepository(
            sharedAccessUrlGenerator,
            client,
            containerName);
    }

    private string GetServiceUrl(string storageAccountName) =>
        storageOptions.IsDevelopment ? storageOptions.DevelopmentConnectionString : $"https://{storageAccountName}.blob.core.windows.net";

    private BlobServiceClient CreateNewBlobClient(string serviceUrl)
    {
        var blobClientOptions = new BlobClientOptions();
        blobClientOptions.Retry.MaxRetries = 3;
        blobClientOptions.Retry.Mode = RetryMode.Exponential;

        var client = storageOptions.IsDevelopment ?
            new BlobServiceClient(connectionString: serviceUrl, options: blobClientOptions) :
            new BlobServiceClient(new Uri(serviceUrl), credential: azureCredential, options: blobClientOptions);

        return client;
    }

    private async Task<BlobServiceClient> GetClientForUriAsync(string serviceUrl)
    {
        if (serviceClients.TryGetValue(serviceUrl, out var client))
        {
            return client;
        }
        else
        {
            await clientAddSemaphore.WaitAsync();

            try
            {
                // Maybe it was created in the meantime while waiting.
                if (serviceClients.TryGetValue(serviceUrl, out var createdClient))
                {
                    return createdClient;
                }
                else
                {
                    var newClient = CreateNewBlobClient(serviceUrl);
                    serviceClients.Add(serviceUrl, newClient);
                    return newClient;
                }
            }
            finally
            {
                clientAddSemaphore.Release();
            }
        }
    }
}