version: '3.4'

services:
  pondres.omnia.print.printmanager:
    image: ${DOCKER_REGISTRY-}pondresomniaprintprintmanager
    build:
      context: .
      dockerfile: Pondres.Omnia.Print.PrintManager/Dockerfile
    environment:
      - CosmosDbEndpoint=${DevelopCosmosDbEndpoint}
      - RabbitConnectionString=amqp://guest:guest@rabbitmq
      - RedisConnectionString=redis
      - AzuriteConnectionString=http://azurite:10000/developmentstorageaccount1

  pondres.omnia.print.printworker.1:
    image: ${DOCKER_REGISTRY-}pondresomniaprintprintworker
    build:
      context: .
      dockerfile: Pondres.Omnia.Print.PrintWorker/Dockerfile
    environment:
      - CosmosDbEndpoint=${DevelopCosmosDbEndpoint}
      - RabbitConnectionString=amqp://guest:guest@rabbitmq
      - RedisConnectionString=redis
      - AzuriteConnectionString=http://azurite:10000/developmentstorageaccount1
        
  rabbitmq:
    image: masstransit/rabbitmq
    ports: 
     - "15672:15672"
     - "5672"
    environment:
     - RABBITMQ_ERLANG_COOKIE=456dfgh34sdfb
     - RABBITMQ_CONFIG_FILE=/etc/rabbitmq/rabbitmq.conf
    volumes:
    - ./rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf

  redis:
    image: redis:6-alpine
    command: ["redis-server", "--appendonly", "yes"]
    ports:
    - "6379"
    
  azurite:
    image: mcr.microsoft.com/azure-storage/azurite:3.24.0
    ports:
     - "10000:10000"
     - "10001:10001"
