﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.4.0" />
		<PackageReference Include="Azure.Identity" Version="1.14.0" />
		<PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.5" />
		<PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="9.0.5" />
		<PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
		<PackageReference Include="Serilog.Extensions.Logging" Version="9.0.2" />
		<PackageReference Include="Serilog.Sinks.ApplicationInsights" Version="4.0.0" />
		<PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
		<PackageReference Include="Solude.Packages.Common" Version="1.20250610.1" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.4" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Pondres.Omnia.Print.Contracts\Pondres.Omnia.Print.Contracts.csproj" />
	</ItemGroup>

</Project>
