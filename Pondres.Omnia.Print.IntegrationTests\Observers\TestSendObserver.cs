﻿using FluentAssertions;
using FluentAssertions.Execution;
using FluentAssertions.Primitives;
using MassTransit;
using System.Linq.Expressions;

namespace Pondres.Omnia.Print.IntegrationTests.Observers;

public class TestSendObserver<TExpectedMessageType> : ISendObserver where TExpectedMessageType : class
{
    public List<Exception> Exceptions { get; private set; } = [];
    public List<TExpectedMessageType> SentMessages { get; private set; } = [];

    public Task PostSend<T>(SendContext<T> context) where T : class
    {
        if (context.Message is TExpectedMessageType expectedMessage)
        {
            SentMessages.Add(expectedMessage);
        }

        return Task.CompletedTask;
    }

    public Task PreSend<T>(SendContext<T> context) where T : class
    {
        return Task.CompletedTask;
    }

    public Task SendFault<T>(SendContext<T> context, Exception exception) where T : class
    {
        Exceptions.Add(exception);

        return Task.CompletedTask;
    }

    public TestSendObserverAssertions<TExpectedMessageType> Should()
    {
        return new TestSendObserverAssertions<TExpectedMessageType>(this);
    }
}

public class TestSendObserverAssertions<TMessage> :
    ReferenceTypeAssertions<TestSendObserver<TMessage>, TestSendObserverAssertions<TMessage>> where TMessage : class
{
    public TestSendObserverAssertions(TestSendObserver<TMessage> instance)
        : base(instance)
    {
    }

    protected override string Identifier => "publisher";

    public AndConstraint<TestSendObserverAssertions<TMessage>> HaveSeenMessage(
        Expression<Func<TMessage, bool>> messageExpression, string because = "", params object[] becauseArgs)
    {
        var messageMatchFunc = messageExpression.Compile();

        Execute.Assertion
            .BecauseOf(because, becauseArgs)
            .ForCondition(Subject.SentMessages.SingleOrDefault(messageMatchFunc) != null)
            .FailWith("Expected message was not sent where {0}{reason}, but the condition was not met by:"
                        + Environment.NewLine + Environment.NewLine + "{1}.",
                messageExpression, Subject);

        return new AndConstraint<TestSendObserverAssertions<TMessage>>(this);
    }

    public AndConstraint<TestSendObserverAssertions<TMessage>> NotHaveSeenMessage(
            Expression<Func<TMessage, bool>> messageExpression, string because = "", params object[] becauseArgs)
    {
        var messageMatchFunc = messageExpression.Compile();

        Execute.Assertion
            .BecauseOf(because, becauseArgs)
            .ForCondition(Subject.SentMessages.SingleOrDefault(messageMatchFunc) == null)
            .FailWith("Expected message was not sent where {0}{reason}, but the condition was not met by:"
                        + Environment.NewLine + Environment.NewLine + "{1}.",
                messageExpression, Subject);

        return new AndConstraint<TestSendObserverAssertions<TMessage>>(this);
    }
}