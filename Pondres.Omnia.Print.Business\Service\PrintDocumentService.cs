﻿using Solude.StorageBase.Model;
using Pondres.Omnia.Print.Business.Extensions;
using Pondres.Omnia.Print.Contracts.Api.Common;
using Pondres.Omnia.Print.Contracts.Api.Document;
using Pondres.Omnia.Print.Contracts.PrintDocument;
using Pondres.Omnia.Print.Storage.Entities.PrintBundle;
using Pondres.Omnia.Print.Storage.Entities.PrintDocument;
using Pondres.Omnia.Print.Storage.Repositories;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Solude.StorageBase.Exceptions;

namespace Pondres.Omnia.Print.Business.Service;

public class PrintDocumentService : IPrintDocumentService
{
    private readonly IPrintBundleBasicRepository printBundleBasicRepository;
    private readonly IPrintDocumentRepository printDocumentRepository;

    public PrintDocumentService(
        IPrintBundleBasicRepository printBundleBasicRepository,
        IPrintDocumentRepository printDocumentRepository)
    {
        this.printBundleBasicRepository = printBundleBasicRepository;
        this.printDocumentRepository = printDocumentRepository;
    }

    public async Task<PagedList<PrintDocumentListItem>> GetDocumentsByFilterAsync(PrintDocumentListFilter filter)
    {
        if (!string.IsNullOrWhiteSpace(filter.BarCode))
        {
            // Split barcode and set SequenceId, GordNumber and TaskNumber accordingly
            filter.SequenceId = int.Parse(filter.BarCode.Substring(9, 6));
            filter.GordNumber = $"GORD{filter.BarCode.Substring(19, 6)}";
            filter.TaskNumber = int.Parse(filter.BarCode.Substring(25, 4));
        }

        var result = await printDocumentRepository.GetByFilterAsync(filter);

        return new PagedList<PrintDocumentListItem>
        {
            ContinuationToken = result.ContinuationToken,
            Items = result.Entities.ToListItemDto().ToList()
        };
    }

    public async Task<PagedList<PrintDocumentListItem>> GetDocumentsByBatchFilterAsync(PrintDocumentBatchListFilter filter)
    {
        ListResult<PrintDocumentEntity> result;
        if (filter.Barcodes.Count != 0)
        {
            foreach (var barcode in filter.Barcodes)
            {
                // Split barcode and add SequenceId, GordNumber and TaskNumber to related lists
                filter.SequenceIds.Add(int.Parse(barcode.Substring(9, 6)));
                filter.GordNumbers.Add($"GORD{barcode.Substring(19, 6)}");
                filter.TaskNumbers.Add(int.Parse(barcode.Substring(25, 4)));
            }

            result = await printDocumentRepository.GetByBatchFilterAsync(filter);

            result.Entities = GetDocumentsByBarCode(filter.Barcodes, result.Entities);
        }
        else
        {
            result = await printDocumentRepository.GetByBatchFilterAsync(filter);
        }

        return new PagedList<PrintDocumentListItem>
        {
            ContinuationToken = result.ContinuationToken,
            Items = result.Entities.ToListItemDto().ToList()
        };
    }

    private static List<PrintDocumentEntity> GetDocumentsByBarCode(List<string> barCodes, List<PrintDocumentEntity> entities)
    {
        var trimmedBarCodes = new List<string>();

        barCodes.ForEach(barcode => trimmedBarCodes.Add($"{int.Parse(barcode.Substring(9, 6))}{barcode.Substring(19, 6)}{int.Parse(barcode.Substring(25, 4))}"));

        var filteredEntities = new List<PrintDocumentEntity>();

        foreach (var entity in entities.Where(x => x.LastBundle != null && !string.IsNullOrEmpty(x.LastBundle.GordNumber)))
        {
            ArgumentNullException.ThrowIfNull(entity.LastBundle);
            ArgumentNullException.ThrowIfNull(entity.LastBundle.GordNumber);

            var barcode = $"{entity.LastBundle.SequenceId}{entity.LastBundle.GordNumber[4..]}{entity.LastBundle.TaskNumber}";

            if (trimmedBarCodes.Contains(barcode))
                filteredEntities.Add(entity);
        }

        return filteredEntities;
    }

    private static bool RegisterPrintBundle(PrintDocumentEntity entity, PrintDocumentBundleRegistrationEntity bundleToRegister, DateTimeOffset timestamp)
    {
        if (entity.Bundles.Exists(b => b.BundleId == bundleToRegister.BundleId))
        {
            Log.Warning("Bundle already registered for document {DocumentId}", entity.Id);
            return false;
        }

        if (entity.LastBundle == null || bundleToRegister.CreatedOn > entity.LastBundle.CreatedOn)
            entity.LastBundle = bundleToRegister;

        entity.Bundles.Add(bundleToRegister);

        entity.LastUpdatedOn = timestamp;

        return true;
    }

    public async Task<PrintDocumentEntity> AssignSequenceIdToEntityAsync(PrintDocumentAssignSequenceId message)
    {
        var entity = await printDocumentRepository.GetSingleAsync(message.DocumentId);

        AssignSequenceId(entity, message.SequenceId, message.PrintBundleId, message.GordNumber, message.TaskNumber);

        await printDocumentRepository.ReplaceConsistentAsync(entity, x => AssignSequenceId(x, message.SequenceId, message.PrintBundleId, message.GordNumber, message.TaskNumber));

        return entity;
    }

    private static bool AssignSequenceId(PrintDocumentEntity entity, int sequenceIdToAssign, Guid printBundleId, string? gordNumber, int taskNumber)
    {
        if (entity.LastBundle == null)
            return true;

        entity.LastBundle.SequenceId = sequenceIdToAssign;
        entity.LastBundle.GordNumber = gordNumber;
        entity.LastBundle.TaskNumber = taskNumber;
        var entityBundles = entity.Bundles.Where(x => x.BundleId == printBundleId).ToList();

        foreach (var bundle in entityBundles)
        {
            bundle.SequenceId = sequenceIdToAssign;
            bundle.GordNumber = gordNumber;
            bundle.TaskNumber = taskNumber;
        }

        entity.LastUpdatedOn = DateTime.Now;

        if ((entity.LastBundle.BundleId == printBundleId) && (entity.LastBundle.SequenceId == sequenceIdToAssign) &&
            entity.Bundles.Exists(b => (b.SequenceId == sequenceIdToAssign) && (b.BundleId == printBundleId)))
            return false;

        return true;
    }

    public async Task RegisterPrintDocumentRequeuedAsync(PrintDocumentRequeued message)
    {
        var bundle = await printBundleBasicRepository.GetSingleAsync(message.PrintBundleId.ToString());

        var document = await printDocumentRepository.GetSingleAsync(message.DocumentMetadata.DocumentId);

        var bundleRegistration = CreateNewBundleRegistration(bundle);

        RegisterPrintBundle(document.Resource, bundleRegistration, message.Timestamp);

        await printDocumentRepository.ReplaceConsistentAsync(
            document,
            document.ETag,
            updateActionRetry: doc => RegisterPrintBundle(doc, bundleRegistration, message.Timestamp));
    }

    public async Task UpdateBatchNameForDocumentsInBundleAsync(Guid printBundleId, string newBatchName)
    {
        List<PrintDocumentEntity> documentsInPrintBundle;

        try
        {
            documentsInPrintBundle = await printDocumentRepository.GetAllForPrintBundleAsync(printBundleId);
        }
        catch (EntityNotFoundException) // no documents in bundle
        {
            return;
        }

        foreach (var document in documentsInPrintBundle)
        {
            if (document.LastBundle != null && document.LastBundle.BatchName != newBatchName)
            {
                document.LastBundle.BatchName = newBatchName;
                await printDocumentRepository.ReplaceConsistentAsync(document, document.GetETag());
            }
        }
    }

    private static PrintDocumentBundleRegistrationEntity CreateNewBundleRegistration(CosmosResponse<PrintBundleBasicEntity> bundle)
    {
        return new PrintDocumentBundleRegistrationEntity
        {
            BundleId = Guid.Parse(bundle.Resource.Id),
            BatchName = bundle.Resource.BatchName,
            CreatedOn = bundle.Resource.CreatedOn,
            GordNumber = bundle.Resource.GordNumber,
            SequenceId = 0,
            TaskNumber = null
        };
    }
}