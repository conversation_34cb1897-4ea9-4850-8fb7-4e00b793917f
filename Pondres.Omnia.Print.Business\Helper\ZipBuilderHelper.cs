﻿using Pondres.Omnia.Print.Integrations.Navision.Model.Bundle;
using System;
using System.Collections.Generic;
using System.IO.Compression;
using System.IO;
using System.Xml.Serialization;
using System.Xml;

namespace Pondres.Omnia.Print.Business.Helper;
public static class ZipBuilderHelper
{
    public static byte[] CreateZip(List<NavisionPrintBundle> xmlData)
    {
        byte[] zipFileContent;
        using (var stream = new MemoryStream())
        {
            using (var archive = new ZipArchive(stream, ZipArchiveMode.Create))
            {
                foreach (var navisionPrintBundle in xmlData)
                {
                    var entryName = $"{navisionPrintBundle.DgCode}-{DateTimeOffset.Now:dd-hh:mm}-{Guid.NewGuid()}.xml";
                    var entry = archive.CreateEntry(entryName, CompressionLevel.Optimal);

                    using var writer = new StreamWriter(entry.Open());
                    var DGProjects = new NavisionDgProjects { Projects = [navisionPrintBundle] };

                    var emptyNamespace = new XmlSerializerNamespaces(new[] { XmlQualifiedName.Empty }); // add to remove metadata from namespace
                    var dgProjectSerializer = new XmlSerializer(typeof(NavisionDgProjects));
                    dgProjectSerializer.Serialize(writer, DGProjects, emptyNamespace);
                }
            }

            zipFileContent = stream.ToArray();
        }

        return zipFileContent;
    }
}
