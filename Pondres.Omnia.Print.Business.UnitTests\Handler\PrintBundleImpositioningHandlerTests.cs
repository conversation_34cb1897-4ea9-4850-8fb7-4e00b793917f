﻿using MassTransit;
using Moq;
using Moq.AutoMock;
using Solude.StorageBase.Model;
using Pondres.Omnia.Print.Business.Handler;
using Pondres.Omnia.Print.Business.Service;
using Pondres.Omnia.Print.Contracts.Common;
using Pondres.Omnia.Print.Contracts.PrintBundle;
using Pondres.Omnia.Print.Storage.DataStorage;
using Pondres.Omnia.Print.Storage.DataStorage.Model;
using Pondres.Omnia.Print.Storage.Entities.PrintBundle;
using Pondres.Omnia.Print.Storage.Entities.Print;
using Pondres.Omnia.Print.Storage.Repositories;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Xunit;
using Pondres.Omnia.Print.Contracts.Api.Enums;
using Pondres.Omnia.Print.Business.UnitTests.Helper;

namespace Pondres.Omnia.Print.Business.UnitTests.Handler;

public class PrintBundleImpositioningHandlerTests
{
    private readonly PrintBundleImpositioningHandler handler;
    private readonly AutoMocker mocker;

    public PrintBundleImpositioningHandlerTests()
    {
        mocker = new AutoMocker(MockBehavior.Strict);

        handler = mocker.CreateInstance<PrintBundleImpositioningHandler>();
    }

    [Fact]
    public async Task SendBundleToImpositioning_PublishesCommand()
    {
        // Arrange
        var customer = "customer";
        var printBundleId = Guid.NewGuid();

        var firstPrintDocument = TestPrintBundleHelper.CreateQueuedDocument();
        var secondPrintDocument = TestPrintBundleHelper.CreateQueuedDocument();

        var printDocuments = new HashSet<PrintBundleQueuedDocument>
        {
            firstPrintDocument,
            secondPrintDocument
        };

        var printMetadata = new PrintDocumentMetadataEntity
        {
            Carrier = "carrier",
            DocumentFormat = "documentFormat",
            EndLocation = "endLocation",
            Laminate = true,
            MailDate = DateTime.Today,
            PostalDestination = "postalDestination",
            PrinterType = PrinterType.FC.ToString(),
            PrintMode = "printMode",
            SheetArticleCode = "sheetArticleCode",
            SheetPageCount = 1,
            SheetFormat = "sheetFormat"
        };

        var printBundleEntity = new PrintBundleDetailsEntity
        {
            Id = printBundleId.ToString(),
            BatchName = "batchName",
            CreatedOn = DateTimeOffset.Now,
            Customer = "customer",
            Quantities = new PrintBundleQuantitiesEntity
            {
                DocumentCount = 1
            },
            Files =
            [
                "file1",
                "file2"
            ],
            InitiatedBy = "Tester",
            GordNumber = "TestGord",
            TaskNumber = 2,
            Metadata = printMetadata,
            State = PrintBundleStateType.Active,
            StateName = PrintBundleStateType.Active.ToString()
        };

        var storageRepositoryMock = new Mock<IDataStorageContainerRepository>(MockBehavior.Strict);
        storageRepositoryMock
            .Setup(x => x.GetSharedAccessSignatureUrlForContainerAsync(TimeSpan.FromHours(3)))
            .ReturnsAsync(new TemporaryDataUri
            {
                DataUri = "dataUri",
                ExpiresOn = DateTimeOffset.Now.AddHours(3),
                FileName = "filename"
            });

        storageRepositoryMock
            .Setup(x => x.GetSharedAccessSignatureUrlForBlobAsync(It.IsAny<string>(), TimeSpan.FromHours(3)))
            .ReturnsAsync(new TemporaryDataUri
            {
                DataUri = "dataUri",
                ExpiresOn = DateTimeOffset.Now.AddHours(3),
                FileName = "filename"
            })
            .Verifiable();

        storageRepositoryMock
            .Setup(x => x.UploadTextAsync(
                It.Is<string>(y => y.Contains(firstPrintDocument.PrintFilePath) && y.Contains(secondPrintDocument.PrintFilePath)),
                $"printbundles/{printBundleId}",
                true))
            .Returns(Task.CompletedTask);

        mocker.GetMock<ISendEndpoint>()
            .Setup(x => x.Send(It.IsAny<PrintBundleStartImpositioningCommand>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        mocker.GetMock<IPrintBundleDetailsRepository>()
            .Setup(x => x.GetSingleAsync(printBundleId.ToString(), false))
            .ReturnsAsync(new CosmosResponse<PrintBundleDetailsEntity>(string.Empty, printBundleEntity));

        mocker.GetMock<IDataStorageRepositoryFactory>()
            .Setup(x => x.GetStorageContainerAsync(printBundleEntity.PrintFilesStorageAccountName, printBundleEntity.PrintFilesContainerName))
            .ReturnsAsync(storageRepositoryMock.Object);

        mocker.GetMock<IPrintBundleDocumentStorageService>()
            .Setup(x => x.GetDocumentsForBundleAsync(printBundleId))
            .ReturnsAsync(printDocuments);

        // Act
        await handler.SendBundleToImpositioningAsync(mocker.Get<ISendEndpoint>(), customer, printBundleId, printBundleEntity.GordNumber, printBundleEntity.TaskNumber.Value);

        // Assert
        mocker.VerifyAll();

        mocker.GetMock<ISendEndpoint>()
            .Verify(x => x.Send(
                It.Is<PrintBundleStartImpositioningCommand>(command =>
                    command.PrintBundleId == printBundleId &&
                    command.BatchName == printBundleEntity.BatchName &&
                    command.Customer == printBundleEntity.Customer &&
                    command.GordNumber == printBundleEntity.GordNumber &&
                    command.TaskNumber == printBundleEntity.TaskNumber &&
                    command.PrintMetadata.MailDate == printMetadata.MailDate &&
                    command.PrintMetadata.Carrier == printMetadata.Carrier &&
                    command.PrintMetadata.PostalDestination == printMetadata.PostalDestination &&
                    command.PrintMetadata.PrinterType.ToString() == printMetadata.PrinterType &&
                    command.PrintMetadata.DocumentFormat == printMetadata.DocumentFormat &&
                    command.PrintMetadata.SheetArticleCode == printMetadata.SheetArticleCode &&
                    command.PrintMetadata.EndLocation == printMetadata.EndLocation &&
                    command.PrintMetadata.SheetFormat == printMetadata.SheetFormat &&
                    command.PrintMetadata.PrintMode == printMetadata.PrintMode &&
                    command.PrintMetadata.Laminate == printMetadata.Laminate &&
                    command.PrintMetadata.SheetPageCount == printMetadata.SheetPageCount),
                It.IsAny<CancellationToken>()));
    }
}
