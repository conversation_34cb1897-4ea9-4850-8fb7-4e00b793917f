﻿using FluentAssertions;
using Pondres.Omnia.OrderHub.IntegrationTests.Helper;
using Pondres.Omnia.Print.IntegrationTests.Fixtures;
using Pondres.Omnia.Print.IntegrationTests.Fixtures.Integration;
using Pondres.Omnia.Print.IntegrationTests.PrintClient;

namespace Pondres.Omnia.OrderHub.IntegrationTests.Tests;

public class BaseIntegrationTest
{
    protected readonly IntegrationTestFixture fixture;

    public BaseIntegrationTest(IntegrationTestFixture fixture)
    {
        this.fixture = fixture;
    }

    protected static string GenerateCustomer() => Guid.NewGuid().ToString("N");

    public async Task<PrintBundleDetails> CreateAndCompletePrintBundleAsync(string customer, Print.Contracts.Common.PrintDocumentCreationMetadata? printDocumentCreationMetadata = null, DateTimeOffset? releaseTime = null)
    {
        printDocumentCreationMetadata ??= OrderHubIntegrationFixture.CreatePrintDocumentMetadata();

        var bundleId = (await SubmitPrintDocumentOrderAsync(customer, printDocumentCreationMetadata)).LastBundle.BundleId;

        // Act & Assert
        await fixture.ManagerApp.BundleApi.WaitForBundleToBeInState(bundleId, MapPrintBundleState.WaitingForStart);
        await fixture.ManagerApp.BundleApi.ReleaseBundleAsync(bundleId, PrintBundleMode.EmptyGroup, releaseTime);
        await fixture.ManagerApp.BundleApi.WaitForBundleToBeInState(bundleId, MapPrintBundleState.Impositioning);
        await fixture.ScalerIntegration.PublishImpositioningCompletedReplyAsync(bundleId, customer);
        await fixture.ManagerApp.BundleApi.WaitForBundleToBeInState(bundleId, MapPrintBundleState.WaitingForPrint);
        await fixture.ManagerApp.BundleApi.ConfirmBundleByPrintAsync(bundleId);
        var bundle = await fixture.ManagerApp.BundleApi.WaitForBundleToBeInState(bundleId, MapPrintBundleState.Completed);

        return bundle!;
    }

    public async Task<List<PrintDocumentListItem>> SubmitMultiPrintDocumentOrderAsync(string customer, ICollection<Print.Contracts.Common.PrintDocumentCreationMetadata> metadataFiles)
    {
        await fixture.OrderHubIntegration.SubmitMultiPrintDocumentOrder(customer, metadataFiles);

        return await WaitForAsync(async () =>
        {
            var getTasks = metadataFiles.Select(metadata =>
                fixture.ManagerApp.DocumentApi.GetSingleDocumentByCustomerReferenceAsync(metadata.CustomerDocumentReference!, customer));

            var documents = await Task.WhenAll(getTasks);

            documents.Should().NotBeNull();
            documents.Should().AllSatisfy(document => document.Should().NotBeNull());

            return documents.Select(x => x!).ToList();
        });
    }

    public async Task<PrintDocumentListItem> SubmitPrintDocumentOrderAsync(string customer, Print.Contracts.Common.PrintDocumentCreationMetadata? printMetadata = null)
    {
        printMetadata ??= OrderHubIntegrationFixture.CreatePrintDocumentMetadata();

        await fixture.OrderHubIntegration.SubmitSinglePrintDocumentOrder(customer, printMetadata);

        return await WaitForAsync(async () =>
        {
            var document = await fixture.ManagerApp.DocumentApi.GetSingleDocumentByCustomerReferenceAsync(printMetadata.CustomerDocumentReference!, customer);

            document.Should().NotBeNull();

            return document!;
        });
    }

    protected static Task WaitForAsync(Func<Task> forFunc, TimeSpan? wait = null, CancellationToken cancellationToken = default) =>
        TestHelper.WaitForAsync(forFunc, wait, cancellationToken);

    protected static Task<TResult> WaitForAsync<TResult>(Func<Task<TResult>> forFunc, TimeSpan? wait = null, CancellationToken cancellationToken = default) =>
        TestHelper.WaitForAsync(forFunc, wait, cancellationToken);
}