﻿using Pondres.Omnia.Print.Contracts.Common;
using System;

namespace Pondres.Omnia.Print.Contracts.PrintBundle
{
    public class PrintBundleCreateCommand
    {
        public PrintBundleOptions BundleOptions { get; set; } = new();
        public int SheetPageCount { get; set; }
        public string SheetArticleCode { get; set; } = string.Empty;
        public string Customer { get; set; } = string.Empty;
        public string InitiatedBy { get; set; } = string.Empty;
        public string BatchName { get; set; } = string.Empty;
        public string? DGCode { get; set; }
        public string? PrintMode { get; set; }
        public string EndLocation { get; set; } = string.Empty;
        public DateTimeOffset? PlannedOn { get; set; }
        public Guid PrintBundleId { get; set; }
        public bool IsPrimary { get; set; }
        public Guid PrimaryBundleId { get; set; }
        public bool IsDirectlyCompleted { get; set; }
    }
}