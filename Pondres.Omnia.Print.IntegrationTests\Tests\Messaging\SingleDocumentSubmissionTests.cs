﻿using FluentAssertions;
using Pondres.Omnia.OrderHub.IntegrationTests.Tests;
using Pondres.Omnia.Print.IntegrationTests.Fixtures;
using Pondres.Omnia.Print.IntegrationTests.Fixtures.Api;
using Pondres.Omnia.Print.IntegrationTests.Fixtures.Integration;
using Pondres.Omnia.Print.IntegrationTests.PrintClient;

namespace Pondres.Omnia.Print.IntegrationTests.Tests.Messaging;

[Collection("IntegrationTests")]
public class SingleDocumentSubmissionTests : BaseIntegrationTest
{
    public SingleDocumentSubmissionTests(IntegrationTestFixture fixture) : base(fixture)
    {
    }

    [Fact]
    public async Task DocumentSubmission_ExistingBundle_AddsToExistingBundle()
    {
        // Arrange
        var customer = GenerateCustomer();

        var sheetArticleCode = Guid.NewGuid();
        var mailDate = DateTime.Now;

        var sharedPrintDocumentMetadata = OrderHubIntegrationFixture.CreatePrintDocumentMetadata(sheetArticleCode: sheetArticleCode, mailDate: mailDate);
        var printDocument1 = await SubmitPrintDocumentOrderAsync(customer, sharedPrintDocumentMetadata);

        // Act
        sharedPrintDocumentMetadata.CustomerDocumentReference = Guid.NewGuid().ToString();
        var printDocument2 = await SubmitPrintDocumentOrderAsync(customer, sharedPrintDocumentMetadata);

        // Assert
        var bundle = await fixture.ManagerApp.BundleApi.GetDetailsAsync(printDocument2.LastBundle.BundleId);
        bundle.Should().NotBeNull();

        printDocument1.LastBundle.BundleId.Should().Be(bundle!.Id);
        printDocument2.LastBundle.BundleId.Should().Be(bundle.Id);
    }

    [Fact]
    public async Task DocumentSubmission_ValidDocument_SavesDocument()
    {
        // Arrange
        var customer = GenerateCustomer();
        var startTestDate = DateTimeOffset.Now;
        var releaseSchedule = "Di-Do_10.00";

        var documentListFilter = DocumentApiFixture.CreateListFilter(customer);

        var printMetadata = OrderHubIntegrationFixture.CreatePrintDocumentMetadata(releaseScheduleId: releaseSchedule);

        // Act
        await fixture.OrderHubIntegration.SubmitSinglePrintDocumentOrder(customer, printMetadata);

        // Assert
        documentListFilter.CustomerDocumentReference = printMetadata.CustomerDocumentReference;

        var document = await WaitForAsync(async () =>
        {
            var pagedList = await fixture.ManagerApp.DocumentApi.GetDocumentPagedAsync(documentListFilter);

            pagedList.Should().NotBeNull();
            pagedList.Items.Should().HaveCount(1);

            return pagedList.Items.First();
        });

        document.LastBundle.Should().NotBeNull();
        document.LastUpdatedOn.Should().BeAfter(startTestDate);
        document.CreatedOn.Should().BeAfter(startTestDate);
        document.CustomerDocumentReference.Should().Be(printMetadata.CustomerDocumentReference);
        document.Id.Should().NotBeNullOrWhiteSpace();

        document.OrderMetadata.Should().NotBeNull();
        document.OrderMetadata.Customer.Should().Be(customer);
    }

    [Fact]
    public async Task DocumentSubmission_NoSchedule_CreatesUnplannedBundle()
    {
        // Arrange
        var customer = GenerateCustomer();
        var mailDate = DateTimeOffset.Now;

        var printDocumentMetadata = OrderHubIntegrationFixture.CreatePrintDocumentMetadata(mailDate: mailDate);

        // Act
        var printDocument = await SubmitPrintDocumentOrderAsync(customer, printDocumentMetadata);

        // Assert
        printDocument.LastBundle.BatchName.Should().Be("Unplanned");

        var bundle = await fixture.ManagerApp.BundleApi.GetDetailsAsync(printDocument.LastBundle.BundleId);
        bundle.Should().NotBeNull();
        bundle!.ReleasedOn.Should().BeNull();
        bundle.PlannedOn.Should().BeNull();
        bundle.BatchName.Should().Be("Unplanned");
    }

    [Fact]
    public async Task DocumentSubmission_WithSchedule_CreatesPlannedBundle()
    {
        // Arrange
        var customer = GenerateCustomer();

        var expectedTime = DateTime.Now.AddMinutes(2).ToString("HH:mm");

        var schedule = ReleaseScheduleApiFixture.CreateReleaseSchedule(scheduleTime: expectedTime);
        await fixture.ManagerApp.ReleaseScheduleApi.CreateReleaseScheduleAsync(schedule);

        var printDocumentMetadata = OrderHubIntegrationFixture.CreatePrintDocumentMetadata(releaseScheduleId: schedule.Id);

        var printDocument = await SubmitPrintDocumentOrderAsync(customer, printDocumentMetadata);

        // Act
        var bundle = await fixture.ManagerApp.BundleApi.GetDetailsAsync(printDocument.LastBundle.BundleId);

        // Assert
        bundle.Should().NotBeNull();
        bundle!.BatchName.Should().Be($"{DateTime.Now:yyyyMMdd}-{expectedTime.Replace(":", "")}");
        bundle.Status.Status.Should().Be(MapPrintBundleState.WaitingForStart);
    }

    [Fact]
    public async Task DocumentSubmission_NoExistingBundle_CreatesNewBundle()
    {
        // Arrange
        var customer = GenerateCustomer();
        var releaseSchedule = "Di-Do_10.00";

        var printMetadata = OrderHubIntegrationFixture.CreatePrintDocumentMetadata(releaseScheduleId: releaseSchedule);

        // Act
        var printDocument = await SubmitPrintDocumentOrderAsync(customer, printMetadata);

        // Assert
        var bundle = await fixture.ManagerApp.BundleApi.GetDetailsAsync(printDocument.LastBundle.BundleId);
        bundle.Should().NotBeNull();
    }
}
