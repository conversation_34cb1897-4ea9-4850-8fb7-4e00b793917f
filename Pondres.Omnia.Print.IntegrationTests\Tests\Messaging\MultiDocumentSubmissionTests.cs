﻿using FluentAssertions;
using Pondres.Omnia.OrderHub.IntegrationTests.Tests;
using Pondres.Omnia.Print.IntegrationTests.Fixtures;
using Pondres.Omnia.Print.IntegrationTests.Fixtures.Integration;

namespace Pondres.Omnia.Print.IntegrationTests.Tests.Messaging;

[Collection("IntegrationTests")]
public class MultiDocumentSubmissionTests : BaseIntegrationTest
{
    public MultiDocumentSubmissionTests(IntegrationTestFixture fixture) : base(fixture)
    {
    }

    [Fact]
    public async Task MultiDocumentSubmission_NoExistingBundles_CreatesNewBundles()
    {
        // Arrange
        var customer = GenerateCustomer();
        var mailDate = DateTimeOffset.Now;

        var metadataFiles = new List<Contracts.Common.PrintDocumentCreationMetadata>
        {
            OrderHubIntegrationFixture.CreatePrintDocumentMetadata(isPrimary: true, mailDate: mailDate),
            OrderHubIntegrationFixture.CreatePrintDocumentMetadata(isPrimary: false, mailDate: mailDate),
            OrderHubIntegrationFixture.CreatePrintDocumentMetadata(isPrimary: false, mailDate: mailDate)
        };

        // Act
        var documents = await SubmitMultiPrintDocumentOrderAsync(customer, metadataFiles);

        // Assert
        documents.Should().HaveCount(metadataFiles.Count);
        documents.Should().ContainSingle(x => x.IsPrimary);

        documents.Should().AllSatisfy(x => x.LastBundle.BundleId.Should().NotBeEmpty());
        documents.Should().AllSatisfy(x =>
            documents.Where(y => y.LastBundle.BundleId == x.LastBundle.BundleId).Should().HaveCount(1), 
            because: "Every document should be placed in its own bundle");

        var primaryDocument = documents.Single(x => x.IsPrimary);

        var primaryBundle = await fixture.ManagerApp.BundleApi.GetDetailsAsync(primaryDocument.LastBundle.BundleId);
        primaryBundle.Should().NotBeNull();
        primaryBundle!.Metadata.IsPrimary.Should().BeTrue();

        var secondaryDocuments = documents.Where(x => !x.IsPrimary);
        secondaryDocuments.Should().HaveCount(metadataFiles.Count - 1);

        foreach (var secondaryDocument in secondaryDocuments)
        {
            var secondaryBundle = await fixture.ManagerApp.BundleApi.GetDetailsAsync(secondaryDocument.LastBundle.BundleId);
            secondaryBundle.Should().NotBeNull();
            secondaryBundle!.Metadata.IsPrimary.Should().BeFalse();
        }
    }
}
