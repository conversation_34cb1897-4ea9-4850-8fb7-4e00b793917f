﻿using Pondres.Omnia.Print.Contracts.Api.Enums;
using System;
using System.Collections.Generic;

namespace Pondres.Omnia.Print.Contracts.Common
{
    public class PrintDocumentCreationMetadata
    {
        public string Carrier { get; set; } = string.Empty;
        public string DocumentFormat { get; set; } = string.Empty;
        public string? CustomerDocumentReference { get; set; }
        public string? DocumentStatus { get; set; }
        public string EndLocation { get; set; } = string.Empty;
        public bool Laminate { get; set; }
        public int Quantity { get; set; }
        public DateTimeOffset MailDate { get; set; }
        public string PostalDestination { get; set; } = string.Empty;
        public PrinterType? PrinterType { get; set; }
        public string? PrintMode { get; set; }
        public string SheetArticleCode { get; set; } = string.Empty;
        public int SheetPageCount { get; set; }
        public string SheetFormat { get; set; } = string.Empty;
        public string? DGCode { get; set; }
        public int PageCount { get; set; }
        public string? Packaging { get; set; }
        public PackingType PackingType { get; set; }
        public string? PackingName { get; set; }
        public string? ReleaseSchedule { get; set; }
        public string? SheetArticleDescription { get; set; }
        public List<PrintDocumentCreationMetadataAttachment> Attachments { get; set; } = new();

        public bool IsPrimary { get; set; }
        public string? PrintBundleSort { get; set; }
    }
}