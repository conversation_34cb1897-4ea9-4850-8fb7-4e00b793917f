﻿using Solude.StorageBase;
using Pondres.Omnia.Print.Contracts.Api.Bundle;
using Pondres.Omnia.Print.Storage.Entities.PrintBundle;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Solude.StorageBase.Model;

namespace Pondres.Omnia.Print.Storage.Repositories;

public interface IPrintBundleDetailsRepository : ISinglePartitionBaseRepository<PrintBundleDetailsEntity>
{
    Task<ListResult<PrintBundleDetailsEntity>> GetByFilterAsync(PrintBundleDefaultFilter filter);
    Task<ListResult<PrintBundleDetailsEntity>> GetByBundleBatchFilterAsync(PrintBundleBatchFilter filter);
    Task<ListResult<PrintBundleDetailsEntity>> GetByPrintBundleIdsAsync(IEnumerable<string> printBundleIds);
    Task<List<PrintBundleDetailsEntity>> GetBundlesWithExcludedLocationAsync(IEnumerable<string> printBundleIds, string[] excludedCustomers, IEnumerable<string> excludedEndLocations);
    Task<PrintBundleDetailsEntity?> GetPlannedBundleAsync(string bundleHash, DateTimeOffset? plannedOn, string? primaryBundleId);
}