﻿using Azure;
using Azure.Storage;
using Azure.Storage.Blobs;
using Azure.Storage.Sas;
using Pondres.Omnia.Print.Storage.DataStorage.Model;
using Serilog;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Storage.DataStorage;

public class EmulatorSharedAccessUrlGenerator : ISharedAccessUrlGenerator
{
    private const int pageSize = 500;
    private readonly StorageSharedKeyCredential storageSharedKeyCredential;

    public EmulatorSharedAccessUrlGenerator(StorageSharedKeyCredential storageSharedKeyCredential)
    {
        this.storageSharedKeyCredential = storageSharedKeyCredential;
    }

    public Task<TemporaryDataUri> GetSharedAccessSignatureForContainerAsync(
        BlobServiceClient client,
        BlobContainerClient container,
        BlobContainerSasPermissions containerSasPermissions,
        TimeSpan expireTime)
    {
        var containerName = container.Name == "devstoreaccount1" ? container.Uri.Segments[2] : container.Name;

        var expiresOn = DateTimeOffset.Now.Add(expireTime);

        var builder = new BlobSasBuilder
        {
            BlobContainerName = containerName,
            Resource = "c",
            StartsOn = DateTimeOffset.Now,
            ExpiresOn = expiresOn
        };

        builder.SetPermissions(containerSasPermissions);
        var queryParameters = builder.ToSasQueryParameters(storageSharedKeyCredential);

        var sasUri = new UriBuilder(container.Uri)
        {
            Query = queryParameters.ToString()
        };

        return Task.FromResult(new TemporaryDataUri
        {
            DataUri = sasUri.ToString(),
            ExpiresOn = expiresOn
        });
    }

    public Task<TemporaryDataUri> GetSharedAccessSignatureUrlForBlobAsync(BlobServiceClient client, BlobContainerClient container, string filePath, TimeSpan expireTime)
    {
        var urlInformation = GetSharedAccessSignatureForBlob(container, filePath, expireTime);

        return Task.FromResult(urlInformation);
    }

    public Task<List<TemporaryDataUri>> GetSharedAccessSignatureUrlForBlobsInFolderAsync(BlobServiceClient client, BlobContainerClient container, string folderName, TimeSpan expireTime)
    {
        var urls = new List<TemporaryDataUri>();

        string? continuationToken = null;

        try
        {
            // Call the listing operation and enumerate the result segment.
            // When the continuation token is empty, the last segment has been returned
            // and execution can exit the loop.
            do
            {
                var resultSegment = container
                    .GetBlobs(prefix: folderName)
                    .AsPages(continuationToken, pageSize);

                foreach (var blobPage in resultSegment)
                {
                    foreach (var blobItem in blobPage.Values)
                    {
                        var urlInformation = GetSharedAccessSignatureForBlob(container, blobItem.Name, expireTime);

                        urls.Add(urlInformation);
                    }

                    // Get the continuation token and loop until it is empty.
                    continuationToken = blobPage.ContinuationToken;

                    Console.WriteLine();
                }
            } while (!string.IsNullOrEmpty(continuationToken));
        }
        catch (RequestFailedException exception)
        {
            Log.Error(exception, "Error during blob list");
            throw;
        }

        return Task.FromResult(urls);
    }

    private TemporaryDataUri GetSharedAccessSignatureForBlob(BlobContainerClient container, string filePath, TimeSpan expireTime)
    {
        var blobClient = container.GetBlobClient(filePath);
        var containerName = container.Name == "devstoreaccount1" ? container.Uri.Segments[2] : container.Name;

        var expiresOn = DateTimeOffset.Now.Add(expireTime);

        var builder = new BlobSasBuilder
        {
            BlobName = filePath,
            BlobContainerName = containerName,
            Resource = "b",
            StartsOn = DateTimeOffset.Now,
            ExpiresOn = expiresOn
        };

        builder.SetPermissions(BlobSasPermissions.Read);
        var queryParameters = builder.ToSasQueryParameters(storageSharedKeyCredential);

        var sasUri = new UriBuilder(blobClient.Uri)
        {
            Query = queryParameters.ToString()
        };

        return new TemporaryDataUri
        {
            DataUri = sasUri.ToString(),
            ExpiresOn = expiresOn,
            FileName = filePath.Split("/")[^1]
        };
    }
}