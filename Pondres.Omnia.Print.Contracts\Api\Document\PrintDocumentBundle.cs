﻿using System;

namespace Pondres.Omnia.Print.Contracts.Api.Document
{
    public class PrintDocumentBundle
    {
        public Guid BundleId { get; set; }
        public string BatchName { get; set; } = string.Empty;
        public DateTimeOffset CreatedOn { get; set; }
        public string? GordNumber { get; set; }
        public int? TaskNumber { get; set; }
        public int SequenceId { get; set; }
    }
}
