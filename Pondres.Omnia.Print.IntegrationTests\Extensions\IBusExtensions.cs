﻿using FluentAssertions;
using MassTransit;
using Pondres.Omnia.OrderHub.IntegrationTests.Helper;
using Pondres.Omnia.Print.IntegrationTests.Observers;

namespace Pondres.Omnia.Print.IntegrationTests.Extensions;

public static class IBusExtensions
{
    public static async Task<TMessageType> PublishAndWaitForConsumeAsync<TMessageType>(this IBus bus, TMessageType message, Func<TMessageType, bool>? filter = null) where TMessageType : class
    {
        var observer = new TestConsumeObserver<TMessageType>(filter);
        using var observerHandle = bus.ConnectConsumeMessageObserver(observer);

        await bus.Publish(message);

        await TestHelper.WaitForAsync(() =>
        {
            observer.MessagePreConsumed.Should().BeTrue();

            return Task.CompletedTask;
        });

        return await observer.MessageConsumed;
    }

    public static TestSendObserver<TMessage> CreateSendObserver<TMessage>(this IBus bus) where TMessage : class
    {
        var observer = new TestSendObserver<TMessage>();
        bus.ConnectSendObserver(observer);

        return observer;
    }

    public static TestPublishObserver<TMessage> CreatePublishObserver<TMessage>(this IBus bus) where TMessage : class
    {
        var observer = new TestPublishObserver<TMessage>();
        bus.ConnectPublishObserver(observer);

        return observer;
    }
}
