﻿using MassTransit;
using Pondres.Omnia.Print.Business.Service;
using Pondres.Omnia.Print.Business.StateMachine.PrintBundle.Model;
using System;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Activities.Bundle;

public class PrintBundleStartActivity : BaseInstanceActivity<PrintBundleState>
{
    private readonly IPrintBundleEventService printBundleEventService;

    public PrintBundleStartActivity(IPrintBundleEventService printBundleEventService)
    {
        this.printBundleEventService = printBundleEventService;
    }

    public override string ActivityName => "print-bundle-start";

    protected override async Task ExecuteAsync(BehaviorContext<PrintBundleState> context)
    {
        var state = context.Saga;

        await printBundleEventService.RegisterBundleStartedAsync(new Contracts.PrintBundle.PrintBundleStarted
        {
            BatchName = state.BatchName,
            PrintBundleId = state.CorrelationId,
            Customer = state.Customer,
            DocumentCount = state.DocumentsTotalRecordCount,
            Message = "Started",
            Status = state.CurrentState,
            PageCount = state.DocumentsTotalPageCount,
            Timestamp = DateTimeOffset.Now
        });
    }
}