﻿using Newtonsoft.Json;
using Solude.StorageBase.Model;
using Pondres.Omnia.Print.Contracts.Common;
using Pondres.Omnia.Print.Storage.Configuration;
using Pondres.Omnia.Print.Storage.Entities.Print;
using System;
using System.Collections.Generic;

namespace Pondres.Omnia.Print.Storage.Entities.PrintBundle;

public class PrintBundleBasicEntity : CosmosEntity
{
    [JsonProperty("batchName")]
    public string BatchName { get; set; } = string.Empty;

    [JsonProperty("createdOn")]
    public DateTimeOffset CreatedOn { get; set; }

    [JsonProperty("customer")]
    public string Customer { get; set; } = string.Empty;

    [JsonProperty("quantities")]
    public PrintBundleQuantitiesEntity Quantities { get; set; } = new();

    [JsonProperty("files")]
    public List<string> Files { get; set; } = [];

    [JsonProperty("initiatedBy")]
    public string InitiatedBy { get; set; } = string.Empty;

    [JsonProperty("metadata")] public PrintDocumentMetadataEntity Metadata { get; set; } = new();

    [JsonProperty("gordNumber")]
    public string? GordNumber { get; set; }

    [JsonProperty("taskNumber")]
    public int? TaskNumber { get; set; }

    public override string PartitionKeyValue => Type;

    [JsonProperty("hash")]
    public string Hash { get; set; } = string.Empty;

    [JsonProperty("plannedOn")]
    public DateTimeOffset? PlannedOn { get; set; } = null;

    [JsonProperty("releasedOn")]
    public DateTimeOffset? ReleasedOn { get; set; } = null;

    [JsonProperty("scheduleName")]
    public string? ScheduleName { get; set; }

    [JsonProperty("bundleMode")]
    public string BundleMode { get; set; } = string.Empty;

    /// <summary>
    /// This is null if we are the primary
    /// </summary>
    [JsonProperty("primaryBundleId")]
    public string? PrimaryBundleId { get; set; }

    [JsonProperty("secondaryBundleId")]
    public HashSet<string> SecondaryBundleIds { get; set; } = [];

    [JsonIgnore]
    public PrintBundleStateType State { get; set; }

    [JsonProperty("state")]
    public string StateName
    {
        get { return State.ToString(); }
        set { State = (PrintBundleStateType)Enum.Parse(typeof(PrintBundleStateType), value); }
    }

    [JsonProperty("isDirectlyCompleted")]
    public bool IsDirectlyCompleted { get; set; }

    [JsonProperty("type")]
    public string Type => CosmosConfiguration.PrintBundlePartitionValue;

    [JsonProperty("version")]
    public override string Version { get; set; } = CosmosConfiguration.PrintBundleVersion;
}