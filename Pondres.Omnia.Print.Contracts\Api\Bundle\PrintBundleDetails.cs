﻿using Pondres.Omnia.Print.Contracts.Common;
using System;
using System.Collections.Generic;

namespace Pondres.Omnia.Print.Contracts.Api.Bundle
{
    public class PrintBundleDetails
    {
        public string BatchName { get; set; } = string.Empty;
        public DateTimeOffset CreatedOn { get; set; }
        public DateTimeOffset? PlannedOn { get; set; }
        public DateTimeOffset? ReleasedOn { get; set; }
        public string Customer { get; set; } = string.Empty;
        public int Documents { get; set; }
        public int TotalPageCount { get; set; }
        public string Id { get; set; } = string.Empty;
        public string InitiatedBy { get; set; } = string.Empty;
        public string? GordNumber { get; set; }
        public int TaskNumber { get; set; }
        public PrintDocumentCreationMetadata Metadata { get; set; } = new();
        public int Sheets { get; set; }
        public PrintBundleStatus Status { get; set; } = new();
        public List<PrintBundleStatus> StatusHistory { get; set; } = new();
        public List<PrintBundleManualEvent> ManualEvents { get; set; } = new();
        public List<string> Files { get; set; } = new();

        public List<string> SecondaryBundles { get; set; } = new();
        public bool IsDirectlyCompleted { get; set; }
    }
}
