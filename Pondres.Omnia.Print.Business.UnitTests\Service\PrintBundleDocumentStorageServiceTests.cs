﻿using Moq.AutoMock;
using Moq;
using Pondres.Omnia.Print.Business.Service;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using Pondres.Omnia.Print.Business.Helper;
using FluentAssertions;
using Pondres.Omnia.Print.Storage.PrintBundle;
using Pondres.Omnia.Print.Contracts.PrintBundle;
using MassTransit;
using System.Threading;
using Pondres.Omnia.Print.Business.UnitTests.Helper;
using StackExchange.Redis;

namespace Pondres.Omnia.Print.Business.UnitTests.Service;

public class PrintBundleDocumentStorageServiceTests
{
    private readonly AutoMocker mocker;
    private readonly PrintBundleDocumentStorageService service;
    private readonly Mock<IRedisCacheHelper> redisCacheHelperMock;
    private readonly Mock<IBus> busMock;
    private readonly Mock<IDatabase> redisDatabase;

    public PrintBundleDocumentStorageServiceTests()
    {
        mocker = new AutoMocker(MockBehavior.Strict);
        service = mocker.CreateInstance<PrintBundleDocumentStorageService>();

        redisCacheHelperMock = mocker.GetMock<IRedisCacheHelper>();
        redisDatabase = mocker.GetMock<IDatabase>();
        busMock = mocker.GetMock<IBus>();
    }

    [Fact]
    public async Task SaveDocumentsForPrintBundleAsync_ShouldRegisterBundle()
    {
        // Arrange
        var printBundleId = Guid.NewGuid();
        var documentId1 = Guid.NewGuid().ToString();
        var documentId2 = Guid.NewGuid().ToString();
        var documentId3 = Guid.NewGuid().ToString();

        var documents = new PrintBundleQueuedDocumentHashSet
        {
            TestPrintBundleHelper.CreateQueuedDocument(documentId1),
            TestPrintBundleHelper.CreateQueuedDocument(documentId2)
        };

        var newDocuments = new PrintBundleQueuedDocumentHashSet
        {
            TestPrintBundleHelper.CreateQueuedDocument(documentId3)
        };

        var documentsFromCache = new PrintBundleDocumentCollection { Documents = documents, Started = false };

        redisCacheHelperMock
            .Setup(x => x.LockAndGetAsync($"PrintService_PrintBundleDocuments_{printBundleId}", It.IsAny<PrintBundleDocumentCollection>()))
            .ReturnsAsync(new RedisLock<PrintBundleDocumentCollection>(redisDatabase.Object, new StackExchange.Redis.RedisKey(), TimeSpan.FromSeconds(30)) { Value = documentsFromCache });

        redisCacheHelperMock
            .Setup(x => x.SaveAsync(It.IsAny<RedisLock<PrintBundleDocumentCollection>>()))
            .Returns(Task.CompletedTask);

        busMock
            .Setup(x => x.Publish(It.IsAny<PrintBundleQuantitiesUpdated>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await service.AddDocumentsToBundleAsync(printBundleId, newDocuments.ToList());

        // Assert
        redisCacheHelperMock.Verify(x => x.LockAndGetAsync($"PrintService_PrintBundleDocuments_{printBundleId}", It.IsAny<PrintBundleDocumentCollection>()), Times.Once);
        redisCacheHelperMock.Verify(x => x.SaveAsync(It.IsAny<RedisLock<PrintBundleDocumentCollection>>()), Times.Once);
        result.AddedCount.Should().Be(newDocuments.Count);
        result.Success.Should().BeTrue();

        documentsFromCache.Documents.Count.Should().Be(3);

        mocker.VerifyAll();
    }

    [Fact]
    public async Task SaveDocumentsForPrintBundleAsync_ShouldSkipDuplicateDocuments_AndRegisterBundle()
    {
        // Arrange
        var printBundleId = Guid.NewGuid();
        var documentId1 = Guid.NewGuid().ToString();
        var documentId2 = Guid.NewGuid().ToString();
        var documentId3 = Guid.NewGuid().ToString();
        var documentId4 = Guid.NewGuid().ToString();

        var documents = new PrintBundleQueuedDocumentHashSet
        {
            TestPrintBundleHelper.CreateQueuedDocument(documentId1),
            TestPrintBundleHelper.CreateQueuedDocument(documentId2),
            TestPrintBundleHelper.CreateQueuedDocument(documentId3)
        };

        var newDocuments = new PrintBundleQueuedDocumentHashSet
        {
            TestPrintBundleHelper.CreateQueuedDocument(documentId3),
            TestPrintBundleHelper.CreateQueuedDocument(documentId4)
        };

        var documentsFromCache = new PrintBundleDocumentCollection { Documents = documents, Started = false };

        redisCacheHelperMock
            .Setup(x => x.LockAndGetAsync($"PrintService_PrintBundleDocuments_{printBundleId}", It.IsAny<PrintBundleDocumentCollection>()))
            .ReturnsAsync(new RedisLock<PrintBundleDocumentCollection>(redisDatabase.Object, new RedisKey(), TimeSpan.FromSeconds(30)) { Value = documentsFromCache });

        redisCacheHelperMock.Setup(x => x.SaveAsync(It.IsAny<RedisLock<PrintBundleDocumentCollection>>())).Returns(Task.CompletedTask);

        busMock
            .Setup(x => x.Publish(It.IsAny<PrintBundleQuantitiesUpdated>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await service.AddDocumentsToBundleAsync(printBundleId, newDocuments.ToList());

        // Assert
        redisCacheHelperMock.Verify(x => x.LockAndGetAsync($"PrintService_PrintBundleDocuments_{printBundleId}", It.IsAny<PrintBundleDocumentCollection>()), Times.Once);
        redisCacheHelperMock.Verify(x => x.SaveAsync(It.IsAny<RedisLock<PrintBundleDocumentCollection>>()), Times.Once);
        result.AddedCount.Should().Be(1);
        result.Success.Should().BeTrue();

        documentsFromCache.Documents.Count.Should().Be(4);

        mocker.VerifyAll();
    }

    [Fact]
    public async Task RemoveDocumentsForPrintBundleAsync_ShouldRemoveDocumentFromBundle()
    {
        // Arrange
        var printBundleId = Guid.NewGuid();
        var documentId1 = Guid.NewGuid().ToString();
        var documentId2 = Guid.NewGuid().ToString();
        var documentId3 = Guid.NewGuid().ToString();

        var documents = new PrintBundleQueuedDocumentHashSet
        {
            TestPrintBundleHelper.CreateQueuedDocument(documentId1),
            TestPrintBundleHelper.CreateQueuedDocument(documentId2),
            TestPrintBundleHelper.CreateQueuedDocument(documentId3)
        };

        var documentsFromCache = new PrintBundleDocumentCollection { Documents = documents };

        var documentToRemove = documentsFromCache.Documents.First(x => x.DocumentMetadata.DocumentId == documentId3);

        redisCacheHelperMock
            .Setup(x => x.LockAndGetAsync($"PrintService_PrintBundleDocuments_{printBundleId}", It.IsAny<PrintBundleDocumentCollection>()))
            .ReturnsAsync(new RedisLock<PrintBundleDocumentCollection>(redisDatabase.Object, new StackExchange.Redis.RedisKey(), TimeSpan.FromSeconds(30)) { Value = documentsFromCache });

        redisCacheHelperMock.Setup(x => x.SaveAsync(It.IsAny<RedisLock<PrintBundleDocumentCollection>>())).Returns(Task.CompletedTask);

        busMock
            .Setup(x => x.Publish(It.IsAny<PrintBundleQuantitiesUpdated>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var removed = await service.RemoveDocumentFromBundleAsync(printBundleId, documentToRemove.DocumentMetadata.DocumentId, true);

        // Assert
        redisCacheHelperMock.Verify(x => x.LockAndGetAsync($"PrintService_PrintBundleDocuments_{printBundleId}", It.IsAny<PrintBundleDocumentCollection>()), Times.Once);
        redisCacheHelperMock.Verify(x => x.SaveAsync(It.IsAny<RedisLock<PrintBundleDocumentCollection>>()), Times.Once);
        removed.Removed.Should().Be(true);

        mocker.VerifyAll();
    }

    [Fact]
    public async Task GetDocumentsForPrintBundleAsync_ShouldReturnDocuments()
    {
        // Arrange
        var printBundleId = Guid.NewGuid();
        var documentId1 = Guid.NewGuid().ToString();
        var documentId2 = Guid.NewGuid().ToString();

        var documents = new PrintBundleQueuedDocumentHashSet
        {
            TestPrintBundleHelper.CreateQueuedDocument(documentId1),
            TestPrintBundleHelper.CreateQueuedDocument(documentId2),
        };

        var documentsFromCache = new PrintBundleDocumentCollection { Documents = documents };

        redisCacheHelperMock
            .Setup(x => x.GetAsync(It.IsAny<string>(), It.IsAny<PrintBundleDocumentCollection>()))
            .ReturnsAsync(documentsFromCache);

        // Act
        var result = await service.GetDocumentsForBundleAsync(printBundleId);

        // Assert
        result.Should().HaveCount(2);
        result.Should().Contain(x => x.DocumentMetadata.DocumentId == documentId1);
        result.Should().Contain(x => x.DocumentMetadata.DocumentId == documentId2);

        mocker.VerifyAll();
    }
}
