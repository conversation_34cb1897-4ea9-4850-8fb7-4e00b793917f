﻿using Microsoft.Extensions.Hosting;
using Pondres.Omnia.Print.Business.Service;
using Serilog;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.PrintManager.Services;

public class LocalFileStorePeriodicCleanupHostedService(PrintDocumentLocalFileStoreService printDocumentLocalFileStoreService) : BackgroundService
{
    private readonly TimeSpan interval = TimeSpan.FromHours(1);

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        using var timer = new PeriodicTimer(interval);

        do
        {
            try
            {
                await printDocumentLocalFileStoreService.CleanupLocalStorageForCompletedBundlesAsync();
            }
            catch (Exception exception)
            {
                Log.Error(exception, "Failed to run the {ServiceName}", nameof(LocalFileStorePeriodicCleanupHostedService));
            }
        }
        while (!stoppingToken.IsCancellationRequested && await timer.WaitForNextTickAsync(stoppingToken));
    }
}
