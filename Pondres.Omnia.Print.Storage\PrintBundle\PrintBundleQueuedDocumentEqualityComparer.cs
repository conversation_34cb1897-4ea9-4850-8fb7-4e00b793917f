﻿using Pondres.Omnia.Print.Contracts.PrintBundle;
using System.Collections.Generic;

namespace Pondres.Omnia.Print.Storage.PrintBundle;

public class PrintBundleQueuedDocumentEqualityComparer : IEqualityComparer<PrintBundleQueuedDocument>
{
    public bool Equals(PrintBundleQueuedDocument? x, PrintBundleQueuedDocument? y)
    {
        if (x == null && y == null)
        {
            return true;
        }

        if (x == null || y == null)
        {
            return false;
        }

        return x.DocumentMetadata.DocumentId == y.DocumentMetadata.DocumentId;
    }

    public int GetHashCode(PrintBundleQueuedDocument obj)
        => obj.DocumentMetadata.DocumentId.ToString().GetHashCode();
}