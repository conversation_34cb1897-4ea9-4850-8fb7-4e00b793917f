﻿using MassTransit;
using Pondres.Omnia.Print.Business.Service;
using Pondres.Omnia.Print.Business.StateMachine.PrintBundle.Model;
using Pondres.Omnia.Print.Contracts.PrintBundle;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Activities.Bundle;

public class PrintBundleStartSecondaryBundlesActivity : BaseInstanceActivity<PrintBundleState>
{
    private readonly IPrintBundleService printBundleService;

    public PrintBundleStartSecondaryBundlesActivity(IPrintBundleService printBundleService)
    {
        this.printBundleService = printBundleService;
    }

    public override string ActivityName => "print-bundle-start";

    protected override async Task ExecuteAsync(BehaviorContext<PrintBundleState> context)
    {
        var bundle = await printBundleService.GetBundleDetailsAsync(context.Saga.CorrelationId);

        var startMessages = bundle.SecondaryBundles.Select(bundleId => new PrintBundleReleaseRequest
        {
            BundleOptions = null,
            PrintBundleId = Guid.Parse(bundleId),
            ReleaseDateTime = context.Saga.ReleasedOn,
            Username = "System",
            GordNumber = bundle.GordNumber,
            TaskNumber = bundle.TaskNumber,
        }).ToList();

        if (startMessages.Count != 0)
            await context.PublishBatch(startMessages);
    }
}