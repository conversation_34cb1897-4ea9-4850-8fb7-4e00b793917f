﻿using System;
using Pondres.Omnia.Print.Contracts.Common;

namespace Pondres.Omnia.Print.Contracts.PrintDocument
{
    public class PrintDocumentQueued
    {
        public OrderMetadata OrderMetadata { get; set; } = new();
        public PrintDocumentMetadata DocumentMetadata { get; set; } = new();
        public DateTimeOffset MailDate { get; set; }
        public Guid PrintBundleId { get; set; }

        public DateTimeOffset Timestamp { get; set; }
    }
}