﻿using Pondres.Omnia.Print.Common.Model;
using Pondres.Omnia.Print.Contracts.Api.Document;
using Pondres.Omnia.Print.Storage.Entities.PrintDocument;
using System.Collections.Generic;
using System.Linq;

namespace Pondres.Omnia.Print.Business.Extensions;

public static class PrintDocumentEntityExtensions
{
    public static IEnumerable<PrintDocumentListItem> ToListItemDto(this IEnumerable<PrintDocumentEntity> entities) =>
        entities.Select(x => x.ToListItemDto());

    public static PrintDocumentListItem ToListItemDto(this PrintDocumentEntity entity) =>
        new()
        {
            LastUpdatedOn = entity.LastUpdatedOn,
            CreatedOn = entity.CreatedOn,
            CustomerDocumentReference = entity.CustomerDocumentReference,
            IsPrimary = entity.PrimaryDocumentId == entity.Id,
            Id = entity.Id,
            OrderMetadata = entity.OrderMetadata.ToOrderMetadata(),
            LastBundle = entity.LastBundle?.ToPrintDocumentBundle()
        };

    public static PrintDocumentCreationModel ToPrintDocumentCreationModel(this PrintDocumentEntity entity) =>
        new(documentId: entity.Id,
            primaryDocumentId: entity.PrimaryDocumentId,
            orderMetadata: entity.OrderMetadata.ToOrderMetadata(),
            fileInformation: entity.FileInformation.ToPrintDocumentFileInformation());
}