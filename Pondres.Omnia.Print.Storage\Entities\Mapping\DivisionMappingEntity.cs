﻿using Newtonsoft.Json;
using Solude.StorageBase.Model;
using Pondres.Omnia.Print.Storage.Configuration;

namespace Pondres.Omnia.Print.Storage.Entities.Mapping;

public class DivisionMappingEntity : CosmosEntity
{
    [JsonProperty("division")] public string Division { get; set; } = string.Empty;

    [JsonProperty("type")]
    public static string Type => CosmosConfiguration.DivisionMappingPartitionValue;

    [JsonIgnore]
    public override string PartitionKeyValue => Type;

    [JsonProperty("version")]
    public override string Version { get; set; } = CosmosConfiguration.DivisionMappingVersion;

}
