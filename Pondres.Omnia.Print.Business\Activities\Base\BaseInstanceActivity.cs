﻿using MassTransit;
using System;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Activities
{
    public abstract class BaseInstanceActivity<TInstance> : IStateMachineActivity<TInstance>
        where TInstance : class, SagaStateMachineInstance
    {
        public abstract string ActivityName { get; }

        public void Accept(StateMachineVisitor visitor)
        {
            visitor.Visit(this);
        }

        public async Task Execute<T>(BehaviorContext<TInstance, T> context, IBehavior<TInstance, T> next) where T : class
        {
            await ExecuteAsync(context);

            await next.Execute(context).ConfigureAwait(false);
        }

        public async Task Execute(BehaviorContext<TInstance> context, IBehavior<TInstance> next)
        {
            await ExecuteAsync(context);

            await next.Execute(context).ConfigureAwait(false);
        }

        public async Task Faulted<T, TException>(BehaviorExceptionContext<TInstance, T, TException> context, I<PERSON>ehavior<TInstance, T> next)
            where T : class
            where TException : Exception
        {
            await next.Faulted(context).ConfigureAwait(false);
        }

        public async Task Faulted<TException>(BehaviorExceptionContext<TInstance, TException> context, IBehavior<TInstance> next) where TException : Exception
        {
            await next.Faulted(context).ConfigureAwait(false);
        }

        public void Probe(ProbeContext context)
        {
            context.CreateScope(ActivityName);
        }

        protected abstract Task ExecuteAsync(BehaviorContext<TInstance> context);
    }
}