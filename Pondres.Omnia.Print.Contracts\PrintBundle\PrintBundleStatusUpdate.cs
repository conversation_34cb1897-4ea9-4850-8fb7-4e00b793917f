﻿using System;

namespace Pondres.Omnia.Print.Contracts.PrintBundle
{
    public class PrintBundleStatusUpdate
    {
        public bool IsInFailedState { get; set; }
        public string Message { get; set; } = string.Empty;
        public Guid PrintBundleId { get; set; }

        public string Status { get; set; } = string.Empty;
        public DateTimeOffset Timestamp { get; set; }
        public bool WaitingForInput { get; set; }
        public string BatchName { get; set; } = string.Empty;
        public string Customer { get; set; } = string.Empty;
    }
}