﻿using MassTransit;
using Pondres.Omnia.Print.Contracts.PrintBundle;
using Pondres.Omnia.Print.Contracts.PrintDocument;
using Pondres.Omnia.Print.Storage.Repositories;
using Serilog;
using System.Linq;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Consumers;

public class PrintBundleReprintConsumer : IConsumer<PrintBundleReprintRequested>
{
    private readonly IPrintDocumentRepository printDocumentRepository;
    private readonly ILogger logger;

    public PrintBundleReprintConsumer(IPrintDocumentRepository printDocumentRepository, ILogger logger)
    {
        this.printDocumentRepository = printDocumentRepository;
        this.logger = logger;
    }

    public async Task Consume(ConsumeContext<PrintBundleReprintRequested> context)
    {
        logger.Information("PrintBundleReprintConsumer received PrintBundleReprintRequested for PrintBundle {PrintBundleId}", context.Message.PrintBundleId);
        var documents = await printDocumentRepository.GetAllForPrintBundleAsync(context.Message.PrintBundleId);

        var requeueMessages = documents.Select(x => new PrintDocumentRequeueCommand(x.OrderMetadata.Customer, x.OrderMetadata.OrderId));

        await context.PublishBatch(requeueMessages);
    }
}
