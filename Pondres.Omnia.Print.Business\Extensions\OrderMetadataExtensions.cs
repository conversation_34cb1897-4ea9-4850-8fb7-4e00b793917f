﻿using Pondres.Omnia.Print.Contracts.Common;
using Pondres.Omnia.Print.Storage.Entities.PrintDocument;

namespace Pondres.Omnia.Print.Business.Extensions;

public static class OrderMetadataExtensions
{
    public static OrderMetadataEntity ToOrderMetadataEntity(this OrderMetadata model) =>
        new()
        {
            Flow = model.Flow,
            Customer = model.Customer,
            CustomerReference = model.CustomerReference,
            OrderId = model.OrderId,
            Categories = new OrderCategoriesEntity
            {
                One = model.Categories.One,
                Two = model.Categories.Two,
                Three = model.Categories.Three,
            }
        };
}