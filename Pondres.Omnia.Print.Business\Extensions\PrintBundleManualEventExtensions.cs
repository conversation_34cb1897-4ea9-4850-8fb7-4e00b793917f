﻿using Pondres.Omnia.Print.Contracts.PrintBundle;
using Pondres.Omnia.Print.Storage.Entities.PrintBundle;

namespace Pondres.Omnia.Print.Business.Extensions;

public static class PrintBundleManualEventExtensions
{
    public static PrintBundleManualEventEntity ToEntity(this PrintBundleManualEvent message)
        => new PrintBundleManualEventEntity
        {
            Event = message.Event,
            Timestamp = message.Timestamp,
            Username = message.Username
        };
}