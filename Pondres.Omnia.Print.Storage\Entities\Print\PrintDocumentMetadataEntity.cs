﻿using Newtonsoft.Json;
using Pondres.Omnia.Print.Contracts.Api.Enums;
using System;
using System.Collections.Generic;

namespace Pondres.Omnia.Print.Storage.Entities.Print;

public class PrintDocumentMetadataEntity
{
    [JsonProperty("carrier")]
    public string Carrier { get; set; } = string.Empty;

    [JsonProperty("documentFormat")]
    public string DocumentFormat { get; set; } = string.Empty;

    [JsonProperty("endLocation")]
    public string EndLocation { get; set; } = string.Empty;

    [JsonProperty("laminate")]
    public bool Laminate { get; set; }

    [JsonProperty("mailDate")]
    public DateTimeOffset MailDate { get; set; }

    [JsonProperty("postalDestination")]
    public string PostalDestination { get; set; } = string.Empty;

    [JsonProperty("printerType")]
    public string PrinterType { get; set; } = string.Empty;

    [JsonProperty("printMode")]
    public string? PrintMode { get; set; }

    [JsonProperty("sheetArticleCode")]
    public string SheetArticleCode { get; set; } = string.Empty;

    [JsonProperty("sheetPageCount")]
    public int SheetPageCount { get; set; }

    [JsonProperty("sheetFormat")]
    public string SheetFormat { get; set; } = string.Empty;

    [JsonProperty("DGCode")]
    public string? DGCode { get; set; }

    [JsonProperty("pageCount")]
    public int PageCount { get; set; }

    [JsonProperty("packaging")]
    public string? Packaging { get; set; }

    [JsonProperty("packingType")]
    public PackingType? PackingType { get; set; }

    [JsonProperty("packingName")]
    public string? PackingName { get; set; }
    [JsonProperty("releaseSchedule")]
    public string? ReleaseSchedule { get; set; }
    [JsonProperty("isPrimary")]
    public bool IsPrimary { get; set; }

    [JsonProperty("sheetArticleDescription")]
    public string? SheetArticleDescription { get; set; }

    [JsonProperty("printBundleSort")]
    public string? PrintBundleSort { get; set; }

    [JsonProperty("attachments")]
    public List<PrintDocumentMetadataAttachmentEntity> Attachments { get; set; } = [];
}