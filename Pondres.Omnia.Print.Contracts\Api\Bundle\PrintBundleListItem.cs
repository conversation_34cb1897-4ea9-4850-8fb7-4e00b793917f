﻿using Pondres.Omnia.Print.Contracts.Common;
using System;
using System.Collections.Generic;

namespace Pondres.Omnia.Print.Contracts.Api.Bundle
{
    public class PrintBundleListItem
    {
        public string BatchName { get; set; } = string.Empty;
        public DateTimeOffset CreatedOn { get; set; }
        public string Customer { get; set; } = string.Empty;
        public int Documents { get; set; }
        public int TotalPageCount { get; set; }
        public string Id { get; set; } = string.Empty;
        public string InitiatedBy { get; set; } = string.Empty;
        public List<PrintBundleInputOption> Actions { get; set; } = new();
        public PrintDocumentCreationMetadata Metadata { get; set; } = new();
        public string? GordNumber { get; set; }
        public int TaskNumber { get; set; }
        public int Sheets { get; set; }
        public PrintBundleStatus Status { get; set; } = new();
        public List<string> Files { get; set; } = new();
        public List<PrintBundleMode> AvailablePrintBundleModes { get; set; } = new();
    }
}