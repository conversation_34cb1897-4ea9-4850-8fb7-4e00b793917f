﻿using FluentAssertions;
using Pondres.Omnia.Print.IntegrationTests.Fixtures;

namespace Pondres.Omnia.OrderHub.IntegrationTests.Tests;

[Collection("IntegrationTests")]
public class StartupTest : BaseIntegrationTest
{
    public StartupTest(IntegrationTestFixture fixture) : base(fixture)
    {
    }

    [Fact]
    public async Task StartupTestAsync()
    {
        // Arrange
        var managerClient = fixture.ManagerApp.CreateClient();
        var workerClient = fixture.WorkerApp.CreateClient();

        // Act
        var managerResponse = await managerClient.GetAsync("/health/ready");
        var workerResponse = await workerClient.GetAsync("/health/ready");

        // Assert
        managerResponse.Should().BeSuccessful();
        workerResponse.Should().BeSuccessful();
    }
}
