﻿using Pondres.Omnia.Print.Common.Model;
using Pondres.Omnia.Print.Contracts.Common;
using System;

namespace Pondres.Omnia.Print.Business.Model;

public class PrintDocumentCreationPair
{
    public PrintDocumentCreationPair(PrintDocumentCreationModel creationModel, PrintDocumentCreationMetadata metadata)
    {
        CreationModel = creationModel;
        Metadata = metadata;
    }

    public PrintDocumentCreationModel CreationModel { get; set; }
    public PrintDocumentCreationMetadata Metadata { get; set; }

    public Guid BundleId { get; set; }
    public string BundleHash { get; set; } = string.Empty;
}
