﻿using Pondres.Omnia.Print.Contracts.Api.Enums;
using System.Collections.Generic;

namespace Pondres.Omnia.Print.Contracts.Api.Batch
{
    public class DGCodeBatchDetails
    {
        public List<string> PrintBundleIds { get; set; } = new();
        public PrinterType? PrinterType { get; set; }
        public string PrintFileLocation { get; set; } = string.Empty;
        public int Sheets { get; set; }
        public int DocumentCount { get; set; }
        public string Status { get; set; } = string.Empty;
        public List<PrinterDetails> PrinterDetails { get; set; } = new();
    }
}
