﻿using MassTransit;
using Pondres.Omnia.Print.Business.Helper;
using Pondres.Omnia.Print.Business.Model;
using Pondres.Omnia.Print.Contracts.PrintBatch;
using Pondres.Omnia.Print.Contracts.PrintBundle;
using Pondres.Omnia.Print.Integrations.Navision.Mappings;
using Pondres.Omnia.Print.Integrations.Navision.Model;
using Pondres.Omnia.Print.Integrations.Navision.Provider;
using Pondres.Omnia.Print.Integrations.Navision.Service;
using Pondres.Omnia.Print.Navision.Model;
using Pondres.Omnia.Print.Storage.Entities.PrintBundle;
using Pondres.Omnia.Print.Storage.Repositories;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Handler.PrintBatch;

public class PrintBatchOrchestrationHandler : IPrintBatchOrchestrationHandler
{
    private readonly IPrintBundleBasicRepository printBundleRepository;
    private readonly IPrintBundleDetailsRepository printBundleDetailsRepository;
    private readonly INavisionDataProvider navisionDataProvider;
    private readonly INavisionIntegrationService navisionIntegrationService;
    private readonly IRedisCacheHelper redisCacheHelper;

    private readonly TimeSpan expirationTime = TimeSpan.FromMinutes(10);
    private static readonly SemaphoreSlim clientAddSemaphore = new(1, 1);

    public PrintBatchOrchestrationHandler(
        IPrintBundleBasicRepository printBundleRepository,
        IPrintBundleDetailsRepository printBundleDetailsRepository,
        INavisionDataProvider navisionDataProvider,
        INavisionIntegrationService navisionIntegrationService,
        IRedisCacheHelper redisCacheHelper)
    {
        this.printBundleRepository = printBundleRepository;
        this.printBundleDetailsRepository = printBundleDetailsRepository;
        this.navisionDataProvider = navisionDataProvider;
        this.navisionIntegrationService = navisionIntegrationService;
        this.redisCacheHelper = redisCacheHelper;
    }

    public async Task AddDataToBatchAsync(IPublishEndpoint context, PrintBundleTaskAssignRequest request)
    {
        await clientAddSemaphore.WaitAsync();

        try
        {
            var gordNumber = string.Empty;
            var taskNumber = 0;

            // Check if bundle has excluded location. If it has we do not send it to navision and do not want to up the task number
            var primaryBundle = (PrintBundleBasicEntity)await printBundleRepository.GetSingleAsync(request.PrintBundleId.ToString());
            var secondaryBundles = await printBundleRepository.GetAllSecondaryPrintBundlesAsync(request.PrintBundleId.ToString());

            var allBundleIds = secondaryBundles.Select(x => x.Id).ToList();
            allBundleIds.Add(primaryBundle.Id);

            var isExcludedEndLocation = NavisionDataMappings.ExcludedEndLocations.Contains(primaryBundle.Metadata.EndLocation);

            if (!isExcludedEndLocation && request.DGCode != null)
            { 
                var cachedGordAndTaskNumber = await GetCachedGordAndTaskNumberAsync(request.DGCode);

                if (cachedGordAndTaskNumber != null)
                {
                    gordNumber = cachedGordAndTaskNumber.GordNumber;
                    taskNumber = cachedGordAndTaskNumber.TaskNumber;
                }
                else
                {
                    var (navisionGordNumber, navisionTaskNumber) = await GetGordAndTaskNumberAsync(request.DGCode);
                    gordNumber = navisionGordNumber;
                    taskNumber = navisionTaskNumber;
                }

                // Up tasknumber when it has no excluded location
                ++taskNumber;

                // Send to navision
                var sendToNavisionRequest = CreateSendToNavisionRequestBundle(request, primaryBundle, secondaryBundles, gordNumber, taskNumber, request.DGCode);
                navisionIntegrationService.SendPrintBundleXmlToNavision(sendToNavisionRequest);
                await redisCacheHelper.SetGordAndTaskNumberAsync(request.DGCode, gordNumber, taskNumber, expirationTime);
            }

            await SetGordAndTaskNumberForAllRelatedBundlesAsync(allBundleIds, gordNumber, taskNumber);

            // Let the bundle state know we done it
            await PublishAddedBundleAsync(
                context,
                gordNumber: gordNumber,
                taskNumber: taskNumber, // Task number is the one we just upped (or default when bundle has excluded location)
                request.PrintBundleId);
        }
        catch (Exception exception)
        {
            Log.Error(exception, "Error adding bundle to batch");

            await PublishFailureAsync(context, request, exception);
        }
        finally
        {
            clientAddSemaphore.Release();
        }
    }

    private async Task SetGordAndTaskNumberForAllRelatedBundlesAsync(List<string> allBundleIds, string gordNumber, int taskNumber)
    {
        foreach (var bundleId in allBundleIds)
        {
            var bundle = await printBundleDetailsRepository.GetSingleAsync(bundleId);

            static bool UpdateBundle(PrintBundleDetailsEntity bundle, string gordNumber, int taskNumber)
            {
                bundle.GordNumber = gordNumber;
                bundle.TaskNumber = taskNumber;
                return true;
            }

            if (UpdateBundle(bundle, gordNumber, taskNumber))
                await printBundleDetailsRepository.ReplaceConsistentAsync(bundle, updateActionRetry: x => UpdateBundle(x, gordNumber, taskNumber));
        }
    }

    private static SendBundleInfoToNavisionRequestBundle CreateSendToNavisionRequestBundle(
        PrintBundleTaskAssignRequest command,
        PrintBundleBasicEntity primaryBundle,
        List<PrintBundleBasicEntity> secondaryBundles,
        string gordNumber,
        int lastTaskNumber,
        string dGCode)
    {
        var sendToNavisionRequest = CreateSendToNavisionRequest(command, primaryBundle, gordNumber, lastTaskNumber, dGCode);

        var secondarySendToNavisionRequests = secondaryBundles.Select(x => CreateSendToNavisionRequest(command, x, gordNumber, lastTaskNumber, dGCode)).ToList();

        var sendToNavisionRequestBundle = new SendBundleInfoToNavisionRequestBundle(sendToNavisionRequest, secondarySendToNavisionRequests);

        return sendToNavisionRequestBundle;
    }

    private static SendBundleInfoToNavisionRequest CreateSendToNavisionRequest(PrintBundleTaskAssignRequest command, PrintBundleBasicEntity bundle, string gordNumber, int lastTaskNumber, string dGCode)
    {
        var sendToNavisionRequest = new SendBundleInfoToNavisionRequest
        {
            // map properties from PrintBundleBasicEntity
            BatchName = bundle.BatchName,
            GordNumber = gordNumber,
            TaskNumber = lastTaskNumber,
            BundleMetadata = bundle.Metadata,
            DocumentCount = command.DocumentCount,
            DGCode = dGCode,
            SheetCount = command.SheetCount
        };
        return sendToNavisionRequest;
    }

    private static async Task PublishFailureAsync(IPublishEndpoint context, PrintBundleTaskAssignRequest request, Exception exception)
    {
        await context.Publish(new PrintBundleAssignTaskFailure
        {
            PrintBundleId = request.PrintBundleId,
            Message = exception.Message
        });
    }

    private async Task<GordAndTaskNumber?> GetCachedGordAndTaskNumberAsync(string DGCode)
    {
        var cachedGordAndTaskNumber = await redisCacheHelper.GetGordAndTaskNumberAsync(DGCode);
        return cachedGordAndTaskNumber;
    }

    private static async Task PublishAddedBundleAsync(
        IPublishEndpoint context,
        string gordNumber,
        int taskNumber,
        Guid bundleId)
    {
        await context.Publish(new PrintBundleAssignTaskCompleted
        {
            PrintBundleId = bundleId,
            TaskNumber = taskNumber,
            GordNumber = gordNumber
        });
    }

    private async Task<(string gordNumber, int taskNumber)> GetGordAndTaskNumberAsync(string DGCode)
    {
        var gordAndTaskNumber = await navisionDataProvider.GetGordAndTaskNumberAsync(DGCode);

        var taskNumber = gordAndTaskNumber.TaskNumber.GetValueOrDefault();
        var gordNumber = !string.IsNullOrWhiteSpace(gordAndTaskNumber.GORDNumber) ? gordAndTaskNumber.GORDNumber : string.Empty;

        return (gordNumber, taskNumber);
    }
}
