﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace ExportServiceClient;



[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ServiceModel.ServiceContractAttribute(Namespace="http://localhost/navision//", ConfigurationName="ExportServiceClient.ExportServiceSoap")]
public interface ExportServiceSoap
{
    
    [System.ServiceModel.OperationContractAttribute(Action="http://localhost/navision//MakeCopy", ReplyAction="*")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    System.Threading.Tasks.Task<ExportServiceClient.order> MakeCopyAsync(ExportServiceClient.order ord);
    
    [System.ServiceModel.OperationContractAttribute(Action="http://localhost/navision//SaveOrders", ReplyAction="*")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    System.Threading.Tasks.Task<string> SaveOrdersAsync(ExportServiceClient.order[] orders, string prefix);
    
    [System.ServiceModel.OperationContractAttribute(Action="http://localhost/navision//SaveOrdersLocation", ReplyAction="*")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    System.Threading.Tasks.Task<string> SaveOrdersLocationAsync(ExportServiceClient.order[] orders, string prefix, string location);
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://localhost/navision//")]
public partial class order
{
    
    private string ordernrField;
    
    private string naamField;
    
    private string naam2Field;
    
    private string naam3Field;
    
    private string contactField;
    
    private string straatField;
    
    private string straat2Field;
    
    private string huisnrField;
    
    private string huisnrtoevField;
    
    private string postcodeField;
    
    private string plaatsField;
    
    private string provincieField;
    
    private string landcodeField;
    
    private string landnaamField;
    
    private string telefoonField;
    
    private string telexField;
    
    private string emailField;
    
    private string factuuraanField;
    
    private string divisieField;
    
    private string loginIDField;
    
    private string orderbronField;
    
    private System.DateTime orderdatumField;
    
    private System.DateTime datetimestampField;
    
    private string campagnenrField;
    
    private string ordernrklantField;
    
    private string kenmerk1Field;
    
    private string kenmerk2Field;
    
    private System.Nullable<double> kortingField;
    
    private System.Nullable<double> orderkostenField;
    
    private bool betalingonderwegField;
    
    private string betaalwijzeField;
    
    private string bankrekeningnrField;
    
    private string naamrekeninghouderField;
    
    private string adresrekeninghouderField;
    
    private string postcoderekeninghouderField;
    
    private string plaatsrekeninghouderField;
    
    private string landcoderekeninghouderField;
    
    private System.Nullable<int> sortField;
    
    private string transportField;
    
    private string psppayidField;
    
    private string psppaymethodField;
    
    private string iBANField;
    
    private string bICField;
    
    private System.DateTime verzenddatumField;
    
    private string klantselectiecodeField;
    
    private OrderLine[] orderlineField;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string ordernr
    {
        get
        {
            return this.ordernrField;
        }
        set
        {
            this.ordernrField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public string naam
    {
        get
        {
            return this.naamField;
        }
        set
        {
            this.naamField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public string naam2
    {
        get
        {
            return this.naam2Field;
        }
        set
        {
            this.naam2Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=3)]
    public string naam3
    {
        get
        {
            return this.naam3Field;
        }
        set
        {
            this.naam3Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=4)]
    public string contact
    {
        get
        {
            return this.contactField;
        }
        set
        {
            this.contactField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=5)]
    public string straat
    {
        get
        {
            return this.straatField;
        }
        set
        {
            this.straatField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=6)]
    public string straat2
    {
        get
        {
            return this.straat2Field;
        }
        set
        {
            this.straat2Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=7)]
    public string huisnr
    {
        get
        {
            return this.huisnrField;
        }
        set
        {
            this.huisnrField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=8)]
    public string huisnrtoev
    {
        get
        {
            return this.huisnrtoevField;
        }
        set
        {
            this.huisnrtoevField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=9)]
    public string postcode
    {
        get
        {
            return this.postcodeField;
        }
        set
        {
            this.postcodeField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=10)]
    public string plaats
    {
        get
        {
            return this.plaatsField;
        }
        set
        {
            this.plaatsField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=11)]
    public string provincie
    {
        get
        {
            return this.provincieField;
        }
        set
        {
            this.provincieField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=12)]
    public string landcode
    {
        get
        {
            return this.landcodeField;
        }
        set
        {
            this.landcodeField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=13)]
    public string landnaam
    {
        get
        {
            return this.landnaamField;
        }
        set
        {
            this.landnaamField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=14)]
    public string telefoon
    {
        get
        {
            return this.telefoonField;
        }
        set
        {
            this.telefoonField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=15)]
    public string telex
    {
        get
        {
            return this.telexField;
        }
        set
        {
            this.telexField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=16)]
    public string email
    {
        get
        {
            return this.emailField;
        }
        set
        {
            this.emailField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=17)]
    public string factuuraan
    {
        get
        {
            return this.factuuraanField;
        }
        set
        {
            this.factuuraanField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=18)]
    public string divisie
    {
        get
        {
            return this.divisieField;
        }
        set
        {
            this.divisieField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=19)]
    public string loginID
    {
        get
        {
            return this.loginIDField;
        }
        set
        {
            this.loginIDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=20)]
    public string orderbron
    {
        get
        {
            return this.orderbronField;
        }
        set
        {
            this.orderbronField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=21)]
    public System.DateTime orderdatum
    {
        get
        {
            return this.orderdatumField;
        }
        set
        {
            this.orderdatumField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=22)]
    public System.DateTime datetimestamp
    {
        get
        {
            return this.datetimestampField;
        }
        set
        {
            this.datetimestampField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=23)]
    public string campagnenr
    {
        get
        {
            return this.campagnenrField;
        }
        set
        {
            this.campagnenrField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=24)]
    public string ordernrklant
    {
        get
        {
            return this.ordernrklantField;
        }
        set
        {
            this.ordernrklantField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=25)]
    public string kenmerk1
    {
        get
        {
            return this.kenmerk1Field;
        }
        set
        {
            this.kenmerk1Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=26)]
    public string kenmerk2
    {
        get
        {
            return this.kenmerk2Field;
        }
        set
        {
            this.kenmerk2Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=27)]
    public System.Nullable<double> korting
    {
        get
        {
            return this.kortingField;
        }
        set
        {
            this.kortingField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=28)]
    public System.Nullable<double> orderkosten
    {
        get
        {
            return this.orderkostenField;
        }
        set
        {
            this.orderkostenField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=29)]
    public bool betalingonderweg
    {
        get
        {
            return this.betalingonderwegField;
        }
        set
        {
            this.betalingonderwegField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=30)]
    public string betaalwijze
    {
        get
        {
            return this.betaalwijzeField;
        }
        set
        {
            this.betaalwijzeField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=31)]
    public string bankrekeningnr
    {
        get
        {
            return this.bankrekeningnrField;
        }
        set
        {
            this.bankrekeningnrField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=32)]
    public string naamrekeninghouder
    {
        get
        {
            return this.naamrekeninghouderField;
        }
        set
        {
            this.naamrekeninghouderField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=33)]
    public string adresrekeninghouder
    {
        get
        {
            return this.adresrekeninghouderField;
        }
        set
        {
            this.adresrekeninghouderField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=34)]
    public string postcoderekeninghouder
    {
        get
        {
            return this.postcoderekeninghouderField;
        }
        set
        {
            this.postcoderekeninghouderField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=35)]
    public string plaatsrekeninghouder
    {
        get
        {
            return this.plaatsrekeninghouderField;
        }
        set
        {
            this.plaatsrekeninghouderField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=36)]
    public string landcoderekeninghouder
    {
        get
        {
            return this.landcoderekeninghouderField;
        }
        set
        {
            this.landcoderekeninghouderField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=37)]
    public System.Nullable<int> sort
    {
        get
        {
            return this.sortField;
        }
        set
        {
            this.sortField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=38)]
    public string transport
    {
        get
        {
            return this.transportField;
        }
        set
        {
            this.transportField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=39)]
    public string psppayid
    {
        get
        {
            return this.psppayidField;
        }
        set
        {
            this.psppayidField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=40)]
    public string psppaymethod
    {
        get
        {
            return this.psppaymethodField;
        }
        set
        {
            this.psppaymethodField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=41)]
    public string IBAN
    {
        get
        {
            return this.iBANField;
        }
        set
        {
            this.iBANField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=42)]
    public string BIC
    {
        get
        {
            return this.bICField;
        }
        set
        {
            this.bICField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=43)]
    public System.DateTime verzenddatum
    {
        get
        {
            return this.verzenddatumField;
        }
        set
        {
            this.verzenddatumField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=44)]
    public string klantselectiecode
    {
        get
        {
            return this.klantselectiecodeField;
        }
        set
        {
            this.klantselectiecodeField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("orderline", Order=45)]
    public OrderLine[] orderline
    {
        get
        {
            return this.orderlineField;
        }
        set
        {
            this.orderlineField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://localhost/navision//")]
public partial class OrderLine
{
    
    private string artikelnrField;
    
    private string artikelnr2Field;
    
    private string artikelnr_levField;
    
    private int aantalField;
    
    private System.Nullable<double> eenheidsprijsField;
    
    private System.Nullable<double> regelkortingField;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string artikelnr
    {
        get
        {
            return this.artikelnrField;
        }
        set
        {
            this.artikelnrField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public string artikelnr2
    {
        get
        {
            return this.artikelnr2Field;
        }
        set
        {
            this.artikelnr2Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public string artikelnr_lev
    {
        get
        {
            return this.artikelnr_levField;
        }
        set
        {
            this.artikelnr_levField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=3)]
    public int aantal
    {
        get
        {
            return this.aantalField;
        }
        set
        {
            this.aantalField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=4)]
    public System.Nullable<double> eenheidsprijs
    {
        get
        {
            return this.eenheidsprijsField;
        }
        set
        {
            this.eenheidsprijsField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=5)]
    public System.Nullable<double> regelkorting
    {
        get
        {
            return this.regelkortingField;
        }
        set
        {
            this.regelkortingField = value;
        }
    }
}

[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
public interface ExportServiceSoapChannel : ExportServiceClient.ExportServiceSoap, System.ServiceModel.IClientChannel
{
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
public partial class ExportServiceSoapClient : System.ServiceModel.ClientBase<ExportServiceClient.ExportServiceSoap>, ExportServiceClient.ExportServiceSoap
{
    
    /// <summary>
    /// Implement this partial method to configure the service endpoint.
    /// </summary>
    /// <param name="serviceEndpoint">The endpoint to configure</param>
    /// <param name="clientCredentials">The client credentials</param>
    static partial void ConfigureEndpoint(System.ServiceModel.Description.ServiceEndpoint serviceEndpoint, System.ServiceModel.Description.ClientCredentials clientCredentials);
    
    public ExportServiceSoapClient(EndpointConfiguration endpointConfiguration) : 
            base(ExportServiceSoapClient.GetBindingForEndpoint(endpointConfiguration), ExportServiceSoapClient.GetEndpointAddress(endpointConfiguration))
    {
        this.Endpoint.Name = endpointConfiguration.ToString();
        ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
    }
    
    public ExportServiceSoapClient(EndpointConfiguration endpointConfiguration, string remoteAddress) : 
            base(ExportServiceSoapClient.GetBindingForEndpoint(endpointConfiguration), new System.ServiceModel.EndpointAddress(remoteAddress))
    {
        this.Endpoint.Name = endpointConfiguration.ToString();
        ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
    }
    
    public ExportServiceSoapClient(EndpointConfiguration endpointConfiguration, System.ServiceModel.EndpointAddress remoteAddress) : 
            base(ExportServiceSoapClient.GetBindingForEndpoint(endpointConfiguration), remoteAddress)
    {
        this.Endpoint.Name = endpointConfiguration.ToString();
        ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
    }
    
    public ExportServiceSoapClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
            base(binding, remoteAddress)
    {
    }
    
    public System.Threading.Tasks.Task<ExportServiceClient.order> MakeCopyAsync(ExportServiceClient.order ord)
    {
        return base.Channel.MakeCopyAsync(ord);
    }
    
    public System.Threading.Tasks.Task<string> SaveOrdersAsync(ExportServiceClient.order[] orders, string prefix)
    {
        return base.Channel.SaveOrdersAsync(orders, prefix);
    }
    
    public System.Threading.Tasks.Task<string> SaveOrdersLocationAsync(ExportServiceClient.order[] orders, string prefix, string location)
    {
        return base.Channel.SaveOrdersLocationAsync(orders, prefix, location);
    }
    
    public virtual System.Threading.Tasks.Task OpenAsync()
    {
        return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
    }
    
    public virtual System.Threading.Tasks.Task CloseAsync()
    {
        return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginClose(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndClose));
    }
    
    private static System.ServiceModel.Channels.Binding GetBindingForEndpoint(EndpointConfiguration endpointConfiguration)
    {
        if ((endpointConfiguration == EndpointConfiguration.ExportServiceSoap))
        {
            System.ServiceModel.BasicHttpBinding result = new System.ServiceModel.BasicHttpBinding();
            result.MaxBufferSize = int.MaxValue;
            result.ReaderQuotas = System.Xml.XmlDictionaryReaderQuotas.Max;
            result.MaxReceivedMessageSize = int.MaxValue;
            result.AllowCookies = true;
            return result;
        }
        if ((endpointConfiguration == EndpointConfiguration.ExportServiceSoap12))
        {
            System.ServiceModel.Channels.CustomBinding result = new System.ServiceModel.Channels.CustomBinding();
            System.ServiceModel.Channels.TextMessageEncodingBindingElement textBindingElement = new System.ServiceModel.Channels.TextMessageEncodingBindingElement();
            textBindingElement.MessageVersion = System.ServiceModel.Channels.MessageVersion.CreateVersion(System.ServiceModel.EnvelopeVersion.Soap12, System.ServiceModel.Channels.AddressingVersion.None);
            result.Elements.Add(textBindingElement);
            System.ServiceModel.Channels.HttpTransportBindingElement httpBindingElement = new System.ServiceModel.Channels.HttpTransportBindingElement();
            httpBindingElement.AllowCookies = true;
            httpBindingElement.MaxBufferSize = int.MaxValue;
            httpBindingElement.MaxReceivedMessageSize = int.MaxValue;
            result.Elements.Add(httpBindingElement);
            return result;
        }
        throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
    }
    
    private static System.ServiceModel.EndpointAddress GetEndpointAddress(EndpointConfiguration endpointConfiguration)
    {
        if ((endpointConfiguration == EndpointConfiguration.ExportServiceSoap))
        {
            return new System.ServiceModel.EndpointAddress("http://nies.acceptatie2.pondres.eu/exportservice.asmx");
        }
        if ((endpointConfiguration == EndpointConfiguration.ExportServiceSoap12))
        {
            return new System.ServiceModel.EndpointAddress("http://nies.acceptatie2.pondres.eu/exportservice.asmx");
        }
        throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
    }
    
    public enum EndpointConfiguration
    {
        
        ExportServiceSoap,
        
        ExportServiceSoap12,
    }
}
