﻿using MassTransit;

namespace Pondres.Omnia.Print.Integrations.OrderHub.Consumer;

public class OrderTaskMultiPrintDocumentSubmissionConsumerDefinition : ConsumerDefinition<OrderTaskMultiPrintDocumentSubmissionConsumer>
{
    public OrderTaskMultiPrintDocumentSubmissionConsumerDefinition()
    {
        // override the default endpoint name
        EndpointName = "printservice_process_multi_printdocument";
    }
}