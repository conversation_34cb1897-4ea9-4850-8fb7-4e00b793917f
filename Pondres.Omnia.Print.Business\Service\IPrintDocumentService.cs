﻿using Pondres.Omnia.Print.Contracts.Api.Common;
using Pondres.Omnia.Print.Contracts.Api.Document;
using Pondres.Omnia.Print.Contracts.PrintDocument;
using Pondres.Omnia.Print.Storage.Entities.PrintDocument;
using System;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Service;

public interface IPrintDocumentService
{
    Task<PagedList<PrintDocumentListItem>> GetDocumentsByBatchFilterAsync(PrintDocumentBatchListFilter filter);
    Task<PagedList<PrintDocumentListItem>> GetDocumentsByFilterAsync(PrintDocumentListFilter filter);
    Task<PrintDocumentEntity> AssignSequenceIdToEntityAsync(PrintDocumentAssignSequenceId message);
    Task RegisterPrintDocumentRequeuedAsync(PrintDocumentRequeued message);
    Task UpdateBatchNameForDocumentsInBundleAsync(Guid printBundleId, string newBatchName);
}