﻿using Pondres.Omnia.Print.Integrations.Navision.Model;
using Pondres.Omnia.Print.Integrations.Navision.Model.Bundle;
using Pondres.Omnia.Print.Navision.Model;

namespace Pondres.Omnia.Print.Integrations.Navision.Service;

public interface INavisionIntegrationService
{
    void SendPrintBundleXmlToNavision(SendBundleInfoToNavisionRequestBundle requestBundle);
    void SendExcludedLocationPrintBundleXmlToNavision(SendBundleInfoToNavisionRequest request, NavisionPrintBundle navisionPrintBundle);
}
