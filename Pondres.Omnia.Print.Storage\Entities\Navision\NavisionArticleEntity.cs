﻿using Newtonsoft.Json;
using Solude.StorageBase.Model;
using Pondres.Omnia.Print.Storage.Configuration;

namespace Pondres.Omnia.Print.Storage.Entities.Navision;

public class NavisionArticleEntity : CosmosEntity
{
    [JsonProperty("type")]
    public static string Type => CosmosConfiguration.NavisionArticlePartitionValue;

    [JsonProperty("No_")] public string No { get; set; } = string.Empty;

    [JsonProperty("Description")]
    public string Description { get; set; } = string.Empty;

    [JsonProperty("Description 2")]
    public string Description2 { get; set; } = string.Empty;

    [JsonProperty("Description 3")]
    public string Description3 { get; set; } = string.Empty;

    [JsonProperty("Format Code")]
    public string FormatCode { get; set; } = string.Empty;

    [JsonProperty("Pick Location")]
    public string PickLocation { get; set; } = string.Empty;

    [JsonProperty("Format 1")]
    public double Format1 { get; set; }

    [JsonProperty("Format 2")]
    public double Format2 { get; set; }

    [JsonProperty("Format 3")]
    public double Format3 { get; set; }

    [JsonProperty("Format 4")]
    public double Format4 { get; set; }

    [JsonProperty("Responsibility Center")]
    public string ResponsibilityCenter { get; set; } = string.Empty;

    public override string PartitionKeyValue => Type;

    public override string Version { get; set; } = CosmosConfiguration.NavisionArticleVersion;
}
