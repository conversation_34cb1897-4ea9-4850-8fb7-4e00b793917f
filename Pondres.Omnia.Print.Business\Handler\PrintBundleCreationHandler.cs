﻿using MassTransit;
using Pondres.Omnia.Print.Business.Extensions;
using Pondres.Omnia.Print.Business.Service;
using Pondres.Omnia.Print.Contracts.Common;
using Pondres.Omnia.Print.Contracts.PrintBundle;
using System;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Handler;

public class PrintBundleCreationHandler : IPrintBundleCreationHandler
{
    private readonly IPrintBundleService printBundleService;

    public PrintBundleCreationHandler(IPrintBundleService printBundleService)
    {
        this.printBundleService = printBundleService;
    }

    public async Task<Guid> CreatePrintBundleFromRequestAsync(IPublishEndpoint publishEndpoint, PrintBundleCreationRequest request)
    {
        var (created, bundle) = await printBundleService.GetOrCreatePrintBundleForRequestAsync(request);

        if (created && request.PrimaryBundleId != null)
        {
            await printBundleService.RegisterSecondaryBundleOnPrimaryAsync(
                primaryBundleId: request.PrimaryBundleId.Value.ToString(),
                secondaryBundleId: bundle.Id);
        }

        var bundleId = Guid.Parse(bundle.Id);

        if (created)
        {
            await publishEndpoint.Publish(new PrintBundleCreateCommand()
            {
                BundleOptions = new PrintBundleOptions
                {
                    BundleMode = bundle.BundleMode.ToEnumOrDefault(PrintBundleMode.EmptyGroup),
                },
                PrintBundleId = bundleId,
                BatchName = bundle.BatchName,
                Customer = bundle.Customer,
                DGCode = bundle.Metadata.DGCode,
                EndLocation = bundle.Metadata.EndLocation,
                InitiatedBy = bundle.InitiatedBy,
                PlannedOn = bundle.PlannedOn,
                PrintMode = bundle.Metadata.PrintMode,
                SheetArticleCode = bundle.Metadata.SheetArticleCode,
                SheetPageCount = bundle.Metadata.SheetPageCount,
                IsPrimary = bundle.Metadata.IsPrimary,
                PrimaryBundleId = bundle.PrimaryBundleId != null ? Guid.Parse(bundle.PrimaryBundleId) : bundleId,
                IsDirectlyCompleted = bundle.IsDirectlyCompleted
            });
        }

        return bundleId;
    }
}
