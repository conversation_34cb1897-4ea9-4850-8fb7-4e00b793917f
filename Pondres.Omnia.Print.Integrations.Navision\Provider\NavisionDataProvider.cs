﻿using Pondres.Omnia.Print.Integrations.Navision.Model.Common;
using System.Text;
using System.Text.Json;

namespace Pondres.Omnia.Print.Integrations.Navision.Provider;

public class NavisionDataProvider(HttpClient httpClient) : INavisionDataProvider
{
    private static readonly JsonSerializerOptions options = new() { PropertyNameCaseInsensitive = true };

    public async Task<NavisionGordAndTaskNumberResponse> GetGordAndTaskNumberAsync(string DGCode)
    {
        var content = new StringContent(JsonSerializer.Serialize(new { DGCode }), Encoding.UTF8, "application/json");

        var response = await httpClient.PostAsync(httpClient.BaseAddress, content);
        var responseString = await response.Content.ReadAsStringAsync();

        var printBundleGordAndTaskNumber = JsonSerializer.Deserialize<NavisionGordAndTaskNumberResponse>(responseString, options);

        ArgumentNullException.ThrowIfNull(printBundleGordAndTaskNumber);

        return printBundleGordAndTaskNumber;
    }
}
