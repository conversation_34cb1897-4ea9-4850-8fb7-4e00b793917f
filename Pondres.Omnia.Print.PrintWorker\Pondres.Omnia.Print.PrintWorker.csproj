﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<Nullable>enable</Nullable>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
		<DockerComposeProjectPath>..\docker-compose.dcproj</DockerComposeProjectPath>
		<UserSecretsId>************************************</UserSecretsId>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.4.0" />
		<PackageReference Include="MassTransit.Redis" Version="8.4.1" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="9.0.5" />
		<PackageReference Include="MassTransit.Azure.ServiceBus.Core" Version="8.4.1" />
		<PackageReference Include="MassTransit.RabbitMQ" Version="8.4.1" />
		<PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
		<PackageReference Include="Serilog.Extensions.Logging" Version="9.0.2" />
		<PackageReference Include="Serilog.Sinks.ApplicationInsights" Version="4.0.0" />
		<PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
		<PackageReference Include="Solude.ApiBase" Version="1.20250610.1" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.4" />
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.22.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Pondres.Omnia.Print.Business\Pondres.Omnia.Print.Business.csproj" />
		<ProjectReference Include="..\Pondres.Omnia.Print.Common\Pondres.Omnia.Print.Common.csproj" />
		<ProjectReference Include="..\Pondres.Omnia.Print.Integrations.Navision\Pondres.Omnia.Print.Integrations.Navision.csproj" />
		<ProjectReference Include="..\Pondres.Omnia.Print.Integrations.Nies\Pondres.Omnia.Print.Integrations.Nies.csproj" />
		<ProjectReference Include="..\Pondres.Omnia.Print.Integrations.OrderHub\Pondres.Omnia.Print.Integrations.OrderHub.csproj" />
		<ProjectReference Include="..\Pondres.Omnia.Print.Integrations.Warehouse\Pondres.Omnia.Print.Integrations.Warehouse.csproj" />
	</ItemGroup>

</Project>
