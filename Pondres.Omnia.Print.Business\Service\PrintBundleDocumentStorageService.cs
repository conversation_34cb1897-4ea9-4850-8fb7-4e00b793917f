﻿using MassTransit;
using Pondres.Omnia.Print.Business.Constants;
using Pondres.Omnia.Print.Business.Extensions;
using Pondres.Omnia.Print.Business.Helper;
using Pondres.Omnia.Print.Business.Model;
using Pondres.Omnia.Print.Contracts.Common;
using Pondres.Omnia.Print.Contracts.PrintBundle;
using Pondres.Omnia.Print.Contracts.PrintDocument;
using Pondres.Omnia.Print.Storage.PrintBundle;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Service;

public class PrintBundleDocumentStorageService(
    IRedisCacheHelper redisCacheHelper,
    IBus bus) : IPrintBundleDocumentStorageService
{
    public async Task<AddDocumentsToBundleResult> AddDocumentsToBundleAsync(Guid bundleId, List<PrintBundleQueuedDocument> documents, bool forceAddIfStarted = false)
    {
        await using var lockValue = await LockAndGetDocumentsFromCacheAsync(bundleId);
        var addCount = 0;

        if (lockValue.Value.Started && !forceAddIfStarted)
            return new AddDocumentsToBundleResult(BundleId: bundleId, Success: false, addCount);

        foreach (var document in documents)
        {
            var added = lockValue.Value.Documents.Add(document);

            if (added)
                addCount++;
        }

        await redisCacheHelper.SaveAsync(lockValue);

        await PublishQuantitiesUpdatedAsync(lockValue);

        return new AddDocumentsToBundleResult(BundleId: bundleId, Success: true, addCount);
    }

    private async Task PublishQuantitiesUpdatedAsync(RedisLock<PrintBundleDocumentCollection> lockValue)
    {
        var quantitiesUpdatesEvent = lockValue.Value.ToQuantitiesUpdatedEvent();

        await bus.Publish(quantitiesUpdatesEvent);
    }

    public async Task<PrintBundleDocumentCollection> RequeueFalloffDocumentsAndMarkAsStartedAsync(
        Guid bundleId,
        PrintBundleMode bundleMode, 
        string? printMode,
        int sheetPageCount)
    {
        await using var lockValue = await LockAndGetDocumentsFromCacheAsync(bundleId);

        var removedDocuments = RemoveDocumentsToMoveToNextBundle(lockValue.Value.Documents, bundleMode, printMode, sheetPageCount);

        lockValue.Value.Started = true;

        await bus.PublishBatch(removedDocuments.Select(x => new PrintDocumentRequeueCommand(x.OrderMetadata.Customer, x.OrderMetadata.OrderId)));

        await PublishQuantitiesUpdatedAsync(lockValue);

        await redisCacheHelper.SaveAsync(lockValue);

        return lockValue.Value;
    }

    private static List<PrintBundleQueuedDocument> RemoveDocumentsToMoveToNextBundle(
        HashSet<PrintBundleQueuedDocument> documents,
        PrintBundleMode bundleMode,
        string? printMode,
        int sheetPageCount)
    {
        var onlyFullSheets = bundleMode == PrintBundleMode.MaxFullSheets;

        if (onlyFullSheets)
        {
            IEnumerable<PrintBundleQueuedDocument> takeDocuments;

            int takeDocumentCount;
            for (takeDocumentCount = documents.Count; takeDocumentCount >= 0; takeDocumentCount--)
            {
                takeDocuments = documents.Take(takeDocumentCount);

                if (SelectedDocumentsMakeUpFullSheet(takeDocuments, sheetPageCount, printMode ?? PrintModeConstants.DefaultPrintMode))
                    break;
            }

            var skipDocuments = documents.Skip(takeDocumentCount).ToList();

            foreach (var skipDocument in skipDocuments)
            {
                documents.Remove(skipDocument);
            }

            return skipDocuments;
        }

        return [];
    }

    private static bool SelectedDocumentsMakeUpFullSheet(IEnumerable<PrintBundleQueuedDocument> documents, int sheetPageCount, string printmode) =>
        printmode.ToLower().Equals("duplex") ?
            documents.Sum(x => x.DocumentMetadata.Quantity * x.DocumentMetadata.PageCount) % (sheetPageCount * 2) == 0 :
            documents.Sum(x => x.DocumentMetadata.Quantity * x.DocumentMetadata.PageCount) % sheetPageCount == 0;

    public async Task<HashSet<PrintBundleQueuedDocument>> GetDocumentsForBundleAsync(Guid bundleId)
    {
        var documentCollection = await redisCacheHelper.GetAsync<PrintBundleDocumentCollection>(GetPrintBundleCacheKey(bundleId)) ?? throw new InvalidOperationException($"No printbundle queued documents collection found for bundle {bundleId}");
        return documentCollection.Documents;
    }

    public async Task<DocumentRemovalResult> RemoveDocumentFromBundleAsync(Guid bundleId, string documentId, bool isPrimaryBundle)
    {
        await using var lockValue = await LockAndGetDocumentsFromCacheAsync(bundleId);

        var documentToRemove = isPrimaryBundle ?
            lockValue.Value.Documents.FirstOrDefault(x => x.DocumentMetadata.DocumentId == documentId) :
            lockValue.Value.Documents.FirstOrDefault(x => x.DocumentMetadata.PrimaryDocumentId == documentId);

        if(documentToRemove == null)
            return new DocumentRemovalResult(false, lockValue.Value.Documents, documentToRemove);

        var isRemoved = lockValue.Value.Documents.Remove(documentToRemove);

        await redisCacheHelper.SaveAsync(lockValue);

        await PublishQuantitiesUpdatedAsync(lockValue);

        return new DocumentRemovalResult(isRemoved, lockValue.Value.Documents, documentToRemove);
    }

    private static string GetPrintBundleCacheKey(Guid bundleId) =>
        $"PrintService_PrintBundleDocuments_{bundleId}";

    private async Task<RedisLock<PrintBundleDocumentCollection>> LockAndGetDocumentsFromCacheAsync(Guid bundleId) =>
        await redisCacheHelper.LockAndGetAsync(
            key: GetPrintBundleCacheKey(bundleId),
            defaultValue: new PrintBundleDocumentCollection() { Id = bundleId });

    public void ExpireDocumentsForBundle(Guid bundleId) =>
        redisCacheHelper.Expire(GetPrintBundleCacheKey(bundleId), TimeSpan.FromHours(1));

    public async Task UpdateDocumentCollectionAsync(Guid bundleId, Action<PrintBundleDocumentCollection> updateFunc)
    {
        await using var lockValue = await LockAndGetDocumentsFromCacheAsync(bundleId);

        updateFunc.Invoke(lockValue.Value);

        await redisCacheHelper.SaveAsync(lockValue);
    }
}