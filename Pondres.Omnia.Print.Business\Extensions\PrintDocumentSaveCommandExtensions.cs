﻿using Pondres.Omnia.Print.Contracts.PrintDocument;
using Pondres.Omnia.Print.Storage.Entities.PrintBundle;
using Pondres.Omnia.Print.Storage.Entities.PrintDocument;
using System;

namespace Pondres.Omnia.Print.Business.Extensions;

public static class PrintDocumentSaveCommandExtensions
{
    public static PrintDocumentEntity ToPrintDocumentEntity(this PrintDocumentSaveCommand saveCommand, PrintBundleBasicEntity printBundleBasicEntity)
    {
        var mergedMailDate = saveCommand.ToMergedMailDate(saveCommand.Timestamp);

        var bundle = new PrintDocumentBundleRegistrationEntity
        {
            BundleId = Guid.Parse(printBundleBasicEntity.Id),
            BatchName = printBundleBasicEntity.BatchName,
            CreatedOn = printBundleBasicEntity.CreatedOn,
            GordNumber = printBundleBasicEntity.GordNumber,
            SequenceId = 0,
            TaskNumber = null
        };

        return new()
        {
            Id = saveCommand.DocumentMetadata.DocumentId,
            CreatedOn = saveCommand.Timestamp,
            LastUpdatedOn = saveCommand.Timestamp,
            OrderMetadata = saveCommand.OrderMetadata.ToOrderMetadataEntity(),
            PrimaryDocumentId = saveCommand.DocumentMetadata.PrimaryDocumentId,
            Bundles = [bundle],
            LastBundle = bundle,
            CustomerDocumentReference = saveCommand.DocumentMetadata.CustomerDocumentReference,
            Quantity = saveCommand.DocumentMetadata.Quantity,
            FileInformation = saveCommand.FileInformation.ToPrintFileInformationEntity(),
            TimeToLiveSeconds = mergedMailDate.ToFourteenDayTimeToLiveSeconds(saveCommand.Timestamp),
        };
    }

    public static DateTimeOffset ToMergedMailDate(this PrintDocumentSaveCommand saveCommand, DateTimeOffset now) =>
        saveCommand.MailDate > now ? saveCommand.MailDate : now;
}