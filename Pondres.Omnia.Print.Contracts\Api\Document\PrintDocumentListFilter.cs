﻿using System;

namespace Pondres.Omnia.Print.Contracts.Api.Document
{
    public class PrintDocumentListFilter
    {
        public string? ContinuationToken { get; set; }
        public int MaxPageSize { get; set; }
        public DateTimeOffset? CreatedFromDate { get; set; }
        public DateTimeOffset? CreatedToDate { get; set; }
        public string Customer { get; set; } = string.Empty;
        public string DocumentId { get; set; } = string.Empty;
        public string CustomerDocumentReference { get; set; } = string.Empty;
        public Guid? OrderId { get; set; }
        public Guid? BundleId { get; set; }
        public string BatchName { get; set; } = string.Empty;
        public string GordNumber { get; set; } = string.Empty;
        public int TaskNumber { get; set; }
        public int SequenceId { get; set; }
        public string BarCode { get; set; } = string.Empty;
    }
}