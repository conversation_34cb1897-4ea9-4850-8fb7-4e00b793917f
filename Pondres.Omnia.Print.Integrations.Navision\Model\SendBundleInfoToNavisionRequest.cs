﻿using Pondres.Omnia.Print.Storage.Entities.Print;

namespace Pondres.Omnia.Print.Integrations.Navision.Model;

public class SendBundleInfoToNavisionRequest
{
    public List<string> BundleIds { get; set; } = [];
    public string BatchName { get; set; } = string.Empty;
    public string DGCode { get; set; } = string.Empty;
    public string GordNumber { get; set; } = string.Empty;
    public int TaskNumber { get; set; }
    public int DocumentCount { get; set; }
    public int SheetCount { get; set; }
    public PrintDocumentMetadataEntity BundleMetadata { get; set; } = new();
}
