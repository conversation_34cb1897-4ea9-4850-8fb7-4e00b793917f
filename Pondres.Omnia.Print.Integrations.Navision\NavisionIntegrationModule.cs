﻿using Microsoft.Extensions.DependencyInjection;
using Pondres.Omnia.Print.Common;
using Pondres.Omnia.Print.Integrations.Navision.Model;
using Pondres.Omnia.Print.Integrations.Navision.Provider;
using Pondres.Omnia.Print.Integrations.Navision.Service;
using Pondres.Omnia.Print.Integrations.Navision.Uploader;

namespace Pondres.Omnia.Print.Integrations.Navision;

public static class NavisionIntegrationModule
{
    public static IServiceCollection AddNavisionIntegrationModule(this IServiceCollection services, PrintAppSettings appSettings)
    {
        services
            .AddDataProvider(appSettings)
            .AddTransient<INavisionIntegrationService, NavisionIntegrationService>()
            .AddSingleton<INavisionXmlUploader>(c => new NavisionXmlUploader(appSettings.FileStoragePath));

        services.Configure<NavisionIntegrationOptions>(x =>
        {
            x.ExcludedCustomers = appSettings.ExcludedCustomers.Split(",");
        });

        return services;
    }

    private static IServiceCollection AddDataProvider(this IServiceCollection services, PrintAppSettings appSettings)
    {
        if (appSettings.NavisionXMLInfoLogicAppConnectionString == "debug")
            services.AddTransient<INavisionDataProvider>(c => new StaticNavisionDataProvider("StaticDGCode", 0));
        else
        {
            var httpClientName = "navision_http_client";
            services.AddHttpClient(httpClientName, client =>
            {
                client.BaseAddress = new Uri(appSettings.NavisionXMLInfoLogicAppConnectionString);
            });

            services.AddHttpClient<INavisionDataProvider, NavisionDataProvider>(name: httpClientName);
        }

        return services;
    }
}