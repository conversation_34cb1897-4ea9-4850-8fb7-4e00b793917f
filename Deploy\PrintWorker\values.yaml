replicaCount: 1

namespace: omnia

image:
  repository: "#|ContainerRegistry|#.azurecr.io/omnia-print-printworker"
  pullPolicy: IfNotPresent
  tag: "#|Build.BuildNumber|#"

imagePullSecrets: []
nameOverride: "print-worker" # name of application
fullnameOverride: "print-worker" # name of application

config:
  ASPNETCORE_ENVIRONMENT: "#|ASPNETCORE_ENVIRONMENT|#"
  RELEASE_NAME: "#|Release.ReleaseName|##|Release.AttemptNumber|#"
  VaultName: "#|AZURE_KEYVAULT_NAME|#"
  TZ: "Europe/Amsterdam"
  FileStoragePath: "#|FILESTORAGE_PATH|#"
  ExcludedCustomers: "#|EXCLUDED_CUSTOMERS|#"
  ExcludedLocationPeriodicDGImportRunTime: "#|EXCLUDED_LOCATION_PERIODIC_DGIMPORT_RUNTIME|#"
  PrintFolderPath: "#|PRINT_FOLDER_PATH|#"
  CustomersExcludedFromDirectComplete: "#|CUSTOMERS_EXCLUDED_FROM_DIRECTCOMPLETE|#"

secrets:
  NavisionXMLInfoLogicAppConnectionString: "#|NAVISION_XML_INFO_LOGIC_APP_URI|#"
  AppInsightsConnectionString : "#|AppInsightsConnectionString|#"

podAnnotations: {}
podLabels:
  aadpodidbinding: "#|AKS_IDENTITY_NAME|#"

service:
  containerPort: 8080

probes:
  initialDelaySeconds: 0
  periodSeconds: 10
  timeoutSeconds: 2
  successThreshold: 1
  failureThreshold: 5
  http:
    livenessPath: /health/live
    readinessPath: /health/ready
    scheme: HTTP

hostAliases: []
#  - ip: IP_ADDRESS_1
#    hostnames:
#      - HOST_NAME_1

resources:
  limits:
    cpu: 1000m
    memory: 768Mi
  requests:
    cpu: 250m
    memory: 768Mi

volumes:
  - name: tmp
    emptyDir: {}
  - name: inspire-scaler-shared-storage
    azureFile:
      secretName: quadient-shared-storage-secret
      shareName: scaler-shared-storage
      readOnly: false

volumeMounts:
  - name: tmp
    mountPath: /tmp
  - name: inspire-scaler-shared-storage
    mountPath: "/mnt/scaler"

nodeSelector: {}

tolerations: []

affinity: {}
