﻿using MassTransit;
using Moq;
using Moq.AutoMock;
using Newtonsoft.Json;
using Solude.StorageBase.Model;
using Pondres.Omnia.OrderHub.Contracts.OrderTask;
using Pondres.Omnia.OrderHub.Contracts.Shared;
using Pondres.Omnia.Print.Business.Service;
using Pondres.Omnia.Print.Contracts.Api.Enums;
using Pondres.Omnia.Print.Contracts.Common;
using Pondres.Omnia.Print.Integrations.OrderHub.Handler;
using Pondres.Omnia.Print.Storage.Entities.Print;
using Pondres.Omnia.Print.Storage.Repositories;
using Serilog.Core;
using System;
using System.Threading;
using System.Threading.Tasks;
using Xunit;

namespace Pondres.Omnia.Print.Integrations.OrderHub.UnitTests;

public class PrintMetadataEnrichmentHandlerTests
{
    private readonly AutoMocker mocker;

    public PrintMetadataEnrichmentHandlerTests()
    {
        mocker = new AutoMocker(MockBehavior.Strict);
    }

    [Fact]
    public async Task Consume_EnrichPrintMetadata_Correctly()
    {
        // Arrange
        var message = new OrderTaskBatchStartCommand
        {
            Metadata = new OrderTaskBatchMetadata
            {
                TaskId = Guid.NewGuid(),
                BatchId = "0",
                Customer = "Customer",
                Flow = "Flow",
                OrderId = Guid.NewGuid(),
                CustomerReference = "CustomerReference",
                TaskTypeName = "Enrichment"
            },
            Input = new OrderTaskBatchStartInput
            {
                Data = new OrderTaskBatchInputData
                {
                    FileGroups =
                    [
                        new OrderTaskBatchSourceDataFileGroup
                        {
                            Items =
                            [
                                new OrderTaskBatchSourceDataFileGroupItem(
                                    new TemporaryFileData {
                                        FileName = "FileName",
                                        FilePath = "FilePath",
                                        TemporaryUri = "http://TemporaryUri",
                                        ExpiresOn = DateTimeOffset.Now.AddMinutes(30)
                                    },
                                    OrderTaskBatchSourceDataType.Data)
                            ]
                        }
                    ]
                }
            }
        };

        var printMetadata = new PrintDocumentCreationMetadata
        {
            SheetArticleCode = "P6150HAL",
            PrinterType = null,
            SheetFormat = "",
            DocumentFormat = "S4D",
            SheetPageCount = 8,
            Laminate = true,
            MailDate = DateTimeOffset.UtcNow,
            Carrier = "PostNl",
            PostalDestination = "dest"
        };

        var metadataJson = JsonConvert.SerializeObject(printMetadata);

        var dataSheetentity = new CosmosResponse<PrintSheetMappingEntity>(string.Empty, new PrintSheetMappingEntity
        {
            SheetArticleCode = "P6150HAL",
            PrinterType = PrinterType.FC.ToString(),
            SheetFormat = "123x456"
        });

        mocker.Use(Logger.None);

        mocker.GetMock<IDownloadService>()
            .Setup(x => x.GetTextAsync(It.IsAny<Uri>()))
            .ReturnsAsync(metadataJson);

        mocker.GetMock<IPrintSheetMappingRepository>()
            .Setup(x => x.GetSingleCachedAsync(printMetadata.SheetArticleCode))
            .ReturnsAsync(dataSheetentity);

        mocker.GetMock<IPublishEndpoint>()
            .Setup(x => x.Publish(It.IsAny<OrderTaskBatchItemCompleted>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        var handler = mocker.CreateInstance<PrintMetadataEnrichmentHandler>();

        // Act
        await handler.HandleEnrichmentRequestAsync(mocker.GetMock<IPublishEndpoint>().Object, message);

        // Assert
        mocker.VerifyAll();
    }

    [Fact]
    public async Task Consume_EnrichPrintMetadata_InvalidDownload()
    {
        // Arrange
        var message = new OrderTaskBatchStartCommand
        {
            Metadata = new OrderTaskBatchMetadata
            {
                TaskId = Guid.NewGuid(),
                BatchId = "0",
                Customer = "Customer",
                Flow = "Flow",
                OrderId = Guid.NewGuid(),
                CustomerReference = "CustomerReference",
                TaskTypeName = "Enrichment"
            },
            Input = new OrderTaskBatchStartInput
            {
                Data = new OrderTaskBatchInputData
                {
                    FileGroups =
                    [
                        new OrderTaskBatchSourceDataFileGroup
                        {
                            Items =
                            [
                                new OrderTaskBatchSourceDataFileGroupItem(
                                    new TemporaryFileData {
                                        FileName = "FileName",
                                        FilePath = "FilePath",
                                        TemporaryUri = "http://TemporaryUri",
                                        ExpiresOn = DateTimeOffset.Now.AddMinutes(30)
                                    },
                                    OrderTaskBatchSourceDataType.Data)
                            ]
                        }
                    ]
                }
            }
        };

        mocker.GetMock<IDownloadService>()
            .Setup(x => x.GetTextAsync(It.IsAny<Uri>()))
            .Throws(new Exception("Download not available Exception"));

        mocker.GetMock<IPublishEndpoint>()
            .Setup(x => x.Publish(It.IsAny<OrderTaskBatchItemFailed>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        var handler = mocker.CreateInstance<PrintMetadataEnrichmentHandler>();

        // Act
        await handler.HandleEnrichmentRequestAsync(mocker.GetMock<IPublishEndpoint>().Object, message);

        // Assert
        mocker.VerifyAll();
    }
}