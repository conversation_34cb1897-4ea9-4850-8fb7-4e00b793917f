﻿using Pondres.Omnia.Print.Contracts.Common;
using Pondres.Omnia.Print.Storage.Entities.Print;
using System.Collections.Generic;
using System.Linq;

namespace Pondres.Omnia.Print.Business.Extensions;

public static class PrintDocumentMetadataAttachmentExtensions
{
    public static List<PrintDocumentCreationMetadataAttachment> ToPrintDocumentCreationMetadataAttachments(this List<PrintDocumentMetadataAttachmentEntity> attachments) =>
         attachments.Select(x => new PrintDocumentCreationMetadataAttachment
         {
             ArticleCode = x.ArticleCode,
             ArticleName = x.ArticleName,
             PickLocation = x.PickLocation,
             ExcludeForNavision = x.ExcludeForNavision
         }).ToList();

    public static List<PrintDocumentMetadataAttachmentEntity> ToPrintDocumentMetadataAttachmentEntities(this List<PrintDocumentCreationMetadataAttachment> attachments) =>
        attachments.Select(x => new PrintDocumentMetadataAttachmentEntity
        {
            ArticleCode = x.ArticleCode,
            ArticleName = x.ArticleName,
            PickLocation = x.PickLocation,
            ExcludeForNavision = x.ExcludeForNavision
        }).ToList();
}
