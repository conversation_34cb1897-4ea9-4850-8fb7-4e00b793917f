﻿using Microsoft.Azure.Cosmos;
using Microsoft.Azure.Cosmos.Linq;
using Microsoft.Extensions.Caching.Memory;
using Solude.StorageBase.Exceptions;
using Solude.StorageBase;
using Pondres.Omnia.Print.Contracts.Api.Document;
using Pondres.Omnia.Print.Storage.Configuration;
using Pondres.Omnia.Print.Storage.Entities.PrintDocument;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Solude.StorageBase.Model;

namespace Pondres.Omnia.Print.Storage.Repositories;

public class PrintDocumentRepository : SinglePartitionBaseRepository<PrintDocumentEntity>, IPrintDocumentRepository
{
    protected override bool CanWrite => true;

    protected override string PartitionKeyValue => CosmosConfiguration.PrintDocumentPartitionValue;

    public PrintDocumentRepository(CosmosClient cosmosClient, IMemoryCache memoryCache) :
        base(cosmosClient, CosmosConfiguration.PrintDatabaseName, CosmosConfiguration.PrintDocumentContainerName, memoryCache)
    {
    }

    public async Task<ListResult<PrintDocumentEntity>> GetByFilterAsync(PrintDocumentListFilter filter)
    {
        var query = BuildQueryByFilter(filter);

        var results = await GetEntityListAsync(query, filter.ContinuationToken, filter.MaxPageSize);

        return results;
    }

    private IOrderedQueryable<PrintDocumentEntity> BuildQueryByFilter(PrintDocumentListFilter filter)
    {
        var query = container.GetItemLinqQueryable<PrintDocumentEntity>().AsQueryable();

        query = !string.IsNullOrWhiteSpace(filter.DocumentId) ?
            query.Where(x => x.Id == filter.DocumentId) : query;

        query = !string.IsNullOrWhiteSpace(filter.Customer) ?
            query.Where(x => x.OrderMetadata.Customer == filter.Customer) : query;

        query = !string.IsNullOrWhiteSpace(filter.CustomerDocumentReference) ?
            query.Where(x => x.CustomerDocumentReference != null && x.CustomerDocumentReference.Contains(filter.CustomerDocumentReference)) : query;

        query = !string.IsNullOrWhiteSpace(filter.BatchName) ?
            query.Where(x => x.LastBundle != null && x.LastBundle.BatchName == filter.BatchName) : query;

        query = filter.BundleId.HasValue ?
            query.Where(x => x.LastBundle != null && x.LastBundle.BundleId == filter.BundleId) : query;

        query = filter.OrderId.HasValue ?
            query.Where(x => x.OrderMetadata.OrderId == filter.OrderId) : query;

        query = filter.CreatedFromDate.HasValue ?
            query.Where(x => x.CreatedOn >= filter.CreatedFromDate) : query;

        query = filter.CreatedToDate.HasValue ?
            query.Where(x => x.CreatedOn <= filter.CreatedToDate) : query;

        query = !string.IsNullOrWhiteSpace(filter.GordNumber) ?
            query.Where(x => x.LastBundle != null && x.LastBundle.GordNumber == filter.GordNumber) : query;

        query = filter.TaskNumber != 0 ?
            query.Where(x => x.LastBundle != null && x.LastBundle.TaskNumber == filter.TaskNumber) : query;

        query = filter.SequenceId != 0 ?
            query.Where(x => x.LastBundle != null && x.LastBundle.SequenceId == filter.SequenceId) : query;

        return query.OrderByDescending(x => x.CreatedOn);
    }

    public async Task<List<PrintDocumentEntity>> GetAllForOrderIdAsync(string customer, Guid orderId, bool throwIfNoDocuments = true)
    {
        var queryDefinition = container.GetItemLinqQueryable<PrintDocumentEntity>()
            .Where(x => x.Type == PartitionKeyValue && x.OrderMetadata.Customer == customer && x.OrderMetadata.OrderId == orderId)
            .ToQueryDefinition();

        var entities = await GetAllFromQueryDefinitionAsync(queryDefinition);

        if (throwIfNoDocuments && entities.Count == 0)
            throw new EntityNotFoundException(typeof(PrintDocumentEntity), orderId.ToString(), customer);

        return entities;
    }

    public async Task<List<PrintDocumentEntity>> GetAllForPrintBundleAsync(Guid bundleId)
    {
        var queryDefinition = container.GetItemLinqQueryable<PrintDocumentEntity>()
            .Where(x => x.LastBundle != null && x.LastBundle.BundleId == bundleId)
            .ToQueryDefinition();

        var entities = await GetAllFromQueryDefinitionAsync(queryDefinition);

        if (entities.Count == 0)
            throw new EntityNotFoundException(typeof(PrintDocumentEntity), bundleId.ToString(), string.Empty);

        return entities;
    }

    private async Task<List<PrintDocumentEntity>> GetAllFromQueryDefinitionAsync(QueryDefinition queryDefinition)
    {
        using var feedIterator = container.GetItemQueryIterator<PrintDocumentEntity>(queryDefinition);

        var entities = new List<PrintDocumentEntity>();

        while (feedIterator.HasMoreResults)
        {
            var currentResultSet = await feedIterator.ReadNextAsync();
            LogResponse("FeedIteratorReadNextAsync", currentResultSet);
            foreach (var entity in currentResultSet)
            {
                entities.Add(entity);
            }
        }

        return entities;
    }

    protected override MemoryCacheEntryOptions CreateCacheOptions() => new()
    {
        SlidingExpiration = TimeSpan.FromMinutes(1)
    };

    protected override string GetCacheKey(string id, string partitionKeyValue) => $"PrintDocumentEntity_{id}_{partitionKeyValue}";

    public async Task<ListResult<PrintDocumentEntity>> GetByBatchFilterAsync(PrintDocumentBatchListFilter filter)
    {
        var query = BuildQueryByBatchFilter(filter);

        var results = await GetEntityListAsync(query, filter.ContinuationToken, filter.MaxPageSize);

        return results;
    }

    private IOrderedQueryable<PrintDocumentEntity> BuildQueryByBatchFilter(PrintDocumentBatchListFilter filter)
    {
        var query = container.GetItemLinqQueryable<PrintDocumentEntity>().AsQueryable();

        query = filter.OrderIds.Count != 0 ? query.Where(x => filter.OrderIds.Contains(x.OrderMetadata.OrderId)) : query;

        query = filter.OrderReferences.Count != 0 ? query.Where(x => filter.OrderReferences.Contains(x.OrderMetadata.CustomerReference)) : query;

        query = filter.References.Count != 0 ? query.Where(x => x.CustomerDocumentReference != null && filter.References.Contains(x.CustomerDocumentReference)) : query;

        query = filter.GordNumbers.Count != 0 ? query.Where(x => x.LastBundle != null && x.LastBundle.GordNumber != null && filter.GordNumbers.Contains(x.LastBundle.GordNumber)) : query;

        query = filter.TaskNumbers.Count != 0 ? query.Where(x => x.LastBundle != null && filter.TaskNumbers.Contains(x.LastBundle.TaskNumber)) : query;

        query = filter.SequenceIds.Count != 0 ? query.Where(x => x.LastBundle != null && filter.SequenceIds.Contains(x.LastBundle.SequenceId)) : query;

        return query.OrderByDescending(x => x.CreatedOn);
    }
}