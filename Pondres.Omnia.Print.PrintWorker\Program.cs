using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;
using Serilog;
using Solude.ApiBase.Configuration;
using Solude.ApiBase.Extensions;
using System;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.PrintWorker;

public class Program
{
    protected Program() { }

    public const string Name = "PrintWorker";

    public static IHostBuilder CreateHostBuilder(string[] args) =>
        Host.CreateDefaultBuilder(args)
            .ConfigureAppConfiguration((_, config) => config.AddDefaultServiceKeyVaultIfAvailable())
            .ConfigureWebHostDefaults(webBuilder => webBuilder.UseStartup<Startup>())
            .UseSerilog();

    public static async Task<int> Main(string[] args)
    {
        LoggerBuilder.BuildLogger(serviceName: Name);

        try
        {
            Log.Information("Building host");

            var host = CreateHostBuilder(args).Build();

            Log.Information("Starting host");

            await host.RunWithTasksAsync();

            return 0;
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "Host terminated unexpectedly");
            return 1;
        }
        finally
        {
            await Log.CloseAndFlushAsync();
        }
    }
}