﻿using MassTransit;
using Microsoft.Extensions.Options;
using Pondres.Omnia.Print.Contracts.PrintBundle;
using Pondres.Omnia.Print.Integrations.Nies.Handler;
using Serilog;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Integrations.Nies.Consumer;

public class ExportToNiesOptions
{
    public string[] CustomersToInclude { get; set; } = [];
    public bool Enabled { get; set; }
}

public class ExportToNiesConsumer : IConsumer<Batch<PrintBundleCompleted>>
{
    private readonly ExportToNiesOptions options;
    private readonly IPrintBundleExportHandler printBundleExportHandler;

    public ExportToNiesConsumer(
        IPrintBundleExportHandler printBundleExportHandler,
        IOptions<ExportToNiesOptions> options)
    {
        this.options = options.Value;
        this.printBundleExportHandler = printBundleExportHandler;
    }

    public async Task Consume(ConsumeContext<Batch<PrintBundleCompleted>> context)
    {
        if (!options.Enabled)
        {
            Log.Information("ExportToNies is disabled");
            return;
        }

        Log.Information("ExportToNiesConsumer received PrintBundleCompleted messages for PrintBundles {PrintBundleIds}", string.Join(',', context.Message.Select(c => c.Message.PrintBundleId.ToString())));

        var bundlesToProcess = context.Message
            .Where(message => options.CustomersToInclude.Contains(message.Message.Customer.ToUpper()))
            .Select(messageContext => messageContext.Message);

        await printBundleExportHandler.HandlePrintBundleCompletedRequestAsync(bundlesToProcess);
    }
}