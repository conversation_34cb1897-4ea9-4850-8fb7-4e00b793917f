﻿using Pondres.Omnia.Print.Contracts.Common;
using Pondres.Omnia.Print.Contracts.PrintDocument;
using System.Collections.Generic;

namespace Pondres.Omnia.Print.Contracts.PrintBundle
{
    public record PrintBundleQueuedDocument(
        OrderMetadata OrderMetadata, 
        PrintDocumentMetadata DocumentMetadata, 
        string PrintFilePath, 
        string CreationHash)
    {
        public int SequenceId { get; set; }
    }
}