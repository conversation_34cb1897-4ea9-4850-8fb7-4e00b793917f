﻿using FluentAssertions;
using Pondres.Omnia.OrderHub.IntegrationTests.Tests;
using Pondres.Omnia.Print.IntegrationTests.Fixtures;
using Pondres.Omnia.Print.IntegrationTests.Fixtures.Integration;
using Pondres.Omnia.Print.IntegrationTests.PrintClient;

namespace Pondres.Omnia.Print.IntegrationTests.Tests.Api;

[Collection("IntegrationTests")]
public class PrintBundleApiIntegrationTests : BaseIntegrationTest
{
    public PrintBundleApiIntegrationTests(IntegrationTestFixture fixture) : base(fixture)
    {

    }

    [Fact]
    public async Task GetRaw_ValidBundleId_ReturnsRawJson()
    {
        // Arrange
        var customer = GenerateCustomer();
        var releaseSchedule = "Di-Do_10.00";

        var printDocument = OrderHubIntegrationFixture.CreatePrintDocumentMetadata(isPrimary: true, releaseScheduleId: releaseSchedule);
        var document = await SubmitPrintDocumentOrderAsync(customer, printDocument);

        // Act
        var rawResponse = await fixture.ManagerApp.BundleApi.GetRawAsync(document.LastBundle.BundleId);

        rawResponse.RawJson.Should().NotBeNullOrEmpty();
        rawResponse.RawJson.Should().Contain("{");
        rawResponse.RawJson.Should().Contain(document.LastBundle.BundleId.ToString());
        rawResponse.RawJson.Should().Contain("}");
    }

    [Fact]
    public async Task GetListByIds_SecondaryBundles_CantBeReleased()
    {
        // Arrange
        var customer = GenerateCustomer();
        var mailDate = DateTimeOffset.Now;

        var metadataCollection = new List<Contracts.Common.PrintDocumentCreationMetadata>
        {
            OrderHubIntegrationFixture.CreatePrintDocumentMetadata(isPrimary: true, mailDate: mailDate),
            OrderHubIntegrationFixture.CreatePrintDocumentMetadata(isPrimary: false, mailDate: mailDate),
        };

        var documents = await SubmitMultiPrintDocumentOrderAsync(customer, metadataCollection);

        var document = documents.Single(x => !x.IsPrimary);

        // Act
        await fixture.ManagerApp.BundleApi.WaitForBundleToBeInState(document.LastBundle.BundleId, MapPrintBundleState.WaitingForStart);

        await WaitForAsync(async () =>
        {
            var list = await fixture.ManagerApp.BundleApi.GetListByIds([document.LastBundle.BundleId]);

            var secondaryBundleListItem = list.Items.First();
            secondaryBundleListItem.Status.Status.Should().Be(MapPrintBundleState.WaitingForStart);
            secondaryBundleListItem.Actions.Should().NotContain(x => x == PrintBundleInputOption.Release);
        });
    }
}
