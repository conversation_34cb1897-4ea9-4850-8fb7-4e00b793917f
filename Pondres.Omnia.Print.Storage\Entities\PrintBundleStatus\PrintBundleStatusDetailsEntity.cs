﻿using Newtonsoft.Json;
using System;

namespace Pondres.Omnia.Print.Storage.Entities.PrintBundleStatus;

public class PrintBundleStatusDetailsEntity
{
    [JsonProperty("isInFailedState")]
    public bool IsInFailedState { get; set; }

    [JsonProperty("message")]
    public string Message { get; set; } = string.Empty;

    [JsonProperty("status")] public string Status { get; set; } = string.Empty;

    [JsonProperty("timestamp")]
    public DateTimeOffset Timestamp { get; set; }

    [JsonProperty("waitingForInput")]
    public bool WaitingForInput { get; set; }

}