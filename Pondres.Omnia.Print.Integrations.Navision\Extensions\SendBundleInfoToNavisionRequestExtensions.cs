﻿using Pondres.Omnia.Print.Integrations.Navision.Model;
using Pondres.Omnia.Print.Integrations.Navision.Model.Bundle;
using System.Globalization;

namespace Pondres.Omnia.Print.Integrations.Navision.Extensions;

public static class SendBundleInfoToNavisionRequestExtensions
{
    public static NavisionPrintBundle ToNavisionPrintBundle(this SendBundleInfoToNavisionRequest request, List<NavisionPrintDataRule> dataRules) =>
        new()
        {
            DgCode = request.DGCode,
            BatchName = request.BatchName,
            TaskNumber = request.TaskNumber,
            DocumentCount = request.DocumentCount,
            MailDate = request.BundleMetadata.MailDate.ToString("dd-MM-yyyy", CultureInfo.InvariantCulture),
            Carrier = request.BundleMetadata.Carrier,
            PaperFormat = request.BundleMetadata.SheetFormat,
            PrintFormat = request.BundleMetadata.SheetFormat,
            EndFormat = request.BundleMetadata.DocumentFormat.SetEndFormat(),
            FoldFormat = request.BundleMetadata.DocumentFormat.SetFoldFormat(),
            DataRules = dataRules
        };

    private static string SetEndFormat(this string documentFormat)
    {
        var documentFormats = documentFormat.Split("-", StringSplitOptions.RemoveEmptyEntries);
        return documentFormats.Length != 0 ? documentFormats[0] : documentFormat;
    }

    private static string SetFoldFormat(this string documentFormat)
    {
        var documentFormats = documentFormat.Split("-", StringSplitOptions.RemoveEmptyEntries);
        return documentFormats.Length != 0 && documentFormats.Length > 1 ? documentFormats[1] : documentFormat;
    }
}
