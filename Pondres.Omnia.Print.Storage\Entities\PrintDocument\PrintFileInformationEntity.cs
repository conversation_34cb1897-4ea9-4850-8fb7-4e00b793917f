﻿using Newtonsoft.Json;

namespace Pondres.Omnia.Print.Storage.Entities.PrintDocument;

public class PrintFileInformationEntity
{
    [JsonProperty("containerName")] public string StorageContainerName { get; set; } = string.Empty;

    [JsonProperty("documentFilePath")] public string DocumentFilePath { get; set; } = string.Empty;

    [JsonProperty("metadataFilePath")] public string MetadataFilePath { get; set; } = string.Empty;

    [JsonProperty("storageAccountName")] public string StorageAccountName { get; set; } = string.Empty;
}