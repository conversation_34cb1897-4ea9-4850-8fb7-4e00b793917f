﻿using FluentValidation;
using MassTransit.Initializers;
using Pondres.Omnia.Print.Business.Extensions;
using Pondres.Omnia.Print.Business.Validation;
using Pondres.Omnia.Print.Contracts.Api.ReleaseSchedule;
using Pondres.Omnia.Print.Storage.Repositories;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Service;

public class ReleaseScheduleService : IReleaseScheduleService
{
    private readonly IReleaseSchedulesRepository releaseSchedulesRepository;
    private const string scheduleType = "releaseSchedule";

    public ReleaseScheduleService(IReleaseSchedulesRepository releaseSchedulesRepository)
    {
        this.releaseSchedulesRepository = releaseSchedulesRepository;
    }

    public async Task CreateReleaseScheduleAsync(ReleaseSchedules entity)
    {
        Validate(entity);
        var cosmosEntity = entity.ToReleaseSchedules();
        await releaseSchedulesRepository.CreateAsync(cosmosEntity);
    }

    public async Task DeleteReleaseScheduleAsync(string id)
    {
        await releaseSchedulesRepository.DeleteAsync(id);
    }

    public async Task<List<ReleaseSchedules>> GetAllReleaseSchedulesAsync()
    {
        var entities = await releaseSchedulesRepository.GetAllAsync();
        var releaseSchedules = entities.Where(x => x.Type == scheduleType).Select(x => x.ToReleaseSchedulesDto()).ToList();

        return releaseSchedules;
    }

    public async Task UpdateReleaseScheduleAsync(ReleaseSchedules entity)
    {
        Validate(entity);
        var cosmosEntity = entity.ToReleaseSchedules();
        await releaseSchedulesRepository.CreateOrReplaceAsync(cosmosEntity);
    }

    private static void Validate(ReleaseSchedules schedule)
    {
        var validator = new ReleaseScheduleValidator();
        validator.ValidateAndThrow(schedule);
    }
}
