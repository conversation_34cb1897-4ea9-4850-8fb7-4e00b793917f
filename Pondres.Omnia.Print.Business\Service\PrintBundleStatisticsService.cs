﻿using Pondres.Omnia.Print.Business.Extensions;
using Pondres.Omnia.Print.Business.Helper;
using Pondres.Omnia.Print.Contracts.Api.Bundle;
using Pondres.Omnia.Print.Storage.Entities.PrintBundle;
using Pondres.Omnia.Print.Storage.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Service;

public class PrintBundleStatisticsService : IPrintBundleStatisticsService
{
    private readonly IPrintBundleBasicRepository printBundleRepository;

    public PrintBundleStatisticsService(IPrintBundleBasicRepository printBundleRepository)
    {
        this.printBundleRepository = printBundleRepository;
    }

    public async Task<PrintBundleTotalsResult> GetTotalsForDateRangeAsync(PrintBundleDateRangeFilter filter)
    {
        var allBundles = await printBundleRepository.GetAllForDateRangeAsync(filter);

        var dateGroupedBundles = allBundles
            .GroupBy(x => x.CreatedOn.Date);

        var totalsResult = new PrintBundleTotalsResult();

        foreach (var dateGroupedBundle in dateGroupedBundles)
        {
            var weekNumber = dateGroupedBundle.Key.ToIso8601WeekOfYear();

            var totalsPerDay = GetPrintBundleTotalsPerDay(dateGroupedBundle, weekNumber);

            UpdatePrintBundleTotalsPerWeek(totalsResult.TotalsPerWeek, weekNumber, totalsPerDay);

            UpdatePrintBundleTotalsPerWeek(totalsResult.TotalsPerHour, totalsPerDay);

            totalsResult.Days.Add(totalsPerDay);
        }

        return totalsResult;
    }

    private static void UpdatePrintBundleTotalsPerWeek(Dictionary<int, PrintBundleTotalsItem> totalsPerHour, PrintBundleTotalsPerDayItem totalsPerDay)
    {
        foreach (var hour in totalsPerDay.Hours)
        {
            if (totalsPerHour.ContainsKey(hour.Key))
            {
                totalsPerHour[hour.Key].DocumentCount += hour.Value.DocumentCount;
                totalsPerHour[hour.Key].SheetCount += hour.Value.SheetCount;
            }
            else
            {
                totalsPerHour.Add(
                    hour.Key,
                    new PrintBundleTotalsItem
                    {
                        DocumentCount = hour.Value.DocumentCount,
                        SheetCount = hour.Value.SheetCount
                    });
            }
        }
    }

    private static void UpdatePrintBundleTotalsPerWeek(
        Dictionary<int, PrintBundleTotalsItem> totalsPerWeek,
        int weekNumber,
        PrintBundleTotalsPerDayItem totalsPerDayItem)
    {
        if (totalsPerWeek.ContainsKey(weekNumber))
        {
            totalsPerWeek[weekNumber].DocumentCount += totalsPerDayItem.Totals.DocumentCount;
            totalsPerWeek[weekNumber].SheetCount += totalsPerDayItem.Totals.SheetCount;
        }
        else
        {
            totalsPerWeek.Add(
                weekNumber,
                new PrintBundleTotalsItem
                {
                    DocumentCount = totalsPerDayItem.Totals.DocumentCount,
                    SheetCount = totalsPerDayItem.Totals.SheetCount
                });
        }
    }

    private static PrintBundleTotalsPerDayItem GetPrintBundleTotalsPerDay(
        IGrouping<DateTime, PrintBundleBasicEntity> dateGroupedBundles,
        int weekNumber)
    {
        var totalsPerDayItem = new PrintBundleTotalsPerDayItem
        {
            Date = dateGroupedBundles.Key.Date,
            WeekNumber = weekNumber
        };

        var hourlyBundles = dateGroupedBundles
            .GroupBy(x => x.CreatedOn.Hour);

        foreach (var hourlyBundle in hourlyBundles)
        {
            var dayTotals = new PrintBundleTotalsItem
            {
                DocumentCount = hourlyBundle.Sum(bundle => bundle.Quantities.DocumentCount),
                SheetCount = hourlyBundle.Sum(bundle => SheetCountCalculationHelper.CalculateSheetCount(
                    bundle.Metadata.PrintMode, 
                    bundle.Metadata.SheetPageCount,
                    bundle.Quantities.PageCount))
            };

            totalsPerDayItem.Hours.Add(hourlyBundle.Key, dayTotals);

            totalsPerDayItem.Totals.DocumentCount += dayTotals.DocumentCount;
            totalsPerDayItem.Totals.SheetCount += dayTotals.SheetCount;
        }

        return totalsPerDayItem;
    }
}