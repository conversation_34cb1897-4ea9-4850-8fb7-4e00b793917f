﻿using Microsoft.Extensions.Hosting;
using Pondres.Omnia.Print.Business.Handler.Navision;
using Pondres.Omnia.Print.Business.Helper;
using Pondres.Omnia.Print.Integrations.Navision.Service;
using Serilog;
using System;
using System.Globalization;
using System.Threading;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.PrintManager.Services;

public class ExcludedLocationPeriodicDGImportService : BackgroundService
{
    public const string cacheKey = $"PrintService_periodic_DG_import_last_run";

    private readonly IRedisCacheHelper redisCacheHelper;
    private readonly IDGImportHandler dgImportHandler;
    private readonly INavisionIntegrationService navisionIntegrationService;

    private readonly TimeSpan interval = TimeSpan.FromMinutes(10);

    public TimeSpan RunTime { get; set; } = TimeSpan.FromHours(23);

    public ExcludedLocationPeriodicDGImportService(
        IRedisCacheHelper redisCacheHelper,
        IDGImportHandler dgImportHandler,
        INavisionIntegrationService navisionIntegrationService)
    {
        this.redisCacheHelper = redisCacheHelper;
        this.dgImportHandler = dgImportHandler;
        this.navisionIntegrationService = navisionIntegrationService;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        using var timer = new PeriodicTimer(interval);

        do
        {
            try
            {
                var lastRun = await redisCacheHelper.GetOrSetAsync(cacheKey, DateTimeOffset.Now.ToString());

                var parsedLastRun = DateTimeOffset.Parse(lastRun!, CultureInfo.CurrentCulture);

                if (!TimeToRun(DateTimeOffset.Now, parsedLastRun))
                    continue;

                var requestsWithBundles = await dgImportHandler.GetExcludedLocationBundlesForTimeRangeAsync(parsedLastRun, DateTimeOffset.Now);

                foreach (var requestWithBundle in requestsWithBundles)
                {
                    navisionIntegrationService.SendExcludedLocationPrintBundleXmlToNavision(requestWithBundle.Key, requestWithBundle.Value);
                }

                // update last run in redis
                await redisCacheHelper.SaveAsync(cacheKey, DateTimeOffset.Now.ToString(CultureInfo.CurrentCulture));
            }
            catch (Exception exception)
            {
                Log.Error(exception, "Failed to run the DGImportToNavisionSinceLastRunAsync");
            }
        }
        while (!stoppingToken.IsCancellationRequested && await timer.WaitForNextTickAsync(stoppingToken));
    }

    private bool TimeToRun(DateTimeOffset now, DateTimeOffset lastRun) =>
        now.Date > lastRun.Date && now > now.Date + RunTime;
}
