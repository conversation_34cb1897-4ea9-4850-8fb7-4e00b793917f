#See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
USER app
WORKDIR /app
EXPOSE 8080
EXPOSE 8081

ENV TZ "Europe/Amsterdam"

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build

## Start: Authenticate with custom feed ##
ARG FEED_ACCESSTOKEN
ENV VSS_NUGET_EXTERNAL_FEED_ENDPOINTS \
    "{\"endpointCredentials\": [{\"endpoint\":\"https://pkgs.dev.azure.com/pondresnl/Omnia/_packaging/Omnia/nuget/v3/index.json\", \"username\":\"docker\", \"password\":\"${FEED_ACCESSTOKEN}\"}]}"

RUN curl -L https://raw.githubusercontent.com/Microsoft/artifacts-credprovider/master/helpers/installcredprovider.sh  | bash
## End: Authenticate with custom feed ##

ARG BUILD_CONFIGURATION=Release
WORKDIR /src

COPY ["nuget.config", "."]
COPY ["Pondres.Omnia.Print.PrintManager/Pondres.Omnia.Print.PrintManager.csproj", "Pondres.Omnia.Print.PrintManager/"]
COPY ["Pondres.Omnia.Print.Business/Pondres.Omnia.Print.Business.csproj", "Pondres.Omnia.Print.Business/"]
COPY ["Pondres.Omnia.Print.Common/Pondres.Omnia.Print.Common.csproj", "Pondres.Omnia.Print.Common/"]
COPY ["Pondres.Omnia.Print.Contracts/Pondres.Omnia.Print.Contracts.csproj", "Pondres.Omnia.Print.Contracts/"]
COPY ["Pondres.Omnia.Print.Integrations.Navision/Pondres.Omnia.Print.Integrations.Navision.csproj", "Pondres.Omnia.Print.Integrations.Navision/"]
COPY ["Pondres.Omnia.Print.Storage/Pondres.Omnia.Print.Storage.csproj", "Pondres.Omnia.Print.Storage/"]
COPY ["Pondres.Omnia.Print.Integrations.Nies/Pondres.Omnia.Print.Integrations.Nies.csproj", "Pondres.Omnia.Print.Integrations.Nies/"]
COPY ["Pondres.Omnia.Print.Integrations.OrderHub/Pondres.Omnia.Print.Integrations.OrderHub.csproj", "Pondres.Omnia.Print.Integrations.OrderHub/"]
COPY ["Pondres.Omnia.Print.Integrations.Warehouse/Pondres.Omnia.Print.Integrations.Warehouse.csproj", "Pondres.Omnia.Print.Integrations.Warehouse/"]
RUN dotnet restore "./Pondres.Omnia.Print.PrintManager/./Pondres.Omnia.Print.PrintManager.csproj"
COPY . .
WORKDIR "/src/Pondres.Omnia.Print.PrintManager"
RUN dotnet build "./Pondres.Omnia.Print.PrintManager.csproj" -c "$BUILD_CONFIGURATION" -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./Pondres.Omnia.Print.PrintManager.csproj" -c "$BUILD_CONFIGURATION" -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Pondres.Omnia.Print.PrintManager.dll"]