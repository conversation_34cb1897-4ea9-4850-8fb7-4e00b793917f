﻿using Pondres.Omnia.Print.Contracts.Api.ReleaseSchedule;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Service;

public interface IReleaseScheduleService
{
    Task<List<ReleaseSchedules>> GetAllReleaseSchedulesAsync();
    Task CreateReleaseScheduleAsync(ReleaseSchedules entity);
    Task UpdateReleaseScheduleAsync(ReleaseSchedules entity);
    Task DeleteReleaseScheduleAsync(string id);
}
