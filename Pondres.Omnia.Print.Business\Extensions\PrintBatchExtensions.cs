﻿using Pondres.Omnia.Print.Contracts.Api.Bundle;

namespace Pondres.Omnia.Print.Business.Extensions;

public static class PrintBatchExtensions
{
    public static string ToPrintFolderPath(this PrintBundleListItem bundle, string basePath) =>
        $"{basePath}\\{bundle.Customer}\\{bundle.BatchName}";

    public static string ToPrintFilePath(this PrintBundleListItem bundle, string basePath, string fileName) =>
        $"{basePath}\\{bundle.Customer}\\{bundle.BatchName}\\{bundle.Metadata.SheetArticleCode}\\{fileName}";
}
