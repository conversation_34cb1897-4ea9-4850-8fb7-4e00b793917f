﻿using MassTransit;
using Pondres.Omnia.OrderHub.Contracts.OrderTask;
using Pondres.Omnia.Print.Business.CommandHandler;
using Pondres.Omnia.Print.Common.Model;
using Pondres.Omnia.Print.Integrations.OrderHub.Extensions;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Integrations.OrderHub.Consumer;
public class OrderTaskMultiPrintDocumentSubmissionConsumer : IConsumer<OrderTaskBatchStartCommand>  
{
    private readonly QueueNewDocumentGroupCommandHandler queueNewDocumentGroupCommandHandler;
    private readonly ILogger logger;

    public OrderTaskMultiPrintDocumentSubmissionConsumer(QueueNewDocumentGroupCommandHandler queueNewDocumentGroupCommandHandler, ILogger logger)
    {
        this.queueNewDocumentGroupCommandHandler = queueNewDocumentGroupCommandHandler;
        this.logger = logger;
    }

    public async Task Consume(ConsumeContext<OrderTaskBatchStartCommand> context)
    {
        logger.Information("OrderTaskMultiPrintDocumentSubmissionConsumer received OrderTaskBatchStartCommand message for Order {OrderId}", context.Message.Metadata.OrderId);

        var totalSourceFileGroupCount = context.Message.Input.Data.FileGroups.Count;
        try
        {
            var creationModels = new List<PrintDocumentCreationModel>();
            for (var itemIndex = 0; itemIndex < totalSourceFileGroupCount; itemIndex++)
            {
                var sourceFileGroup = context.Message.Input.Data.FileGroups[itemIndex].Items;
                var printFileInformation = sourceFileGroup.Single(x => x.Type == OrderTaskBatchSourceDataType.DocumentFile);
                var printMetadataInformation = sourceFileGroup.Single(x => x.Type == OrderTaskBatchSourceDataType.DocumentMetadata);

                var creationModel = context.Message.ToPrintDocumentCreationModel(
                    printFileInformation: printFileInformation,
                    printMetadataInformation: printMetadataInformation,
                    itemIndex: itemIndex);

                creationModels.Add(creationModel);
            }

            var command = new QueueNewDocumentGroupCommand(context, creationModels);
            await queueNewDocumentGroupCommandHandler.HandleAsync(command);

            await PublishTaskSuccessfulAsync(
                publishEndpoint: context,
                taskId: context.Message.Metadata.TaskId,
                batchId: context.Message.Metadata.BatchId,
                creationModels: creationModels,
                totalItemCount: 1,
                itemIndex: 0);
        }
        catch (Exception exception)
        {
            await PublishTaskFailedAsync(
                publishEndpoint: context,
                taskId: context.Message.Metadata.TaskId,
                batchId: context.Message.Metadata.BatchId,
                exception: exception,
                totalItemCount: 1,
                itemIndex: 0);
        }
    }

    private static async Task PublishTaskFailedAsync(
        IPublishEndpoint publishEndpoint,
        Guid taskId,
        string batchId,
        Exception exception,
        int totalItemCount,
        int itemIndex)
    {
        Log.Debug("Publishing OrderTaskBatchItemFailed message");

        await publishEndpoint.Publish(new OrderTaskBatchItemFailed
        {
            Message = exception.Message,
            BatchId = batchId,
            TaskId = taskId,
            TotalItemCount = totalItemCount,
            Index = itemIndex
        });
    }

    private static async Task PublishTaskSuccessfulAsync(
        IPublishEndpoint publishEndpoint,
        Guid taskId,
        string batchId,
        List<PrintDocumentCreationModel> creationModels,
        int totalItemCount,
        int itemIndex)
    {
        Log.Debug("Publishing OrderTaskBatchItemCompleted message");

        await publishEndpoint.Publish(new OrderTaskBatchItemCompleted
        {
            BatchId = batchId,
            TaskId = taskId,
            ItemData = new OrderTaskBatchItemData()
            {
                Data = creationModels,
                Extension = "json"
            },
            Message = "Print order submitted",
            TotalItemCount = totalItemCount,
            Index = itemIndex
        });
    }
}
