﻿using Newtonsoft.Json;
using System;

namespace Pondres.Omnia.Print.Storage.Entities.PrintDocument;

public class PrintDocumentBundleRegistrationEntity
{
    [JsonProperty("bundleId")]
    public Guid BundleId { get; set; }

    [JsonProperty("batchName")] public string BatchName { get; set; } = string.Empty;

    [JsonProperty("createdOn")]
    public DateTimeOffset CreatedOn { get; set; }

    [JsonProperty("gordNumber")]
    public string? GordNumber { get; set; }

    [JsonProperty("taskNumber")]
    public int? TaskNumber { get; set; }

    [JsonProperty("sequenceId")]
    public int SequenceId { get; set; }
}
