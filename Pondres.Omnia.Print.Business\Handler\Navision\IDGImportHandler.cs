﻿using Pondres.Omnia.Print.Integrations.Navision.Model;
using Pondres.Omnia.Print.Integrations.Navision.Model.Bundle;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Handler.Navision;
public interface IDGImportHandler
{
    Task<Dictionary<SendBundleInfoToNavisionRequest, NavisionPrintBundle>> GetExcludedLocationBundlesForTimeRangeAsync(DateTimeOffset timeRangeStart, DateTimeOffset timeRangeEnd);
}
