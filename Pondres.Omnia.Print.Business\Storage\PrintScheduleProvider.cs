﻿using Pondres.Omnia.Print.Business.Extensions;
using Pondres.Omnia.Print.Business.Model.Schedule;
using Pondres.Omnia.Print.Contracts.Common;
using Pondres.Omnia.Print.Storage.Entities.Schedule;
using Pondres.Omnia.Print.Storage.Repositories;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Storage;

public class PrintScheduleProvider : IPrintScheduleProvider
{
    private readonly IReleaseSchedulesRepository releaseSchedulesRepository;
    private readonly IOverrideSchedulesRepository overrideSchedulesRepository;

    public PrintScheduleProvider(IReleaseSchedulesRepository releaseSchedulesRepository, IOverrideSchedulesRepository overrideSchedulesRepository)
    {
        this.releaseSchedulesRepository = releaseSchedulesRepository;
        this.overrideSchedulesRepository = overrideSchedulesRepository;
    }

    public async Task<PrintSchedule?> GetApplicableScheduleAsync(string? releaseSchedule)
    {
        if (string.IsNullOrWhiteSpace(releaseSchedule))
            return null;

        var overrideSchedule = await overrideSchedulesRepository.GetSingleOrDefaultAsync(releaseSchedule);
        var schedulesResponse = await releaseSchedulesRepository.GetSingleOrDefaultAsync(releaseSchedule);

        var schedulesEntity = schedulesResponse?.Resource;

        if (schedulesEntity == null)
            return null;

        var schedule = new PrintSchedule
        {
            Name = schedulesEntity.Id,
            ReleaseDays = GetScheduleDays(schedulesEntity),
            Active = schedulesEntity.Active,
        };

        if (overrideSchedule?.Resource != null && overrideSchedule.Resource.Active)
            OverrideScheduleDays(schedule, overrideSchedule);

        return schedule;
    }

    private static void OverrideScheduleDays(PrintSchedule printSchedule, ReleaseSchedulesEntity overrideSchedule)
    {
        foreach (var schedule in overrideSchedule.Schedule)
        {
            var daysOfWeekToReplace = schedule.Dates.Where(x =>
                (DateTime.Parse(x, CultureInfo.CurrentCulture) >= DateTime.Now.Date) &&
                (DateTime.Parse(x, CultureInfo.CurrentCulture) <= DateTime.Now.Date.AddDays(6)))
                .Select(y => DateTime.Parse(y, CultureInfo.CurrentCulture).DayOfWeek.ToString())
                .ToList();

            if (daysOfWeekToReplace.Count != 0)
            {
                printSchedule.ReleaseDays.Where(x => daysOfWeekToReplace.Contains(x.Day)).ToList().ForEach(y => printSchedule.ReleaseDays.Remove(y));

                daysOfWeekToReplace.ForEach(x => printSchedule.ReleaseDays.Add(new()
                {
                    Day = x,
                    Times = GetReleaseTimes(schedule.Releases)
                }));
            }
        }
    }

    private static List<PrintScheduleDay> GetScheduleDays(ReleaseSchedulesEntity scheduleEntity)
    {
        var printScheduleDays = new List<PrintScheduleDay>();

        foreach (var s in scheduleEntity.Schedule)
        {
            foreach (var d in s.DayOfWeek)
            {
                var day = new PrintScheduleDay
                {
                    Day = d,
                    Times = GetReleaseTimes(s.Releases)
                };
                printScheduleDays.Add(day);
            }
        }

        return printScheduleDays;
    }

    private static List<PrintScheduleTime> GetReleaseTimes(List<PrintScheduleTimeEntity> releases)
    {
        var times = new List<PrintScheduleTime>();

        foreach (var t in releases)
        {
            var time = new PrintScheduleTime
            {
                ReleaseTimeOffset = TimeSpan.Parse(t.Time),
                Type = t.Type.ToEnumOrDefault(PrintBundleMode.EmptyGroup),
            };
            times.Add(time);
        }

        return times;
    }
}