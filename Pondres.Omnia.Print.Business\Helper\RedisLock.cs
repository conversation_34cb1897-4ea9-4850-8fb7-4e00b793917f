﻿using Serilog;
using StackExchange.Redis;
using System;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Helper;

public sealed class RedisLock<TValue> : IAsyncDisposable where TValue : class, new()
{
    private readonly IDatabase redisDb;
    private readonly RedisValue token;
    private readonly TimeSpan lockDuration;
    private readonly RedisKey lockKey;
    private DateTime expiresAfter;

    public TValue Value { get; set; }
    public RedisKey Key { get; private set; }

    public bool Expired => DateTime.UtcNow > expiresAfter;

    public RedisLock(IDatabase redisDb, RedisKey key, TimeSpan lockDuration)
    {
        Key = key;
        lockKey = key.Append(":lock");

        this.redisDb = redisDb;
        this.lockDuration = lockDuration;

        token = Guid.NewGuid().ToString();

        Value = new();
    }

    public async ValueTask DisposeAsync()
    {
        try
        {
            var success = await redisDb.LockReleaseAsync(lockKey, token);
            if (!success)
                Log.Warning("Unable to release lock for key: {LockKey}, release returned unsuccessful", lockKey);
        }
        catch (Exception ex)
        {
            Log.Warning(ex, "Unable to release lock for key: {LockKey}", lockKey);
        }
    }

    public async Task LockAsync()
    {
        // wait for the maximum time to acquire the lock
        var waitUntil = DateTime.UtcNow.Add(lockDuration);
        while (DateTime.UtcNow < waitUntil)
        {
            expiresAfter = DateTime.UtcNow.Add(lockDuration);

            var result = await redisDb.LockTakeAsync(lockKey, token, lockDuration);

            if (result)
                return;

            await Task.Delay(100);
        }

        throw new RedisException($"Unable to lock redis cache for key: {lockKey}");
    }
}
