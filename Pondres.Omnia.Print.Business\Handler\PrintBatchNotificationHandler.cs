﻿using MassTransit;
using Pondres.Omnia.Print.Business.Helper;
using Pondres.Omnia.Print.Contracts.PrintTask;
using System;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Handler;

public class PrintBatchNotificationHandler : IPrintBatchNotificationHandler
{
    private readonly IRedisCacheHelper redisCacheHelper;
    private readonly TimeSpan cacheValueExpirationTime = TimeSpan.FromMinutes(30);

    public PrintBatchNotificationHandler(IRedisCacheHelper redisCacheHelper)
    {
        this.redisCacheHelper = redisCacheHelper;
    }

    public async Task PublishPrintBatchReadyAsync(IPublishEndpoint context, string batchName, string customer)
    {
        var mailSent = await redisCacheHelper.GetPrintBatchNotificationSentAsync(batchName, customer);

        if (!mailSent) // Prevent duplicate publish
        {
            await context.Publish(new PrintBatchReadyForPrint
            {
                BatchName = batchName,
                Customer = customer
            });

            await redisCacheHelper.SetPrintBatchNotificationSentAsync(batchName, customer, cacheValueExpirationTime);
        }
    }
}
