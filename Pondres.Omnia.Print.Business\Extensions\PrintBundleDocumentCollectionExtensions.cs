﻿using Pondres.Omnia.Print.Contracts.PrintBundle;
using Pondres.Omnia.Print.Storage.PrintBundle;
using System;
using System.Linq;

namespace Pondres.Omnia.Print.Business.Extensions;

public static class PrintBundleDocumentCollectionExtensions
{
    public static PrintBundleQuantitiesUpdated ToQuantitiesUpdatedEvent(this PrintBundleDocumentCollection collection) =>
        new()
        {
            PrintBundleId = collection.Id,
            DocumentsQuantity = collection.Documents.Sum(x => x.DocumentMetadata.Quantity),
            DocumentsPageCount = collection.Documents.Sum(x => x.DocumentMetadata.PageCount * x.DocumentMetadata.Quantity),
            DocumentsRecordCount = collection.Documents.Count,
            Timestamp = DateTimeOffset.Now
        };

}
