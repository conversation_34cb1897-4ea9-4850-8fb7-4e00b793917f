﻿using Newtonsoft.Json;
using Solude.StorageBase.Model;
using Pondres.Omnia.Print.Storage.Configuration;

namespace Pondres.Omnia.Print.Storage.Entities.Print;

public class PrintConfigurationEntity : CosmosEntity
{
    [JsonProperty("id")] public override string Id { get; set; } = string.Empty;
    [JsonIgnore]
    public override string PartitionKeyValue => Type;
    [JsonProperty("type")]
    public string Type { get; } = CosmosConfiguration.ConfigurationPartitionValue;
    [JsonProperty("version")]
    public override string Version { get; set; } = CosmosConfiguration.ConfigurationVersion;
}
