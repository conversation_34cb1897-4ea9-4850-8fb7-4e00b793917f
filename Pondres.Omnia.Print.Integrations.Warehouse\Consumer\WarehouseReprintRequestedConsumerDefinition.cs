﻿using MassTransit;
using System;

namespace Pondres.Omnia.Print.Integrations.OrderHub.Consumer;

public class WarehouseReprintRequestedConsumerDefinition : ConsumerDefinition<WarehouseReprintRequestedConsumer>
{
    public WarehouseReprintRequestedConsumerDefinition()
    {
    }

    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<WarehouseReprintRequestedConsumer> consumerConfigurator,
        IRegistrationContext context)
    {
        consumerConfigurator.UseMessageRetry(x => x.Interval(3, TimeSpan.FromMinutes(1)));
    }
}