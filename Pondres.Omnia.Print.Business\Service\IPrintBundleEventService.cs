﻿using Pondres.Omnia.Print.Contracts.Common;
using Pondres.Omnia.Print.Contracts.PrintBundle;
using Pondres.Omnia.Print.Storage.Entities.PrintBundle;
using System;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Service;

public interface IPrintBundleEventService
{
    Task<PrintBundleDetailsEntity> RegisterBundleCompletionAsync(PrintBundleCompleted message);

    Task<PrintBundleDetailsEntity> RegisterManualEventAsync(PrintBundleManualEvent message);

    Task<PrintBundleDetailsEntity> UpdateFileListAsync(PrintBundleImpositioningCompleted message);

    Task<PrintBundleDetailsEntity> UpdateStatusAsync(PrintBundleStatusUpdate message);

    Task<PrintBundleDetailsEntity> RegisterBundleSkippedAsync(PrintBundleSkipped message);
    Task<PrintBundleDetailsEntity> RegisterBundleStartedAsync(PrintBundleStarted message);

    Task<PrintBundleDetailsEntity> RegisterBundleCancellationAsync(PrintBundleCancelled message);

    Task UpdateQuantitiesAsync(PrintBundleQuantitiesUpdated message);

    Task<PrintBundleDetailsEntity> RegisterWaitingForStartEventAsync(
        Guid printBundleId,
        DateTimeOffset timestamp,
        PrintBundleStateType stateType);
}