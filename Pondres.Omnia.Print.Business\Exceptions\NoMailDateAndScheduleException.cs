﻿using System;
using System.Runtime.Serialization;

namespace Pondres.Omnia.Print.Business.Exceptions;
[Serializable]
public sealed class NoMailDateAndScheduleException : Exception
{
    public NoMailDateAndScheduleException(string documentId) : base($"MailDate or ReleaseSchedule cannot be empty on document {documentId}")
    {
    }

    private NoMailDateAndScheduleException(SerializationInfo info, StreamingContext context) : base(info, context)
    {
    }
}
