{"openapi": "3.0.1", "info": {"title": "Pondres.Omnia.Print.PrintManager", "version": "1.0"}, "servers": [{"url": "/print"}], "paths": {"/bundle/list": {"post": {"tags": ["PrintBundle"], "parameters": [{"name": "x-token", "in": "header", "description": "Access token", "required": true, "schema": {"type": "string"}}, {"name": "x-customer", "in": "header", "description": "Customer", "schema": {"type": "string"}}, {"name": "x-tenant", "in": "header", "description": "Tenant", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PrintBundleDefaultFilter"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PrintBundleDefaultFilter"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PrintBundleDefaultFilter"}]}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PrintBundleListItemPagedList"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PrintBundleListItemPagedList"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PrintBundleListItemPagedList"}}}}}}}, "/bundle/raw": {"get": {"tags": ["PrintBundle"], "parameters": [{"name": "printBundleId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "x-token", "in": "header", "description": "Access token", "required": true, "schema": {"type": "string"}}, {"name": "x-customer", "in": "header", "description": "Customer", "schema": {"type": "string"}}, {"name": "x-tenant", "in": "header", "description": "Tenant", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PrintBundleRawDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PrintBundleRawDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PrintBundleRawDetails"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/bundle/batchList": {"post": {"tags": ["PrintBundle"], "parameters": [{"name": "x-token", "in": "header", "description": "Access token", "required": true, "schema": {"type": "string"}}, {"name": "x-customer", "in": "header", "description": "Customer", "schema": {"type": "string"}}, {"name": "x-tenant", "in": "header", "description": "Tenant", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PrintBundleDefaultFilter"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PrintBundleDefaultFilter"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PrintBundleDefaultFilter"}]}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DGCodeBatchPagedList"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DGCodeBatchPagedList"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DGCodeBatchPagedList"}}}}}}}, "/bundle/bundleBatchList": {"post": {"tags": ["PrintBundle"], "parameters": [{"name": "x-token", "in": "header", "description": "Access token", "required": true, "schema": {"type": "string"}}, {"name": "x-customer", "in": "header", "description": "Customer", "schema": {"type": "string"}}, {"name": "x-tenant", "in": "header", "description": "Tenant", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PrintBundleBatchFilter"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PrintBundleBatchFilter"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PrintBundleBatchFilter"}]}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PrintBundleListItemPagedList"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PrintBundleListItemPagedList"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PrintBundleListItemPagedList"}}}}}}}, "/bundle/cancel": {"post": {"tags": ["PrintBundle"], "parameters": [{"name": "x-token", "in": "header", "description": "Access token", "required": true, "schema": {"type": "string"}}, {"name": "x-customer", "in": "header", "description": "Customer", "schema": {"type": "string"}}, {"name": "x-tenant", "in": "header", "description": "Tenant", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PrintBundleCancelCommand"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PrintBundleCancelCommand"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PrintBundleCancelCommand"}]}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PrintBundleCancelResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PrintBundleCancelResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PrintBundleCancelResponse"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}, "500": {"description": "Internal Server Error", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/bundle/release": {"post": {"tags": ["PrintBundle"], "parameters": [{"name": "x-token", "in": "header", "description": "Access token", "required": true, "schema": {"type": "string"}}, {"name": "x-customer", "in": "header", "description": "Customer", "schema": {"type": "string"}}, {"name": "x-tenant", "in": "header", "description": "Tenant", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PrintBundleReleaseRequest"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PrintBundleReleaseRequest"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PrintBundleReleaseRequest"}]}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PrintBundleReleaseResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PrintBundleReleaseResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PrintBundleReleaseResponse"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}, "500": {"description": "Internal Server Error", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/bundle/continue": {"post": {"tags": ["PrintBundle"], "parameters": [{"name": "x-token", "in": "header", "description": "Access token", "required": true, "schema": {"type": "string"}}, {"name": "x-customer", "in": "header", "description": "Customer", "schema": {"type": "string"}}, {"name": "x-tenant", "in": "header", "description": "Tenant", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PrintBundleContinueCommand"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PrintBundleContinueCommand"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PrintBundleContinueCommand"}]}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PrintBundleManualContinueResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PrintBundleManualContinueResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PrintBundleManualContinueResponse"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}, "500": {"description": "Internal Server Error", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/bundle/details": {"get": {"tags": ["PrintBundle"], "parameters": [{"name": "printBundleId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "x-token", "in": "header", "description": "Access token", "required": true, "schema": {"type": "string"}}, {"name": "x-customer", "in": "header", "description": "Customer", "schema": {"type": "string"}}, {"name": "x-tenant", "in": "header", "description": "Tenant", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PrintBundleDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PrintBundleDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PrintBundleDetails"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/bundle/printconfirm": {"post": {"tags": ["PrintBundle"], "parameters": [{"name": "x-token", "in": "header", "description": "Access token", "required": true, "schema": {"type": "string"}}, {"name": "x-customer", "in": "header", "description": "Customer", "schema": {"type": "string"}}, {"name": "x-tenant", "in": "header", "description": "Tenant", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PrintBundlePrintConfirmCommand"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PrintBundlePrintConfirmCommand"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PrintBundlePrintConfirmCommand"}]}}}}, "responses": {"200": {"description": "OK"}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/bundle/completedconfirm": {"post": {"tags": ["PrintBundle"], "parameters": [{"name": "x-token", "in": "header", "description": "Access token", "required": true, "schema": {"type": "string"}}, {"name": "x-customer", "in": "header", "description": "Customer", "schema": {"type": "string"}}, {"name": "x-tenant", "in": "header", "description": "Tenant", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PrintBundleCompletedConfirmCommand"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PrintBundleCompletedConfirmCommand"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PrintBundleCompletedConfirmCommand"}]}}}}, "responses": {"200": {"description": "OK"}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/bundle/scannedconfirm": {"post": {"tags": ["PrintBundle"], "parameters": [{"name": "x-token", "in": "header", "description": "Access token", "required": true, "schema": {"type": "string"}}, {"name": "x-customer", "in": "header", "description": "Customer", "schema": {"type": "string"}}, {"name": "x-tenant", "in": "header", "description": "Tenant", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PrintBundleScannedConfirmCommand"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PrintBundleScannedConfirmCommand"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PrintBundleScannedConfirmCommand"}]}}}}, "responses": {"200": {"description": "OK"}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/bundle/activeStatusRaw": {"get": {"tags": ["PrintBundle"], "parameters": [{"name": "printBundleId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "x-token", "in": "header", "description": "Access token", "required": true, "schema": {"type": "string"}}, {"name": "x-customer", "in": "header", "description": "Customer", "schema": {"type": "string"}}, {"name": "x-tenant", "in": "header", "description": "Tenant", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PrintBundleStatusResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PrintBundleStatusResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PrintBundleStatusResponse"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/bundle/removeDocument": {"post": {"tags": ["PrintBundle"], "parameters": [{"name": "x-token", "in": "header", "description": "Access token", "required": true, "schema": {"type": "string"}}, {"name": "x-customer", "in": "header", "description": "Customer", "schema": {"type": "string"}}, {"name": "x-tenant", "in": "header", "description": "Tenant", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PrintBundleRemoveDocumentCommand"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PrintBundleRemoveDocumentCommand"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PrintBundleRemoveDocumentCommand"}]}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PrintBundleDocumentRemovedResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PrintBundleDocumentRemovedResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PrintBundleDocumentRemovedResponse"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/bundle/listByIds": {"post": {"tags": ["PrintBundle"], "parameters": [{"name": "x-token", "in": "header", "description": "Access token", "required": true, "schema": {"type": "string"}}, {"name": "x-customer", "in": "header", "description": "Customer", "schema": {"type": "string"}}, {"name": "x-tenant", "in": "header", "description": "Tenant", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PrintBundleListItemPagedList"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PrintBundleListItemPagedList"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PrintBundleListItemPagedList"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/bundle/zippedExcludedLocationBundles": {"post": {"tags": ["PrintBundle"], "parameters": [{"name": "x-token", "in": "header", "description": "Access token", "required": true, "schema": {"type": "string"}}, {"name": "x-customer", "in": "header", "description": "Customer", "schema": {"type": "string"}}, {"name": "x-tenant", "in": "header", "description": "Tenant", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ZippedExcludedLocationPrintBundleCommand"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ZippedExcludedLocationPrintBundleCommand"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ZippedExcludedLocationPrintBundleCommand"}]}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "string", "format": "binary"}}, "application/json": {"schema": {"type": "string", "format": "binary"}}, "text/json": {"schema": {"type": "string", "format": "binary"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/document/pagedList": {"post": {"tags": ["PrintDocument"], "parameters": [{"name": "x-token", "in": "header", "description": "Access token", "required": true, "schema": {"type": "string"}}, {"name": "x-customer", "in": "header", "description": "Customer", "schema": {"type": "string"}}, {"name": "x-tenant", "in": "header", "description": "Tenant", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PrintDocumentListFilter"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PrintDocumentListFilter"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PrintDocumentListFilter"}]}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PrintDocumentListItemPagedList"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PrintDocumentListItemPagedList"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PrintDocumentListItemPagedList"}}}}}}}, "/document/pagedBatchList": {"post": {"tags": ["PrintDocument"], "parameters": [{"name": "x-token", "in": "header", "description": "Access token", "required": true, "schema": {"type": "string"}}, {"name": "x-customer", "in": "header", "description": "Customer", "schema": {"type": "string"}}, {"name": "x-tenant", "in": "header", "description": "Tenant", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PrintDocumentBatchListFilter"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PrintDocumentBatchListFilter"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PrintDocumentBatchListFilter"}]}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PrintDocumentListItemPagedList"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PrintDocumentListItemPagedList"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PrintDocumentListItemPagedList"}}}}}}}, "/document/cancelList": {"post": {"tags": ["PrintDocument"], "parameters": [{"name": "x-token", "in": "header", "description": "Access token", "required": true, "schema": {"type": "string"}}, {"name": "x-customer", "in": "header", "description": "Customer", "schema": {"type": "string"}}, {"name": "x-tenant", "in": "header", "description": "Tenant", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PrintDocumentReprintRequest"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PrintDocumentReprintRequest"}}}, "application/*+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PrintDocumentReprintRequest"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/releaseSchedules/getAll": {"get": {"tags": ["ReleaseSchedule"], "parameters": [{"name": "x-token", "in": "header", "description": "Access token", "required": true, "schema": {"type": "string"}}, {"name": "x-customer", "in": "header", "description": "Customer", "schema": {"type": "string"}}, {"name": "x-tenant", "in": "header", "description": "Tenant", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ReleaseSchedules"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ReleaseSchedules"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ReleaseSchedules"}}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/releaseSchedules/create": {"post": {"tags": ["ReleaseSchedule"], "parameters": [{"name": "x-token", "in": "header", "description": "Access token", "required": true, "schema": {"type": "string"}}, {"name": "x-customer", "in": "header", "description": "Customer", "schema": {"type": "string"}}, {"name": "x-tenant", "in": "header", "description": "Tenant", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ReleaseSchedules"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ReleaseSchedules"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ReleaseSchedules"}]}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ReleaseScheduleResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ReleaseScheduleResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ReleaseScheduleResult"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/releaseSchedules/update": {"post": {"tags": ["ReleaseSchedule"], "parameters": [{"name": "x-token", "in": "header", "description": "Access token", "required": true, "schema": {"type": "string"}}, {"name": "x-customer", "in": "header", "description": "Customer", "schema": {"type": "string"}}, {"name": "x-tenant", "in": "header", "description": "Tenant", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ReleaseSchedules"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ReleaseSchedules"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ReleaseSchedules"}]}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ReleaseScheduleResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ReleaseScheduleResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ReleaseScheduleResult"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/releaseSchedules/delete": {"delete": {"tags": ["ReleaseSchedule"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "string"}}, {"name": "x-token", "in": "header", "description": "Access token", "required": true, "schema": {"type": "string"}}, {"name": "x-customer", "in": "header", "description": "Customer", "schema": {"type": "string"}}, {"name": "x-tenant", "in": "header", "description": "Tenant", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/stats/bundleTotalsForDateRange": {"post": {"tags": ["Statistics"], "parameters": [{"name": "x-token", "in": "header", "description": "Access token", "required": true, "schema": {"type": "string"}}, {"name": "x-customer", "in": "header", "description": "Customer", "schema": {"type": "string"}}, {"name": "x-tenant", "in": "header", "description": "Tenant", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PrintBundleDateRangeFilter"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PrintBundleDateRangeFilter"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PrintBundleDateRangeFilter"}]}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PrintBundleTotalsResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PrintBundleTotalsResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PrintBundleTotalsResult"}}}}}}}}, "components": {"schemas": {"DGCodeBatch": {"type": "object", "properties": {"customer": {"type": "string"}, "gordNumber": {"type": "string", "nullable": true}, "dgCode": {"type": "string", "nullable": true}, "batchName": {"type": "string"}, "status": {"type": "string"}, "details": {"type": "array", "items": {"$ref": "#/components/schemas/DGCodeBatchDetails"}}, "printBundles": {"type": "array", "items": {"$ref": "#/components/schemas/PrintBundleListItem"}}}, "additionalProperties": false}, "DGCodeBatchDetails": {"type": "object", "properties": {"printBundleIds": {"type": "array", "items": {"type": "string"}}, "printerType": {"allOf": [{"$ref": "#/components/schemas/PrinterType"}], "nullable": true}, "printFileLocation": {"type": "string"}, "sheets": {"type": "integer", "format": "int32"}, "documentCount": {"type": "integer", "format": "int32"}, "status": {"type": "string"}, "printerDetails": {"type": "array", "items": {"$ref": "#/components/schemas/PrinterDetails"}}}, "additionalProperties": false}, "DGCodeBatchPagedList": {"type": "object", "properties": {"continuationToken": {"type": "string", "nullable": true}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/DGCodeBatch"}}}, "additionalProperties": false}, "MapPrintBundleState": {"enum": ["None", "Cancelled", "Skipped", "WaitingForBatch", "BatchCreationFailure", "Impositioning", "ImpositioningOnHold", "Printed", "WaitingForPrint", "WaitingForStart", "Created", "Started", "Completed", "Final", "WaitingForScan", "Scanned"], "type": "string"}, "OrderCategories": {"type": "object", "properties": {"one": {"type": "string"}, "two": {"type": "string"}, "three": {"type": "string"}}, "additionalProperties": false}, "OrderMetadata": {"type": "object", "properties": {"customer": {"type": "string"}, "customerReference": {"type": "string"}, "flow": {"type": "string"}, "orderId": {"type": "string", "format": "uuid"}, "categories": {"allOf": [{"$ref": "#/components/schemas/OrderCategories"}]}}, "additionalProperties": false}, "PackingType": {"enum": ["All", "C4", "C5", "C6", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "type": "string"}, "PrintBundleBatchFilter": {"type": "object", "properties": {"continuationToken": {"type": "string", "nullable": true}, "maxPageSize": {"type": "integer", "format": "int32"}, "printBundleIds": {"type": "array", "items": {"type": "string"}}, "batchNames": {"type": "array", "items": {"type": "string"}}, "state": {"allOf": [{"$ref": "#/components/schemas/PrintBundleStateType"}], "nullable": true}}, "additionalProperties": false}, "PrintBundleCancelCommand": {"type": "object", "properties": {"force": {"type": "boolean"}, "reprint": {"type": "boolean"}, "printBundleId": {"type": "string", "format": "uuid"}, "username": {"type": "string"}}, "additionalProperties": false}, "PrintBundleCancelResponse": {"type": "object", "properties": {"message": {"type": "string"}, "printBundleId": {"type": "string", "format": "uuid"}, "success": {"type": "boolean"}}, "additionalProperties": false}, "PrintBundleCompletedConfirmCommand": {"type": "object", "properties": {"printBundleId": {"type": "string", "format": "uuid"}, "username": {"type": "string"}}, "additionalProperties": false}, "PrintBundleContinueCommand": {"type": "object", "properties": {"force": {"type": "boolean"}, "printBundleId": {"type": "string", "format": "uuid"}, "username": {"type": "string"}}, "additionalProperties": false}, "PrintBundleDateRangeFilter": {"type": "object", "properties": {"printerType": {"allOf": [{"$ref": "#/components/schemas/PrinterType"}], "nullable": true}, "customer": {"type": "string"}, "createdOnFrom": {"type": "string", "format": "date-time"}, "createdOnTo": {"type": "string", "format": "date-time"}, "includeHiddenCustomers": {"type": "boolean"}}, "additionalProperties": false}, "PrintBundleDefaultFilter": {"type": "object", "properties": {"bundleId": {"type": "string"}, "carrier": {"type": "string"}, "continuationToken": {"type": "string", "nullable": true}, "customer": {"type": "string"}, "batchName": {"type": "string"}, "dgCode": {"type": "string"}, "gordNumber": {"type": "string"}, "mailDateFrom": {"type": "string", "format": "date-time", "nullable": true}, "mailDateTo": {"type": "string", "format": "date-time", "nullable": true}, "maxPageSize": {"type": "integer", "format": "int32"}, "createdOnFrom": {"type": "string", "format": "date-time", "nullable": true}, "createdOnTo": {"type": "string", "format": "date-time", "nullable": true}, "printerType": {"allOf": [{"$ref": "#/components/schemas/PrinterType"}], "nullable": true}, "printMode": {"type": "string"}, "includeHiddenCustomers": {"type": "boolean"}, "includeEmptyBundles": {"type": "boolean"}, "state": {"allOf": [{"$ref": "#/components/schemas/PrintBundleStateType"}], "nullable": true}}, "additionalProperties": false}, "PrintBundleDetails": {"type": "object", "properties": {"batchName": {"type": "string"}, "createdOn": {"type": "string", "format": "date-time"}, "plannedOn": {"type": "string", "format": "date-time", "nullable": true}, "releasedOn": {"type": "string", "format": "date-time", "nullable": true}, "customer": {"type": "string"}, "documents": {"type": "integer", "format": "int32"}, "totalPageCount": {"type": "integer", "format": "int32"}, "id": {"type": "string"}, "initiatedBy": {"type": "string"}, "gordNumber": {"type": "string", "nullable": true}, "taskNumber": {"type": "integer", "format": "int32"}, "metadata": {"allOf": [{"$ref": "#/components/schemas/PrintDocumentCreationMetadata"}]}, "sheets": {"type": "integer", "format": "int32"}, "status": {"allOf": [{"$ref": "#/components/schemas/PrintBundleStatus"}]}, "statusHistory": {"type": "array", "items": {"$ref": "#/components/schemas/PrintBundleStatus"}}, "manualEvents": {"type": "array", "items": {"$ref": "#/components/schemas/PrintBundleManualEvent"}}, "files": {"type": "array", "items": {"type": "string"}}, "secondaryBundles": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false}, "PrintBundleDocumentRemovedResponse": {"type": "object", "properties": {"printBundleId": {"type": "string", "format": "uuid"}, "removedDocumentId": {"type": "string"}}, "additionalProperties": false}, "PrintBundleInputOption": {"enum": ["None", "Cancel", "Continue", "PrintConfirmation", "RemoveDocument", "Release"], "type": "string"}, "PrintBundleListItem": {"type": "object", "properties": {"batchName": {"type": "string"}, "createdOn": {"type": "string", "format": "date-time"}, "customer": {"type": "string"}, "documents": {"type": "integer", "format": "int32"}, "totalPageCount": {"type": "integer", "format": "int32"}, "id": {"type": "string"}, "initiatedBy": {"type": "string"}, "actions": {"type": "array", "items": {"$ref": "#/components/schemas/PrintBundleInputOption"}}, "metadata": {"allOf": [{"$ref": "#/components/schemas/PrintDocumentCreationMetadata"}]}, "gordNumber": {"type": "string", "nullable": true}, "taskNumber": {"type": "integer", "format": "int32"}, "sheets": {"type": "integer", "format": "int32"}, "status": {"allOf": [{"$ref": "#/components/schemas/PrintBundleStatus"}]}, "files": {"type": "array", "items": {"type": "string"}}, "availablePrintBundleModes": {"type": "array", "items": {"$ref": "#/components/schemas/PrintBundleMode"}}}, "additionalProperties": false}, "PrintBundleListItemPagedList": {"type": "object", "properties": {"continuationToken": {"type": "string", "nullable": true}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/PrintBundleListItem"}}}, "additionalProperties": false}, "PrintBundleManualContinueResponse": {"type": "object", "properties": {"message": {"type": "string"}, "printBundleId": {"type": "string", "format": "uuid"}, "success": {"type": "boolean"}}, "additionalProperties": false}, "PrintBundleManualEvent": {"type": "object", "properties": {"event": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}, "username": {"type": "string"}}, "additionalProperties": false}, "PrintBundleMode": {"enum": ["MaxFullSheets", "EmptyGroup"], "type": "string"}, "PrintBundleOptions": {"type": "object", "properties": {"bundleMode": {"allOf": [{"$ref": "#/components/schemas/PrintBundleMode"}]}}, "additionalProperties": false}, "PrintBundlePrintConfirmCommand": {"type": "object", "properties": {"printBundleId": {"type": "string", "format": "uuid"}, "username": {"type": "string"}}, "additionalProperties": false}, "PrintBundleRawDetails": {"type": "object", "properties": {"rawJson": {"type": "string"}}, "additionalProperties": false}, "PrintBundleReleaseRequest": {"type": "object", "properties": {"printBundleId": {"type": "string", "format": "uuid"}, "bundleOptions": {"allOf": [{"$ref": "#/components/schemas/PrintBundleOptions"}], "nullable": true}, "username": {"type": "string"}, "releaseDateTime": {"type": "string", "format": "date-time", "nullable": true}, "gordNumber": {"type": "string", "nullable": true}, "taskNumber": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "PrintBundleReleaseResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "printBundleId": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "PrintBundleRemoveDocumentCommand": {"type": "object", "properties": {"printBundleId": {"type": "string", "format": "uuid"}, "documentId": {"type": "string"}}, "additionalProperties": false}, "PrintBundleScannedConfirmCommand": {"type": "object", "properties": {"printBundleId": {"type": "string", "format": "uuid"}, "username": {"type": "string"}}, "additionalProperties": false}, "PrintBundleStateType": {"enum": ["Active", "Completed", "NotStarted"], "type": "string"}, "PrintBundleStatus": {"type": "object", "properties": {"isInFailedState": {"type": "boolean"}, "message": {"type": "string"}, "status": {"allOf": [{"$ref": "#/components/schemas/MapPrintBundleState"}]}, "timestamp": {"type": "string", "format": "date-time"}, "waitingForInput": {"type": "boolean"}}, "additionalProperties": false}, "PrintBundleStatusResponse": {"type": "object", "properties": {"correlationId": {"type": "string", "format": "uuid"}, "currentState": {"type": "string"}, "version": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "PrintBundleTotalsItem": {"type": "object", "properties": {"documentCount": {"type": "integer", "format": "int32"}, "sheetCount": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "PrintBundleTotalsPerDayItem": {"type": "object", "properties": {"date": {"type": "string", "format": "date-time"}, "hours": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/PrintBundleTotalsItem"}}, "totals": {"allOf": [{"$ref": "#/components/schemas/PrintBundleTotalsItem"}]}, "weekNumber": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "PrintBundleTotalsResult": {"type": "object", "properties": {"days": {"type": "array", "items": {"$ref": "#/components/schemas/PrintBundleTotalsPerDayItem"}}, "totalsPerWeek": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/PrintBundleTotalsItem"}}, "totalsPerHour": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/PrintBundleTotalsItem"}}}, "additionalProperties": false}, "PrintDocumentBatchListFilter": {"type": "object", "properties": {"continuationToken": {"type": "string", "nullable": true}, "maxPageSize": {"type": "integer", "format": "int32"}, "orderIds": {"type": "array", "items": {"type": "string", "format": "uuid"}}, "references": {"type": "array", "items": {"type": "string"}}, "orderReferences": {"type": "array", "items": {"type": "string"}}, "barcodes": {"type": "array", "items": {"type": "string"}}, "gordNumbers": {"type": "array", "items": {"type": "string"}}, "taskNumbers": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "sequenceIds": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, "additionalProperties": false}, "PrintDocumentBundle": {"type": "object", "properties": {"bundleId": {"type": "string", "format": "uuid"}, "batchName": {"type": "string"}, "createdOn": {"type": "string", "format": "date-time"}, "gordNumber": {"type": "string", "nullable": true}, "taskNumber": {"type": "integer", "format": "int32", "nullable": true}, "sequenceId": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "PrintDocumentCreationMetadata": {"type": "object", "properties": {"carrier": {"type": "string"}, "documentFormat": {"type": "string"}, "customerDocumentReference": {"type": "string", "nullable": true}, "documentStatus": {"type": "string", "nullable": true}, "endLocation": {"type": "string"}, "laminate": {"type": "boolean"}, "quantity": {"type": "integer", "format": "int32"}, "mailDate": {"type": "string", "format": "date-time"}, "postalDestination": {"type": "string"}, "printerType": {"allOf": [{"$ref": "#/components/schemas/PrinterType"}], "nullable": true}, "printMode": {"type": "string", "nullable": true}, "sheetArticleCode": {"type": "string"}, "sheetPageCount": {"type": "integer", "format": "int32"}, "sheetFormat": {"type": "string"}, "dgCode": {"type": "string", "nullable": true}, "pageCount": {"type": "integer", "format": "int32"}, "packaging": {"type": "string", "nullable": true}, "packingType": {"allOf": [{"$ref": "#/components/schemas/PackingType"}]}, "packingName": {"type": "string", "nullable": true}, "releaseSchedule": {"type": "string", "nullable": true}, "sheetArticleDescription": {"type": "string", "nullable": true}, "attachments": {"type": "array", "items": {"$ref": "#/components/schemas/PrintDocumentCreationMetadataAttachment"}}, "isPrimary": {"type": "boolean"}}, "additionalProperties": false}, "PrintDocumentCreationMetadataAttachment": {"type": "object", "properties": {"articleCode": {"type": "string"}, "articleName": {"type": "string"}, "pickLocation": {"type": "string"}}, "additionalProperties": false}, "PrintDocumentListFilter": {"type": "object", "properties": {"continuationToken": {"type": "string", "nullable": true}, "maxPageSize": {"type": "integer", "format": "int32"}, "createdFromDate": {"type": "string", "format": "date-time", "nullable": true}, "createdToDate": {"type": "string", "format": "date-time", "nullable": true}, "customer": {"type": "string"}, "documentId": {"type": "string"}, "customerDocumentReference": {"type": "string"}, "orderId": {"type": "string", "format": "uuid", "nullable": true}, "bundleId": {"type": "string", "format": "uuid", "nullable": true}, "batchName": {"type": "string"}, "gordNumber": {"type": "string"}, "taskNumber": {"type": "integer", "format": "int32"}, "sequenceId": {"type": "integer", "format": "int32"}, "barCode": {"type": "string"}}, "additionalProperties": false}, "PrintDocumentListItem": {"type": "object", "properties": {"id": {"type": "string"}, "orderMetadata": {"allOf": [{"$ref": "#/components/schemas/OrderMetadata"}]}, "customerDocumentReference": {"type": "string", "nullable": true}, "isPrimary": {"type": "boolean"}, "createdOn": {"type": "string", "format": "date-time"}, "lastUpdatedOn": {"type": "string", "format": "date-time"}, "lastBundle": {"allOf": [{"$ref": "#/components/schemas/PrintDocumentBundle"}], "nullable": true}}, "additionalProperties": false}, "PrintDocumentListItemPagedList": {"type": "object", "properties": {"continuationToken": {"type": "string", "nullable": true}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/PrintDocumentListItem"}}}, "additionalProperties": false}, "PrintDocumentReprintRequest": {"type": "object", "properties": {"orderId": {"type": "string", "format": "uuid"}, "customer": {"type": "string"}, "username": {"type": "string"}}, "additionalProperties": false}, "PrintScheduleTime": {"type": "object", "properties": {"time": {"type": "string"}, "type": {"allOf": [{"$ref": "#/components/schemas/PrintBundleMode"}]}}, "additionalProperties": false}, "PrinterDetails": {"type": "object", "properties": {"printFileName": {"type": "string"}, "printFilePath": {"type": "string"}}, "additionalProperties": false}, "PrinterType": {"enum": ["FC", "Food", "Flatbed", "ZW", "CFFC", "fc"], "type": "string"}, "ReleasePrintSchedule": {"type": "object", "properties": {"dayOfWeek": {"type": "array", "items": {"type": "string"}}, "releases": {"type": "array", "items": {"$ref": "#/components/schemas/PrintScheduleTime"}}}, "additionalProperties": false}, "ReleaseScheduleResult": {"type": "object", "properties": {"isValid": {"type": "boolean"}, "message": {"type": "string"}}, "additionalProperties": false}, "ReleaseSchedules": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string"}, "schedule": {"type": "array", "items": {"$ref": "#/components/schemas/ReleasePrintSchedule"}}, "active": {"type": "string"}}, "additionalProperties": false}, "ZippedExcludedLocationPrintBundleCommand": {"type": "object", "properties": {"timeRangeStart": {"type": "string", "format": "date-time"}, "timeRangeEnd": {"type": "string", "format": "date-time"}}, "additionalProperties": false}}}}