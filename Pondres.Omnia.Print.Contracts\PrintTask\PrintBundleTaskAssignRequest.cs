﻿using System;

namespace Pondres.Omnia.Print.Contracts.PrintBatch
{
    public class PrintBundleTaskAssignRequest
    {
        public Guid PrintBundleId { get; set; }
        public string BatchName { get; set; } = string.Empty;
        public string? DGCode { get; set; }
        public DateTimeOffset Timestamp { get; set; }
        public int DocumentCount { get; set; }
        public int SheetCount { get; set; }
    }
}
