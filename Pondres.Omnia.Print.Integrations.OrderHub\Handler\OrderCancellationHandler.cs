﻿using MassTransit;
using Pondres.Omnia.Print.Contracts.Common;
using Pondres.Omnia.Print.Contracts.PrintBundle;
using Pondres.Omnia.Print.Contracts.PrintDocument;
using Pondres.Omnia.Print.Storage.Repositories;
using System;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Integrations.OrderHub.Handler;
public class OrderCancellationHandler : IOrderCancellationHandler
{
    private readonly IPrintDocumentRepository printDocumentRepository;
    private readonly IPrintBundleDetailsRepository printBundleRepository;
    private readonly IBus bus;

    public OrderCancellationHandler(IPrintDocumentRepository printDocumentRepository, IPrintBundleDetailsRepository printBundleRepository, IBus bus)
    {
        this.printDocumentRepository = printDocumentRepository;
        this.printBundleRepository = printBundleRepository;
        this.bus = bus;
    }

    public async Task HandleOrderCancellationAsync(Guid orderId, string customer)
    {
        var documents = await printDocumentRepository.GetAllForOrderIdAsync(customer, orderId, throwIfNoDocuments: false);

        foreach (var document in documents)
        {
            var lastBundle = document.LastBundle ?? throw new InvalidOperationException("Last bundle should not be null when cancelling order");
            var canRemoveDocument = await BundleIsInWaitingForStartStateAsync(lastBundle.BundleId);
            if (canRemoveDocument)
                await bus.Publish(new PrintBundleRemoveDocumentRequest(lastBundle.BundleId, document.Id));
            else
                await bus.Publish(new PrintDocumentRemovalFromBundleFailed(
                    Customer: document.OrderMetadata.Customer,
                    CustomerReference: document.OrderMetadata.CustomerReference,
                    CustomerDocumentReference: document.CustomerDocumentReference,
                    OrderId: orderId,
                    PrintBundleId: lastBundle.BundleId,
                    DocumentId: document.Id,
                    TimeStamp: DateTimeOffset.Now));
        }
    }

    private async Task<bool> BundleIsInWaitingForStartStateAsync(Guid bundleId)
    {
        var bundle = await printBundleRepository.GetSingleAsync(bundleId.ToString());
        return bundle.Resource.LastStatus.Status == nameof(MapPrintBundleState.WaitingForStart);
    }
}
