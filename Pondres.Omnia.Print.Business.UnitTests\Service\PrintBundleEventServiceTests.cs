﻿using FluentAssertions;
using Moq;
using Moq.AutoMock;
using Solude.StorageBase.Model;
using Pondres.Omnia.Print.Business.Service;
using Pondres.Omnia.Print.Contracts.Common;
using Pondres.Omnia.Print.Contracts.PrintBundle;
using Pondres.Omnia.Print.Storage.Entities.PrintBundle;
using Pondres.Omnia.Print.Storage.Entities.PrintBundleStatus;
using Pondres.Omnia.Print.Storage.Repositories;
using System;
using System.Linq;
using System.Threading.Tasks;
using Xunit;

namespace Pondres.Omnia.Print.Business.UnitTests.Service;

public class PrintBundleEventServiceTests
{
    private readonly AutoMocker mocker;
    private readonly PrintBundleEventService service;

    public PrintBundleEventServiceTests()
    {
        mocker = new AutoMocker(MockBehavior.Strict);

        service = mocker.CreateInstance<PrintBundleEventService>();
    }

    [Fact]
    public async Task RegisterBundleCompletionAsync_NewerState_CompletesBundleState()
    {
        // Arrange
        var now = DateTimeOffset.Now;

        var bundleId = Guid.NewGuid();
        var bundleEntity = new PrintBundleDetailsEntity
        {
            Id = bundleId.ToString(),
            BatchName = "batchName",
            CreatedOn = now,
            Customer = "customer",
            Quantities = new PrintBundleQuantitiesEntity
            {
                DocumentCount = 1,
            },
            Files =
            [
                "file1",
                "file2"
            ],
            InitiatedBy = "Tester",
            State = PrintBundleStateType.Active,
            StateName = PrintBundleStateType.Active.ToString(),
            ManualEvents = [],
            LastStatus = new PrintBundleStatusDetailsEntity
            {
                Timestamp = now
            }
        };

        var message = new PrintBundleCompleted
        {
            PrintBundleId = bundleId,
            Timestamp = now.AddMinutes(1),
            BatchName = bundleEntity.BatchName,
            CompletedBy = "Tester",
            Message = "Message",
            Status = "Status"
        };

        var bundleEntityDetailsResponse = new CosmosResponse<PrintBundleDetailsEntity>(string.Empty, bundleEntity);

        mocker.GetMock<IPrintBundleDetailsRepository>()
            .Setup(x => x.GetSingleAsync(bundleEntity.Id, false))
            .ReturnsAsync(bundleEntityDetailsResponse);

        mocker.GetMock<IPrintBundleDetailsRepository>()
            .Setup(x => x.ReplaceConsistentAsync(bundleEntityDetailsResponse, It.IsAny<Func<PrintBundleDetailsEntity, bool>>()))
            .Returns(Task.CompletedTask);

        // Act
        var updatedEntity = await service.RegisterBundleCompletionAsync(message);

        // Assert
        updatedEntity.State.Should().Be(PrintBundleStateType.Completed);
        updatedEntity.LastStatus.Should().NotBeNull();

        var statusEntry = updatedEntity.LastStatus;
        statusEntry.Status.Should().Be(message.Status);
        statusEntry.IsInFailedState.Should().BeFalse();
        statusEntry.Message.Should().Be(message.Message);
        statusEntry.Timestamp.Should().Be(message.Timestamp);
        statusEntry.WaitingForInput.Should().BeFalse();
    }

    [Fact]
    public async Task RegisterBundleCompletionAsync_OlderState_IgnoresBundleState()
    {
        // Arrange
        var now = DateTimeOffset.Now;

        var bundleId = Guid.NewGuid();
        var bundleEntity = new PrintBundleDetailsEntity
        {
            Id = bundleId.ToString(),
            BatchName = "batchName",
            CreatedOn = now,
            Customer = "customer",
            Quantities = new PrintBundleQuantitiesEntity
            {
                DocumentCount = 1,
            },
            Files =
            [
                "file1",
                "file2"
            ],
            InitiatedBy = "Tester",
            State = PrintBundleStateType.Active,
            StateName = PrintBundleStateType.Active.ToString(),
            ManualEvents = [],
            LastStatus = new PrintBundleStatusDetailsEntity
            {
                Timestamp = now
            }
        };

        var message = new PrintBundleCompleted
        {
            PrintBundleId = bundleId,
            Timestamp = now.AddMinutes(-1),
            BatchName = bundleEntity.BatchName,
            CompletedBy = "Tester",
            Message = "Message",
            Status = "Status"
        };

        var bundleEntityDetailsResponse = new CosmosResponse<PrintBundleDetailsEntity>(string.Empty, bundleEntity);

        mocker.GetMock<IPrintBundleDetailsRepository>()
            .Setup(x => x.GetSingleAsync(bundleEntity.Id, false))
            .ReturnsAsync(bundleEntityDetailsResponse);

        mocker.GetMock<IPrintBundleDetailsRepository>()
            .Setup(x => x.ReplaceConsistentAsync(bundleEntityDetailsResponse, It.IsAny<Func<PrintBundleDetailsEntity, bool>>()))
            .Returns(Task.CompletedTask);

        // Act
        var updatedEntity = await service.RegisterBundleCompletionAsync(message);

        // Assert
        updatedEntity.State.Should().NotBe(PrintBundleStateType.Completed);
        updatedEntity.LastStatus.Should().NotBeNull();

        var statusEntry = updatedEntity.LastStatus;
        statusEntry.Status.Should().NotBe(message.Status);
        statusEntry.IsInFailedState.Should().BeFalse();
        statusEntry.Message.Should().NotBe(message.Message);
        statusEntry.Timestamp.Should().NotBe(message.Timestamp);
        statusEntry.WaitingForInput.Should().BeFalse();

        mocker.GetMock<IPrintBundleDetailsRepository>()
            .Verify(x => x.ReplaceConsistentAsync(bundleEntityDetailsResponse, It.IsAny<Func<PrintBundleDetailsEntity, bool>>()), Times.Never);
    }

    [Fact]
    public async Task RegisterManualEventAsync_RegistersEvent()
    {
        // Arrange
        var bundleId = Guid.NewGuid();
        var bundleEntity = new PrintBundleDetailsEntity
        {
            Id = bundleId.ToString(),
            BatchName = "batchName",
            CreatedOn = DateTimeOffset.Now,
            Customer = "customer",
            Quantities = new PrintBundleQuantitiesEntity
            {
                DocumentCount = 1,
            },
            Files =
            [
                "file1",
                "file2"
            ],
            InitiatedBy = "Tester",
            State = PrintBundleStateType.Active,
            StateName = PrintBundleStateType.Active.ToString(),
            ManualEvents = []
        };

        var message = new PrintBundleManualEvent
        {
            Event = "event",
            PrintBundleId = bundleId,
            Timestamp = DateTimeOffset.Now,
            Username = "username"
        };

        var bundleEntityDetailsResponse = new CosmosResponse<PrintBundleDetailsEntity>(string.Empty, bundleEntity);

        mocker.GetMock<IPrintBundleDetailsRepository>()
            .Setup(x => x.GetSingleAsync(bundleEntity.Id, false))
            .ReturnsAsync(bundleEntityDetailsResponse);

        mocker.GetMock<IPrintBundleDetailsRepository>()
            .Setup(x => x.ReplaceConsistentAsync(bundleEntityDetailsResponse, It.IsAny<Func<PrintBundleDetailsEntity, bool>>()))
            .Returns(Task.CompletedTask);

        // Act
        var updatedEntity = await service.RegisterManualEventAsync(message);

        // Assert
        updatedEntity.ManualEvents.Should().ContainSingle();

        var manualEvent = updatedEntity.ManualEvents.Single();
        manualEvent.Event.Should().Be(message.Event);
        manualEvent.Timestamp.Should().Be(message.Timestamp);
        manualEvent.Username.Should().Be(message.Username);
    }

    [Fact]
    public async Task UpdateFileListAsync_UpdatesFileList()
    {
        // Arrange
        var bundleId = Guid.NewGuid();
        var bundleEntity = new PrintBundleDetailsEntity
        {
            Id = bundleId.ToString(),
            BatchName = "batchName",
            CreatedOn = DateTimeOffset.Now,
            Customer = "customer",
            Quantities = new PrintBundleQuantitiesEntity
            {
                DocumentCount = 1,
            },
            Files = [],
            InitiatedBy = "Tester",
            State = PrintBundleStateType.Active,
            StateName = PrintBundleStateType.Active.ToString(),
            ManualEvents = []
        };

        var message = new PrintBundleImpositioningCompleted
        {
            PrintBundleId = bundleId,
            Customer = bundleEntity.Customer,
            ResultFileNames =
            [
                "File1",
                "File2"
            ]
        };

        var bundleEntityDetailsResponse = new CosmosResponse<PrintBundleDetailsEntity>(string.Empty, bundleEntity);

        mocker.GetMock<IPrintBundleDetailsRepository>()
            .Setup(x => x.GetSingleAsync(bundleEntity.Id, false))
            .ReturnsAsync(bundleEntityDetailsResponse);

        mocker.GetMock<IPrintBundleDetailsRepository>()
            .Setup(x => x.ReplaceConsistentAsync(bundleEntityDetailsResponse, It.IsAny<Func<PrintBundleDetailsEntity, bool>>()))
            .Returns(Task.CompletedTask);

        // Act
        var updatedEntity = await service.UpdateFileListAsync(message);

        // Assert
        updatedEntity.Files.Should().HaveCount(message.ResultFileNames.Count);
        updatedEntity.Files.Should().OnlyContain(file => message.ResultFileNames.Any(resultfile => resultfile == file));
    }

    [Fact]
    public async Task UpdateStatusAsync_OlderStatus_IgnoresStatus()
    {
        // Arrange
        var now = DateTimeOffset.Now;

        var bundleId = Guid.NewGuid();
        var bundleEntity = new PrintBundleDetailsEntity
        {
            Id = bundleId.ToString(),
            BatchName = "batchName",
            CreatedOn = now,
            Customer = "customer",
            Quantities = new PrintBundleQuantitiesEntity
            {
                DocumentCount = 1,
            },
            Files = [],
            InitiatedBy = "Tester",
            State = PrintBundleStateType.Active,
            StateName = PrintBundleStateType.Active.ToString(),
            ManualEvents = [],
            LastStatus = new PrintBundleStatusDetailsEntity
            {
                IsInFailedState = true,
                Message = "Failed",
                Status = "Failed",
                Timestamp = now,
                WaitingForInput = false
            }
        };

        var message = new PrintBundleStatusUpdate
        {
            PrintBundleId = bundleId,
            BatchName = bundleEntity.BatchName,
            WaitingForInput = true,
            IsInFailedState = false,
            Message = "Message",
            Status = "Status",
            Timestamp = now.AddDays(-1)
        };

        var bundleEntityDetailsResponse = new CosmosResponse<PrintBundleDetailsEntity>(string.Empty, bundleEntity);

        mocker.GetMock<IPrintBundleDetailsRepository>()
            .Setup(x => x.GetSingleAsync(bundleEntity.Id, false))
            .ReturnsAsync(bundleEntityDetailsResponse);

        mocker.GetMock<IPrintBundleDetailsRepository>()
            .Setup(x => x.ReplaceConsistentAsync(bundleEntityDetailsResponse, It.IsAny<Func<PrintBundleDetailsEntity, bool>>()))
            .Returns(Task.CompletedTask);

        // Act
        var updatedEntity = await service.UpdateStatusAsync(message);

        // Assert
        updatedEntity.LastStatus.Should().NotBeNull();

        var statusEntry = updatedEntity.LastStatus;

        statusEntry.IsInFailedState.Should().BeTrue();
        statusEntry.Message.Should().Be("Failed");
        statusEntry.Status.Should().Be("Failed");
        statusEntry.WaitingForInput.Should().BeFalse();
        statusEntry.Timestamp.Should().Be(now);
    }

    [Fact]
    public async Task UpdateStatusAsync_NewerStatus_UpdatesStatus()
    {
        // Arrange
        var bundleId = Guid.NewGuid();
        var bundleEntity = new PrintBundleDetailsEntity
        {
            Id = bundleId.ToString(),
            BatchName = "batchName",
            CreatedOn = DateTimeOffset.Now,
            Customer = "customer",
            Quantities = new PrintBundleQuantitiesEntity
            {
                DocumentCount = 1,
            },
            Metadata = new(),
            Files = [],
            InitiatedBy = "Tester",
            State = PrintBundleStateType.Active,
            StateName = PrintBundleStateType.Active.ToString(),
            ManualEvents = [],
            LastStatus = new PrintBundleStatusDetailsEntity
            {
                IsInFailedState = true,
                Message = "Failed",
                Status = "Failed",
                Timestamp = DateTimeOffset.Now.AddMinutes(-1),
                WaitingForInput = false
            }
        };

        var message = new PrintBundleStatusUpdate
        {
            PrintBundleId = bundleId,
            BatchName = bundleEntity.BatchName,
            WaitingForInput = true,
            IsInFailedState = false,
            Message = "Message",
            Status = "Status",
            Timestamp = DateTimeOffset.Now
        };

        var bundleEntityDetailsResponse = new CosmosResponse<PrintBundleDetailsEntity>(string.Empty, bundleEntity);

        mocker.GetMock<IPrintBundleDetailsRepository>()
            .Setup(x => x.GetSingleAsync(bundleEntity.Id, false))
            .ReturnsAsync(bundleEntityDetailsResponse);

        mocker.GetMock<IPrintBundleDetailsRepository>()
            .Setup(x => x.ReplaceConsistentAsync(bundleEntityDetailsResponse, It.IsAny<Func<PrintBundleDetailsEntity, bool>>()))
            .Returns(Task.CompletedTask);

        // Act
        var updatedEntity = await service.UpdateStatusAsync(message);

        // Assert
        updatedEntity.LastStatus.Should().NotBeNull();

        var statusEntry = updatedEntity.LastStatus;

        statusEntry.IsInFailedState.Should().Be(message.IsInFailedState);
        statusEntry.Message.Should().Be(message.Message);
        statusEntry.Status.Should().Be(message.Status);
        statusEntry.WaitingForInput.Should().Be(message.WaitingForInput);
        statusEntry.Timestamp.Should().Be(message.Timestamp);
    }
}