﻿using Pondres.Omnia.Print.Contracts.PrintBundle;
using Pondres.Omnia.Print.Storage.Entities.PrintBundleStatus;

namespace Pondres.Omnia.Print.Business.Extensions;

public static class PrintBundleSkippedExtensions 
{
    public static PrintBundleStatusHistoryEntity ToStatusEntity(this PrintBundleSkipped message) =>
        PrintBundleStatusHistoryEntity.Create(
            timestamp: message.Timestamp,
            bundleId: message.PrintBundleId.ToString(),
            details: message.ToStatusDetailsEntity());

    public static PrintBundleStatusDetailsEntity ToStatusDetailsEntity(this PrintBundleSkipped message) =>
        new()
        {
            Timestamp = message.Timestamp,
            Message = message.Message,
            Status = message.Status,
        };
}