﻿using Azure.Storage.Blobs;
using System.Text;

namespace Pondres.Omnia.Print.IntegrationTest.Services;

public class BlobFixture
{
    public readonly BlobServiceClient client;

    public BlobFixture(string connectionString)
    {
        client = new BlobServiceClient(connectionString);
    }

    public async Task<string> SaveContentToContainer(
        string containerName,
        string fileName,
        string content)
    {
        var containerClient = client.GetBlobContainerClient(containerName);

        await containerClient.CreateIfNotExistsAsync();

        var blobContents = new MemoryStream(Encoding.UTF8.GetBytes(content));

        var response = await containerClient.UploadBlobAsync(fileName, blobContents);

        return fileName;
    }
}