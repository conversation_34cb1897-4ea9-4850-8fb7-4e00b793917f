﻿using Pondres.Omnia.Print.Contracts.Api.ReleaseSchedule;
using Pondres.Omnia.Print.Contracts.Common;
using Pondres.Omnia.Print.Storage.Entities.Schedule;
using System.Collections.Generic;

namespace Pondres.Omnia.Print.Business.Extensions;

public static class ReleaseSchedulesExtensions
{
    public static ReleaseSchedules ToReleaseSchedulesDto(this ReleaseSchedulesEntity entity)
    {
        return new()
        {
            Id = entity.Id,
            Type = entity.Type,
            Schedule = entity.Schedule.ToReleasePrintSchedulesDto(),
            Active = entity.Active.ToString().ToLower()
        };
    }

    public static List<ReleasePrintSchedule> ToReleasePrintSchedulesDto(this List<PrintScheduleEntity> entities)
    {
        var printSchedules = new List<ReleasePrintSchedule>();
        foreach(var printSchedule in entities)
        {
            printSchedules.Add(new()
            {
                DayOfWeek = printSchedule.DayOfWeek,
                Releases = printSchedule.Releases.ToPrintScheduleTimesDto()
            });
        }

        return printSchedules;
    }

    public static List<PrintScheduleTime> ToPrintScheduleTimesDto(this List<PrintScheduleTimeEntity> entities)
    {
        var printScheduleTimes = new List<PrintScheduleTime>();
        foreach(var printScheduleTime in entities)
        {
            printScheduleTimes.Add(new()
            {
                Time = printScheduleTime.Time,
                Type = printScheduleTime.Type.ToEnumOrDefault(PrintBundleMode.EmptyGroup)
            });
        }
        return printScheduleTimes;
    }

    public static ReleaseSchedulesEntity ToReleaseSchedules(this ReleaseSchedules entity)
    {
        return new()
        {
            Id = entity.Id,
            Type = entity.Type,
            Schedule = entity.Schedule.ToReleasePrintSchedules(),
            Active = entity.Active == "true"
        };
    }

    public static List<PrintScheduleEntity> ToReleasePrintSchedules(this List<ReleasePrintSchedule> entities)
    {
        var printSchedules = new List<PrintScheduleEntity>();
        foreach (var printSchedule in entities)
        {
            printSchedules.Add(new()
            {
                DayOfWeek = printSchedule.DayOfWeek,
                Releases = printSchedule.Releases.ToPrintScheduleTimes()
            });
        }

        return printSchedules;
    }

    public static List<PrintScheduleTimeEntity> ToPrintScheduleTimes(this List<PrintScheduleTime> entities)
    {
        var printScheduleTimes = new List<PrintScheduleTimeEntity>();
        foreach (var printScheduleTime in entities)
        {
            printScheduleTimes.Add(new()
            {
                Time = printScheduleTime.Time,
                Type = printScheduleTime.Type.ToString()
            });
        }
        return printScheduleTimes;
    }
}
