﻿using FluentAssertions;
using MassTransit;
using Moq;
using Pondres.Omnia.Print.Business.Handler;
using Pondres.Omnia.Print.Business.Service;
using Pondres.Omnia.Print.Business.StateMachine.PrintBundle;
using Pondres.Omnia.Print.Business.StateMachine.PrintBundle.Model;
using Pondres.Omnia.Print.Business.UnitTests.Extensions;
using Pondres.Omnia.Print.Business.UnitTests.Helper;
using Pondres.Omnia.Print.Contracts.Common;
using Pondres.Omnia.Print.Contracts.PrintBundle;
using Pondres.Omnia.Print.Contracts.PrintDocument;
using Pondres.Omnia.Print.Storage.PrintBundle;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;

namespace Pondres.Omnia.Print.Business.UnitTests.StateMachine;

public class PrintBundleStateMachineTests : StateMachineTestBase<PrintBundleStateMachine, PrintBundleState>
{
    public static readonly Random random = new();

    [Fact]
    public async Task Any_PrintBundleCancelRequestReceived_WithoutReprint_CancelsOnly()
    {
        // Arrange
        var startingState = GenerateStartingState(nameof(PrintBundleStateMachine.Impositioning));

        var message = new PrintBundleCancelRequest
        {
            PrintBundleId = startingState.CorrelationId,
            Reprint = false,
            Force = true,
            Username = "username"
        };

        // Act
        await RunHarnessTest(
            message,
            startingState,
            async (sagaHarness, testHarness) =>
            {
                // Assert
                (await testHarness.Published.Any<PrintBundleCancelled>()).Should().BeTrue();
                (await testHarness.Published.Any<PrintBundleManualEvent>()).Should().BeTrue();
                (await testHarness.Published.Any<PrintBundleReprintRequested>()).Should().BeFalse();

                var state = sagaHarness.Sagas.Select(x => x.CorrelationId == message.PrintBundleId).Single();
                state.Should().NotBeNull();

                mocker.VerifyAll();
            });
    }

    [Fact]
    public async Task Any_PrintBundleCancelRequestReceived_WithReprint_CancelsAndReprints()
    {
        // Arrange
        var startingState = GenerateStartingState(nameof(PrintBundleStateMachine.Impositioning));

        var message = new PrintBundleCancelRequest
        {
            PrintBundleId = startingState.CorrelationId,
            Reprint = true,
            Force = true,
            Username = "username"
        };

        // Act
        await RunHarnessTest(
            message,
            startingState,
            async (sagaHarness, testHarness) =>
            {
                // Assert
                (await testHarness.Published.Any<PrintBundleCancelled>()).Should().BeTrue();
                (await testHarness.Published.Any<PrintBundleManualEvent>()).Should().BeTrue();
                (await testHarness.Published.Any<PrintBundleReprintRequested>()).Should().BeTrue();

                var state = sagaHarness.Sagas.Select(x => x.CorrelationId == message.PrintBundleId).Single();
                state.Should().NotBeNull();

                mocker.VerifyAll();
            });
    }

    [Fact]
    public async Task Impositioning_ImpositioningCompleted_TransitionsTo_WaitingForPrint()
    {
        // Arrange
        var documents = GeneratePrintDocuments(2);
        var startingState = GenerateStartingState(nameof(PrintBundleStateMachine.Impositioning));

        var message = new PrintBundleImpositioningCompleted
        {
            Customer = "Customer",
            PrintBundleId = startingState.CorrelationId,
            ResultFileNames = ["FileName1", "FileName2"]
        };

        mocker.GetMock<IPrintBundleDocumentStorageService>()
            .Setup(x => x.GetDocumentsForBundleAsync(startingState.CorrelationId))
            .ReturnsAsync(new HashSet<PrintBundleQueuedDocument>(documents));

        // Act
        await RunHarnessTest(
            message,
            startingState,
            async (sagaHarness, testHarness) =>
            {
                // Assert
                (await testHarness.Published.Any<PrintDocumentSentToPrint>()).Should().BeTrue();
                (await testHarness.Published.Any<PrintDocumentPrintConfirmed>()).Should().BeTrue();

                var state = sagaHarness.Sagas.Select(x => x.CorrelationId == message.PrintBundleId).Single();
                state.Should().NotBeNull();

                state.Saga.CurrentState.Should().Be(nameof(PrintBundleStateMachine.WaitingForPrint));

                mocker.VerifyAll();
            });
    }

    [Fact]
    public async Task Impositioning_ImpositioningFailed_TransitionsTo_ImpositioningOnHold()
    {
        // Arrange
        var startingState = GenerateStartingState(nameof(PrintBundleStateMachine.Impositioning));

        var message = new PrintBundleImpositioningFailed
        {
            Customer = "Customer",
            PrintBundleId = startingState.CorrelationId,
            Message = "Message"
        };

        // Act
        await RunHarnessTest(
            message,
            startingState,
            async (sagaHarness, testHarness) =>
            {
                // Assert
                (await testHarness.Published.Any<PrintBundleSetOnHold>()).Should().BeTrue();

                var state = sagaHarness.Sagas.Select(x => x.CorrelationId == message.PrintBundleId).Single();
                state.Should().NotBeNull();

                state.Saga.CurrentState.Should().Be(nameof(PrintBundleStateMachine.ImpositioningOnHold));

                mocker.VerifyAll();
            });
    }

    [Fact]
    public async Task Impositioning_ManualContinueRequestReceived_WithForce_RetriesImpositioning()
    {
        // Arrange
        var startingState = GenerateStartingState(nameof(PrintBundleStateMachine.Impositioning));

        var message = new PrintBundleManualContinueRequest
        {
            PrintBundleId = startingState.CorrelationId,
            Force = true,
            Username = "username"
        };

        mocker.GetMock<IPrintBundleImpositioningHandler>()
            .Setup(x => x.SendBundleToImpositioningAsync(
                It.IsAny<ISendEndpoint>(),
                It.IsAny<string>(),
                It.IsAny<Guid>(),
                It.IsAny<string>(),
                It.IsAny<int>()))
            .Returns(Task.CompletedTask);

        // Act
        await RunHarnessTest(
            message,
            startingState,
            async (sagaHarness, testHarness) =>
            {
                // Assert
                (await testHarness.Published.Any<PrintBundleManualEvent>()).Should().BeTrue();
                (await testHarness.Published.Any<PrintBundleManualContinueResponse>()).Should().BeTrue();

                var state = sagaHarness.Sagas.Select(x => x.CorrelationId == message.PrintBundleId).Single();
                state.Should().NotBeNull();

                state.Saga.CurrentState.Should().Be(nameof(PrintBundleStateMachine.Impositioning));

                mocker.VerifyAll();
            });
    }

    [Fact]
    public async Task Impositioning_ManualContinueRequestReceived_WithoutForce_DoesNothing()
    {
        // Arrange
        var startingState = GenerateStartingState(nameof(PrintBundleStateMachine.Impositioning));

        var message = new PrintBundleManualContinueRequest
        {
            PrintBundleId = startingState.CorrelationId,
            Force = false,
            Username = "username"
        };

        // Act
        await RunHarnessTest(
            message,
            startingState,
            async (sagaHarness, testHarness) =>
            {
                // Assert
                (await testHarness.Published.Any(x => x.MessageType != typeof(PrintBundleManualContinueRequest))).Should().BeFalse();

                var state = sagaHarness.Sagas.Select(x => x.CorrelationId == message.PrintBundleId).Single();
                state.Should().NotBeNull();

                state.Saga.CurrentState.Should().Be(nameof(PrintBundleStateMachine.Impositioning));

                mocker.VerifyAll();
            });
    }

    [Fact]
    public async Task ImpositioningOnHold_ManualContinueRequestReceived_RestartsImpositioning()
    {
        // Arrange
        var startingState = GenerateStartingState(nameof(PrintBundleStateMachine.ImpositioningOnHold));

        var message = new PrintBundleManualContinueRequest
        {
            PrintBundleId = startingState.CorrelationId,
            Force = false,
            Username = "username"
        };

        mocker.GetMock<IPrintBundleImpositioningHandler>()
            .Setup(x => x.SendBundleToImpositioningAsync(
                It.IsAny<ISendEndpoint>(),
                It.IsAny<string>(),
                It.IsAny<Guid>(),
                It.IsAny<string>(),
                It.IsAny<int>()))
            .Returns(Task.CompletedTask);

        // Act
        await RunHarnessTest(
            message,
            startingState,
            async (sagaHarness, testHarness) =>
            {
                // Assert
                (await testHarness.Published.Any<PrintBundleManualEvent>()).Should().BeTrue();
                (await testHarness.Published.Any<PrintBundleManualContinueResponse>()).Should().BeTrue();

                var state = sagaHarness.Sagas.Select(x => x.CorrelationId == message.PrintBundleId).Single();
                state.Should().NotBeNull();

                state.Saga.CurrentState.Should().Be(nameof(PrintBundleStateMachine.Impositioning));

                mocker.VerifyAll();
            });
    }

    [Fact]
    public async Task ImpositioningOnHold_RemoveDocument_TransitionsTo_Impositioning()
    {
        // Arrange
        var documents = GeneratePrintDocuments(2);
        var startingState = GenerateStartingState(nameof(PrintBundleStateMachine.ImpositioningOnHold), documents);

        var message = new PrintBundleRemoveDocumentRequest(
            PrintBundleId: startingState.CorrelationId,
            DocumentId: documents[0].DocumentMetadata.DocumentId);

        var actualDocument = TestPrintBundleHelper.CreateQueuedDocument();

        var documentsFromCache = new PrintBundleDocumentCollection
        {
            Documents = [actualDocument]
        };

        mocker.GetMock<IPrintBundleDocumentStorageService>()
            .Setup(x => x.RemoveDocumentFromBundleAsync(startingState.CorrelationId, message.DocumentId, startingState.IsPrimary))
            .ReturnsAsync(new Model.DocumentRemovalResult(
                Removed: true,
                RemovedDocument: actualDocument,
                UpdatedDocumentSet: documentsFromCache.Documents
            ));

        mocker.GetMock<IPrintBundleImpositioningHandler>()
            .Setup(x => x.SendBundleToImpositioningAsync(
                It.IsAny<ISendEndpoint>(),
                It.IsAny<string>(),
                It.IsAny<Guid>(),
                It.IsAny<string>(),
                It.IsAny<int>()))
            .Returns(Task.CompletedTask);

        // Act
        await RunHarnessTest(
            message,
            startingState,
            async (sagaHarness, testHarness) =>
            {
                // Assert
                (await testHarness.Published.Any<PrintBundleDocumentRemoved>()).Should().BeTrue();

                var state = sagaHarness.Sagas.Select(x => x.CorrelationId == message.PrintBundleId).Single();
                state.Should().NotBeNull();

                state.Saga.CurrentState.Should().Be(nameof(PrintBundleStateMachine.Impositioning));

                var documentRemoved = testHarness.Published.Select<PrintBundleDocumentRemovedResponse>().Single();
                documentRemoved.Context.Message.RemovedDocumentId.Should().Be(message.DocumentId);

                mocker.VerifyAll();
            });
    }

    [Fact]
    public async Task WaitingForBatch_AssignBatchFailed_TransitionsToCreationFailure()
    {
        // Arrange
        var startingState = GenerateStartingState(nameof(PrintBundleStateMachine.WaitingForBatch));

        var message = new PrintBundleAssignTaskFailure
        {
            PrintBundleId = startingState.CorrelationId,
            Message = "Message"
        };

        // Act
        await RunHarnessTest(
            message,
            startingState,
            async (sagaHarness, testHarness) =>
            {
                // Assert
                (await testHarness.Consumed.Any<PrintBundleAssignTaskFailure>()).Should().BeTrue();
                (await sagaHarness.Consumed.Any<PrintBundleAssignTaskFailure>()).Should().BeTrue();

                var state = sagaHarness.Sagas.Select(x => x.CorrelationId == message.PrintBundleId).Single();
                state.Should().NotBeNull();

                state.Saga.CurrentState.Should().Be(nameof(PrintBundleStateMachine.BatchCreationFailure));

                mocker.VerifyAll();
            });
    }

    [Fact]
    public async Task WaitingForBatch_AssignedTaskAndGordNumber_AddTaskAndGordNumber()
    {
        // Arrange
        var documents = GeneratePrintDocuments(2);
        var startingState = GenerateStartingState(
            nameof(PrintBundleStateMachine.WaitingForBatch),
            documents);

        var firstDocumentId = documents[0].DocumentMetadata.DocumentId;
        var secondDocumentId = documents[1].DocumentMetadata.DocumentId;

        var message = new PrintBundleAssignTaskCompleted
        {
            PrintBundleId = startingState.CorrelationId,
            GordNumber = "GordNumber",
            TaskNumber = 2
        };

        mocker.GetMock<IPrintBundleImpositioningHandler>()
            .Setup(x => x.SendBundleToImpositioningAsync(
                It.IsAny<ISendEndpoint>(),
                It.IsAny<string>(),
                It.IsAny<Guid>(),
                It.IsAny<string>(),
                It.IsAny<int>()))
            .Returns(Task.CompletedTask);

        mocker.GetMock<IPrintBundleService>()
            .Setup(x => x.GetBundleDetailsAsync(It.IsAny<Guid>()))
            .ReturnsAsync(new Contracts.Api.Bundle.PrintBundleDetails());

        var documentCollection = new PrintBundleDocumentCollection() { Documents = new PrintBundleQueuedDocumentHashSet(documents) };

        mocker.GetMock<IPrintBundleDocumentStorageService>()
            .Setup(x => x.UpdateDocumentCollectionAsync(startingState.CorrelationId, It.IsAny<Action<PrintBundleDocumentCollection>>()))
            .Callback((Guid bundleId, Action<PrintBundleDocumentCollection> action) =>
            {
                action(documentCollection);
            })
            .Returns(Task.CompletedTask);

        // Act
        await RunHarnessTest(
            message,
            startingState,
            async (sagaHarness, testHarness) =>
            {
                // Assert
                (await testHarness.Consumed.Any<PrintBundleAssignTaskCompleted>()).Should().BeTrue();
                (await sagaHarness.Consumed.Any<PrintBundleAssignTaskCompleted>()).Should().BeTrue();

                (await testHarness.Published.Any<PrintDocumentAssignSequenceId>()).Should().BeTrue();

                var assignMessages = testHarness.Published.Select<PrintDocumentAssignSequenceId>();
                assignMessages.Should().HaveCount(2);

                var firstDocumentAssignment = assignMessages.Single(x => x.Context.Message.DocumentId == firstDocumentId).Context.Message;
                firstDocumentAssignment.SequenceId.Should().Be(1);
                firstDocumentAssignment.TaskNumber.Should().Be(message.TaskNumber);
                firstDocumentAssignment.GordNumber.Should().Be(message.GordNumber);
                firstDocumentAssignment.PrintBundleId.Should().Be(message.PrintBundleId);

                var secondDocumentAssignment = assignMessages.Single(x => x.Context.Message.DocumentId == secondDocumentId).Context.Message;
                secondDocumentAssignment.SequenceId.Should().Be(2);
                secondDocumentAssignment.TaskNumber.Should().Be(message.TaskNumber);
                secondDocumentAssignment.GordNumber.Should().Be(message.GordNumber);
                secondDocumentAssignment.PrintBundleId.Should().Be(message.PrintBundleId);

                var state = sagaHarness.Sagas.Select(x => x.CorrelationId == message.PrintBundleId).Single();
                state.Should().NotBeNull();

                state.Saga.CurrentState.Should().Be(nameof(PrintBundleStateMachine.Impositioning));

                state.Saga.GordNumber.Should().Be(message.GordNumber);
                state.Saga.TaskNumber.Should().Be(message.TaskNumber);

                documents[0].SequenceId.Should().Be(1);
                documents[1].SequenceId.Should().Be(2);

                mocker.VerifyAll();
            });
    }

    [Fact]
    public async Task Scanned_PrintBundleCompletedConfirmationRequestReceived_TransitionsTo_Printed_And_Finalizes_Without_EndLocation()
    {
        // Arrange
        var documents = GeneratePrintDocuments(1);
        var startingState = GenerateStartingState(nameof(PrintBundleStateMachine.Scanned), documents);

        var message = new PrintBundleCompletedConfirmationRequest
        {
            PrintBundleId = startingState.CorrelationId,
            Username = "username"
        };

        mocker.GetMock<IPrintBundleDocumentStorageService>()
            .Setup(x => x.GetDocumentsForBundleAsync(startingState.CorrelationId))
            .ReturnsAsync(new HashSet<PrintBundleQueuedDocument>(documents));

        mocker.GetMock<IPrintBundleDocumentStorageService>()
            .Setup(x => x.ExpireDocumentsForBundle(startingState.CorrelationId))
            .Verifiable();

        // Act
        await RunHarnessTest(
            message,
            startingState,
            async (sagaHarness, testHarness) =>
            {
                // Assert
                (await testHarness.Published.Any<PrintBundleCompletedConfirmationResponse>()).Should().BeTrue();
                (await testHarness.Published.Any<PrintBundleManualEvent>()).Should().BeTrue();
                (await testHarness.Published.Any<PrintBundleCompleted>()).Should().BeTrue();

                var state = sagaHarness.Sagas.Select(x => x.CorrelationId == message.PrintBundleId).Single();
                state.Should().NotBeNull();

                state.Saga.CurrentState.Should().Be(nameof(PrintBundleStateMachine.Final));

                mocker.VerifyAll();
            });
    }

    [Fact]
    public async Task WaitingForScan_PrintBundleScannedConfirmationRequestReceived_TransitionsTo_Scanned()
    {
        // Arrange
        var documents = GeneratePrintDocuments(1, customer: TestPrintBundleHelper.DirectCompleteDisabledCustomer);
        var startingState = GenerateStartingState(nameof(PrintBundleStateMachine.WaitingForScan), documents);

        var message = new PrintBundleScannedConfirmationRequest
        {
            PrintBundleId = startingState.CorrelationId,
            Username = "username"
        };

        // Act
        await RunHarnessTest(
            message,
            startingState,
            (sagaHarness, testHarness) =>
            {
                // Assert
                var state = sagaHarness.Sagas.Select(x => x.CorrelationId == message.PrintBundleId).Single();
                state.Should().NotBeNull();

                state.Saga.CurrentState.Should().Be(nameof(PrintBundleStateMachine.Scanned));

                mocker.VerifyAll();

                return Task.CompletedTask;
            });
    }

    [Fact]
    public async Task WaitingForPrint_PrintBundlePrintConfirmationRequestReceived_TransitionsTo_Completed_And_Finalize_With_EndLocation()
    {
        // Arrange
        var documents = GeneratePrintDocuments(1);
        var startingState = GenerateStartingState(nameof(PrintBundleStateMachine.WaitingForPrint), documents);

        var message = new PrintBundlePrintConfirmationRequest
        {
            PrintBundleId = startingState.CorrelationId,
            Username = "username"
        };

        mocker.GetMock<IPrintBundleDocumentStorageService>()
            .Setup(x => x.GetDocumentsForBundleAsync(startingState.CorrelationId))
            .ReturnsAsync(new HashSet<PrintBundleQueuedDocument>(documents));

        mocker.GetMock<IPrintBundleDocumentStorageService>()
            .Setup(x => x.ExpireDocumentsForBundle(startingState.CorrelationId))
            .Verifiable();

        // Act
        await RunHarnessTest(
            message,
            startingState,
            async (sagaHarness, testHarness) =>
            {
                // Assert
                (await testHarness.Published.Any<PrintBundlePrintConfirmationResponse>()).Should().BeTrue();
                (await testHarness.Published.Any<PrintBundleManualEvent>()).Should().BeTrue();
                (await testHarness.Published.Any<PrintDocumentPrinted>()).Should().BeTrue();
                (await testHarness.Published.Any<PrintBundleCompleted>()).Should().BeTrue();

                var state = sagaHarness.Sagas.Select(x => x.CorrelationId == message.PrintBundleId).Single();
                state.Should().NotBeNull();

                state.Saga.CurrentState.Should().Be(nameof(PrintBundleStateMachine.Final));

                mocker.VerifyAll();
            });
    }

    private static List<PrintBundleQueuedDocument> GeneratePrintDocuments(int count, string? customer = null)
    {
        var documents = new List<PrintBundleQueuedDocument>(count);
        for (var i = 0; i < count; i++)
        {
            documents.Add(TestPrintBundleHelper.CreateQueuedDocument(customer: customer));
        }

        return documents;
    }

    private static PrintBundleState GenerateStartingState(
        string initialState,
        List<PrintBundleQueuedDocument>? documents = null)
    {
        documents ??= GeneratePrintDocuments(1);

        var bundleId = Guid.NewGuid();

        var state = new PrintBundleState
        {
            CorrelationId = bundleId,
            BatchName = RandomValue("BatchName"),
            BundleOptions = new PrintBundleOptions
            {
                BundleMode = PrintBundleMode.EmptyGroup
            },
            CreatedOn = DateTimeOffset.Now,
            CurrentState = initialState,
            Customer = RandomValue("Customer"),
            InitiatedBy = RandomValue("InitiatedBy"),
            DocumentsTotalPageCount = documents.Sum(x => x.DocumentMetadata.PageCount * x.DocumentMetadata.Quantity),
            DocumentsTotalQuantity = documents.Sum(x => x.DocumentMetadata.Quantity),
            DocumentsTotalRecordCount = documents.Count,
            EndLocation = "gifts",
            IsPrimary = true,
            PrimaryBundleId = bundleId,
            IsDirectlyCompleted = true
        };
        state.AddDocumentStatistics(documents);

        return state;
    }

    private static string RandomValue(string prefix) => $"{prefix}_{Guid.NewGuid()}";
}