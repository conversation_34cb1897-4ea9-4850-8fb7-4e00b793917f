﻿using System;

namespace Pondres.Omnia.Print.Contracts.PrintBundle
{
    public class PrintBundleDelayed
    {
        public DateTimeOffset CreatedOn { get; set; }

        public string CurrentState { get; set; } = string.Empty;
        public string Customer { get; set; } = string.Empty;
        public Guid PrintBundleId { get; set; }

        public DateTimeOffset Timestamp { get; set; }
        public string BatchName { get; set; } = string.Empty;
    }
}