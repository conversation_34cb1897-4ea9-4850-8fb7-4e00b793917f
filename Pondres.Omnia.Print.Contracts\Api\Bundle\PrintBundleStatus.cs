﻿using Pondres.Omnia.Print.Contracts.Common;
using System;

namespace Pondres.Omnia.Print.Contracts.Api.Bundle
{
    public class PrintBundleStatus
    {
        public bool IsInFailedState { get; set; }
        public string Message { get; set; } = string.Empty;
        public MapPrintBundleState Status { get; set; }
        public DateTimeOffset Timestamp { get; set; }
        public bool WaitingForInput { get; set; }
    }
}