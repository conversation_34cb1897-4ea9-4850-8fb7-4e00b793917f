﻿using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.Caching.Memory;
using Solude.StorageBase;
using Pondres.Omnia.Print.Storage.Configuration;
using Pondres.Omnia.Print.Storage.Entities.Print;
using System;

namespace Pondres.Omnia.Print.Storage.Repositories;

public class PrintConfigurationRepository : SinglePartitionBaseRepository<PrintConfigurationEntity>, IPrintConfigurationRepository
{
    protected override string PartitionKeyValue => CosmosConfiguration.ConfigurationPartitionValue;

    protected override bool CanWrite => true;

    public PrintConfigurationRepository(CosmosClient cosmosClient, IMemoryCache memoryCache)
        : base(cosmosClient, CosmosConfiguration.PrintDatabaseName, CosmosConfiguration.ConfigurationContainerName, memoryCache)
    { }

    protected override MemoryCacheEntryOptions CreateCacheOptions() => new MemoryCacheEntryOptions
    {
        SlidingExpiration = TimeSpan.FromMinutes(1)
    };

    protected override string Get<PERSON><PERSON>Key(string id, string partitionKeyValue) => $"PrintConfigurationEntity_{id}_{partitionKeyValue}";
}
