﻿using Pondres.Omnia.Print.Business.Exceptions;
using Pondres.Omnia.Print.Contracts.PrintBundle;
using Pondres.Omnia.Print.Integrations.Nies.Extensions;
using Pondres.Omnia.Print.Integrations.Nies.Model;
using Pondres.Omnia.Print.Integrations.Nies.Service;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Integrations.Nies.Handler;

public class PrintBundleExportHandler: IPrintBundleExportHandler
{
    private readonly INiesExportService exportService;
    private readonly IMappingService mappingService;

    private static readonly SemaphoreSlim sendSemaphore = new(1,1);

    public PrintBundleExportHandler(IMappingService mappingService, INiesExportService exportService)
    {
        this.mappingService = mappingService; 
        this.exportService = exportService;
    }

    public async Task HandlePrintBundleCompletedRequestAsync(IEnumerable<PrintBundleCompleted> printBundlesCompleted)
    {
        var mappingEntities = await mappingService.GetMappingsAsync(printBundlesCompleted.ToCustomerList());

        if (printBundlesCompleted.ToCustomersWithoutMappingList(mappingEntities).Any())
            throw new NoDivisionMappingFoundException($"No division mapping found for customers [{string.Join(" - ", printBundlesCompleted.ToCustomersWithoutMappingList(mappingEntities).ToArray())}]");

        var ordersToSend = printBundlesCompleted.ToOrdersToSend(mappingEntities);

        foreach (var orderToSend in ordersToSend)
        {
            await ProcessOrderInOneLaneAsync(orderToSend);
        }
    }

    private async Task ProcessOrderInOneLaneAsync(OrderToSendResult orderToSend)
    {
        await sendSemaphore.WaitAsync();
        try
        {
            await exportService.SendAsync(orderToSend);
        }
        catch(Exception exception)
        {
            throw new SendOrderToNiesException($"Error sending order(s) to  Nies [{string.Join(',',orderToSend.Orders.Select(o => o.ordernr).ToArray())}]", exception);
        }
        finally
        {
            sendSemaphore.Release();
        }
    }
}
