﻿using Pondres.Omnia.Print.Contracts.Common;
using System.Collections.Generic;

namespace Pondres.Omnia.Print.Contracts.Api.Bundle
{
    public class PrintBundleBatchFilter
    {
        public string? ContinuationToken { get; set; }
        public int MaxPageSize { get; set; }

        public List<string> PrintBundleIds { get; set; } = new();

        public List<string> BatchNames { get; set; } = new();

        public PrintBundleStateType? State { get; set; }
    }
}