﻿using MassTransit;
using Pondres.Omnia.Print.Business.Extensions;
using Pondres.Omnia.Print.Business.Service;
using Pondres.Omnia.Print.Business.StateMachine.PrintBundle.Model;
using Pondres.Omnia.Print.Contracts.PrintBundle;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Activities.Bundle;

public class PrintBundleRemovePrintDocumentActivity : BaseActivity<PrintBundleState, PrintBundleRemoveDocumentRequest>
{
    private readonly IPrintBundleDocumentStorageService bundleDocumentStorageService;

    public PrintBundleRemovePrintDocumentActivity(IPrintBundleDocumentStorageService bundleDocumentStorageService)
    {
        this.bundleDocumentStorageService = bundleDocumentStorageService;
    }

    public override string ActivityName => "remove-print-bundle-activity";

    protected override async Task ExecuteAsync(BehaviorContext<PrintBundleState, PrintBundleRemoveDocumentRequest> context)
    {
        var removeResult = await bundleDocumentStorageService.RemoveDocumentFromBundleAsync(
            bundleId: context.Saga.CorrelationId,
            documentId: context.Message.DocumentId,
            isPrimaryBundle: context.Saga.IsPrimary);

        if (removeResult.Removed)
        {
            context.Saga.RecalculateTotals(removeResult.UpdatedDocumentSet);

            context.Respond(context.Message.ToPrintBundleDocumentRemovedResponse());

            await context.Publish(context.Message.ToPrintBundleDocumentRemoved());
        }
        else
            context.Respond(context.Message.ToPrintBundleDocumentNotFoundResponse());
    }
}