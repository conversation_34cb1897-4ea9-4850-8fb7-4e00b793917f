﻿using MassTransit;
using Pondres.Omnia.Print.Business.Handler;
using Pondres.Omnia.Print.Contracts.PrintBundle;
using System;
using System.Collections.Concurrent;
using System.Threading;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Consumers;

public class PrintBundleCreationRequestConsumer(
    IPrintBundleCreationHandler printBundleCreationHandler) : IConsumer<PrintBundleCreationRequest>
{
    private static readonly ConcurrentDictionary<string, (SemaphoreSlim Semaphore, int RefCount)> semaphores = new();
    private static readonly Lock cleanupLock = new();

    public async Task Consume(ConsumeContext<PrintBundleCreationRequest> context)
    {
        var semaphore = await WaitForBatchSemaphoreAsync(context.Message.BundleHash);

        try
        {
            var bundleId = await printBundleCreationHandler.CreatePrintBundleFromRequestAsync(context, context.Message);

            var response = new PrintBundleCreationResponse
            {
                Id = bundleId
            };

            await context.RespondAsync(response);
        }
        finally
        {
            ReleaseBatchSemaphore(semaphore, context.Message.BundleHash);
        }
    }

    private static async Task<SemaphoreSlim> WaitForBatchSemaphoreAsync(string bundleHash)
    {
        var semaphore = semaphores.AddOrUpdate(
            key: bundleHash,
            addValueFactory: _ => (new SemaphoreSlim(1, 1), 1), 
            updateValueFactory: (_, existing) => (existing.Semaphore, existing.RefCount + 1)).Semaphore;

        await semaphore.WaitAsync();

        return semaphore;
    }

    private static void ReleaseBatchSemaphore(SemaphoreSlim semaphore, string bundleHash)
    {
        semaphore.Release();

        lock (cleanupLock)
        {
            if (semaphores.TryGetValue(bundleHash, out var existing))
            {
                var newRefCount = existing.RefCount - 1;
                if (newRefCount == 0)
                {
                    // No more references, remove and dispose the semaphore
                    semaphores.TryRemove(bundleHash, out _);
                    existing.Semaphore.Dispose();
                }
                else
                {
                    // Still has references, update the count
                    semaphores[bundleHash] = (existing.Semaphore, newRefCount);
                }
            }
        }
    }
}
