﻿using Pondres.Omnia.Print.Contracts.Api.Bundle;
using Pondres.Omnia.Print.Contracts.Common;
using Pondres.Omnia.Print.Storage.Entities.PrintBundleStatus;
using System.Collections.Generic;
using System.Linq;

namespace Pondres.Omnia.Print.Business.Extensions;

public static class PrintBundleStatusDetailsEntityExtensions
{
    public static PrintBundleStatus ToDto(this PrintBundleStatusDetailsEntity entity)
    {
        return new PrintBundleStatus
        {
            Status = entity.Status.ToEnumOrDefault(MapPrintBundleState.None),
            Message = entity.Message,
            Timestamp = entity.Timestamp,
            IsInFailedState = entity.IsInFailedState,
            WaitingForInput = entity.WaitingForInput,
        };
    }

    public static IEnumerable<PrintBundleStatus> ToDto(this IEnumerable<PrintBundleStatusDetailsEntity> entities) =>
        entities.Select(x => x.ToDto());

    public static bool CanBeContinued(this PrintBundleStatusDetailsEntity entity)
        => entity.Status.IsOneOfEnumValues( 
            MapPrintBundleState.BatchCreationFailure, 
            MapPrintBundleState.ImpositioningOnHold, 
            MapPrintBundleState.Impositioning);

    public static bool CanBeCancelled(this PrintBundleStatusDetailsEntity entity)
        => entity.Status.IsOneOfEnumValues(MapPrintBundleState.BatchCreationFailure);

    public static bool CanRemoveDocument(this PrintBundleStatusDetailsEntity entity)
        => entity.Status.IsOneOfEnumValues(
            MapPrintBundleState.ImpositioningOnHold,
            MapPrintBundleState.WaitingForStart);

    public static bool CanConfirmPrint(this PrintBundleStatusDetailsEntity entity)
        => entity.Status.IsEnumValue(MapPrintBundleState.WaitingForPrint);
}