﻿using MassTransit;
using Pondres.Omnia.Print.Business.Extensions;
using Pondres.Omnia.Print.Business.Service;
using Pondres.Omnia.Print.Business.StateMachine.PrintBundle;
using Pondres.Omnia.Print.Business.StateMachine.PrintBundle.Model;
using Pondres.Omnia.Print.Contracts.PrintBundle;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Activities.Bundle;

public class PrintBundlePublishDocumentStateEventsActivity : BaseInstanceActivity<PrintBundleState>
{
    private readonly IPrintBundleDocumentStorageService documentStorageService;

    public PrintBundlePublishDocumentStateEventsActivity(IPrintBundleDocumentStorageService documentStorageService)
    {
        this.documentStorageService = documentStorageService;
    }

    public override string ActivityName => "publish-document-state-events-activity";

    protected override async Task ExecuteAsync(BehaviorContext<PrintBundleState> context)
    {
        var documents = await documentStorageService.GetDocumentsForBundleAsync(context.Saga.CorrelationId);

        switch (context.Saga.CurrentState)
        {
            case nameof(PrintBundleStateMachine.WaitingForPrint):
                await PublishPrintDocumentsSentToPrintAsync(context, documents);
                await PublishPrintDocumentsPrintConfirmedMessagesAsync(context, documents);
                break;

            case nameof(PrintBundleStateMachine.Printed):
                await PublishPrintDocumentPrintedMessagesAsync(context, documents);
                break;

            case nameof(PrintBundleStateMachine.Completed):
                await PublishPrintDocumentCompletedMessagesAsync(context, documents);
                break;

            default:
                break;
        }
    }

    private static async Task PublishPrintDocumentCompletedMessagesAsync(BehaviorContext<PrintBundleState> context, HashSet<PrintBundleQueuedDocument> documents)
    {
        var publishTasks = documents.Select(document => context.Publish(document.ToPrintDocumentCompletedConfirmed())).ToList();
        await Task.WhenAll(publishTasks);
    }

    private static async Task PublishPrintDocumentPrintedMessagesAsync(BehaviorContext<PrintBundleState> context, HashSet<PrintBundleQueuedDocument> documents)
    {
        var publishTasks = documents.Select(document => context.Publish(document.ToPrintDocumentPrinted())).ToList();
        await Task.WhenAll(publishTasks);
    }

    private static async Task PublishPrintDocumentsPrintConfirmedMessagesAsync(BehaviorContext<PrintBundleState> context, HashSet<PrintBundleQueuedDocument> documents)
    {
        var publishTasks = documents.Select(document => context.Publish(document.ToPrintDocumentPrintConfirmed())).ToList();
        await Task.WhenAll(publishTasks);
    }

    private static async Task PublishPrintDocumentsSentToPrintAsync(BehaviorContext<PrintBundleState> context, HashSet<PrintBundleQueuedDocument> documents)
    {
        var publishTasks = documents.Select(document => context.Publish(document.ToPrintDocumentSentToPrint())).ToList();
        await Task.WhenAll(publishTasks);
    }
}