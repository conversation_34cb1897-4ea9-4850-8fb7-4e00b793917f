﻿using Pondres.Omnia.Print.Contracts.Api.Bundle;
using Pondres.Omnia.Print.Contracts.PrintBundle;
using System;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Handler;

public interface IPrintBundleActionHandler
{
    Task<PrintBundleCancelResponse> CancelBundleAsync(PrintBundleCancelCommand command);
    Task<PrintBundleManualContinueResponse> ContinueBundleAsync(PrintBundleContinueCommand command);
    Task<PrintBundlePrintConfirmationResponse> PrintConfirmBundleAsync(PrintBundlePrintConfirmCommand command);
    Task<PrintBundleCompletedConfirmationResponse> CompletedConfirmBundleAsync(PrintBundleCompletedConfirmCommand command);
    Task<PrintBundleScannedConfirmationResponse> ScannedConfirmBundleAsync(PrintBundleScannedConfirmCommand command);
    Task<PrintBundleStatusResponse> RawStatusAsync(Guid printBundleId);
    Task<PrintBundleDocumentRemovedResponse> RemoveDocumentFromBundleAsync(PrintBundleRemoveDocumentCommand command);
    Task<PrintBundleReleaseResponse> ReleaseBundleAsync(PrintBundleReleaseRequest request);
}