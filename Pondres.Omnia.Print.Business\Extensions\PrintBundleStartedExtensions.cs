﻿using Pondres.Omnia.Print.Contracts.PrintBundle;
using Pondres.Omnia.Print.Storage.Entities.PrintBundleStatus;

namespace Pondres.Omnia.Print.Business.Extensions;

public static class PrintBundleStartedExtensions
{
    public static PrintBundleStatusDetailsEntity ToStatusDetailsEntity(this PrintBundleStarted message) =>
        new()
        {
            Timestamp = message.Timestamp,
            Message = message.Message,
            Status = message.Status,
        };
}