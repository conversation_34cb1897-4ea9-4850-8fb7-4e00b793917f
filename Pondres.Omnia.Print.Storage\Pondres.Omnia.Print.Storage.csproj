﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Azure.Identity" Version="1.14.0" />
		<PackageReference Include="Azure.Storage.Blobs" Version="12.24.0" />
		<PackageReference Include="Microsoft.Extensions.Caching.Abstractions" Version="9.0.5" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.5" />
		<PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.5" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="Solude.StorageBase" Version="1.20250610.1" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Pondres.Omnia.Print.Common\Pondres.Omnia.Print.Common.csproj" />
		<ProjectReference Include="..\Pondres.Omnia.Print.Contracts\Pondres.Omnia.Print.Contracts.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Folder Include="Handler\" />
	</ItemGroup>

</Project>
