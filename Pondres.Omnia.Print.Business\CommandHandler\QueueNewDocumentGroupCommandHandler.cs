﻿using MassTransit;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Pondres.Omnia.Print.Business.Constants;
using Pondres.Omnia.Print.Business.Exceptions;
using Pondres.Omnia.Print.Business.Extensions;
using Pondres.Omnia.Print.Business.Handler;
using Pondres.Omnia.Print.Business.Handler.PrintBundleBuffer;
using Pondres.Omnia.Print.Business.Model;
using Pondres.Omnia.Print.Business.Service;
using Pondres.Omnia.Print.Business.Storage;
using Pondres.Omnia.Print.Common.Model;
using Pondres.Omnia.Print.Contracts.Api.Bundle;
using Pondres.Omnia.Print.Contracts.Api.Enums;
using Pondres.Omnia.Print.Contracts.Common;
using Pondres.Omnia.Print.Contracts.PrintDocument;
using Pondres.Omnia.Print.Storage.DataStorage;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.CommandHandler;

public record QueueNewDocumentGroupCommand(
    ConsumeContext Context,
    List<PrintDocumentCreationModel> Documents);

public class QueueNewDocumentGroupCommandHandler(
    PrintDocumentLocalFileStoreService printDocumentLocalFileStoreService,
    INavisionArticleService navisionArticleService,
    IDataStorageRepositoryFactory dataStorageRepositoryFactory,
    IPrintBundleBufferHandler printBundleBufferHandler,
    IPrintScheduleProvider printScheduleProvider,
    IPrintDocumentSaveHandler printSaveHandler,
    IPrintBundleActionHandler printBundleActionHandler)
{
    public async Task HandleAsync(QueueNewDocumentGroupCommand command)
    {
        var creationPairs = await BuildDocumentCreationPairsAsync(command);
        var documentGroup = ConvertCreationPairsToDocumentGroup(creationPairs);

        try
        {
            await QueuePrintDocumentsAsync(command.Context, documentGroup);

            await Parallel.ForEachAsync(
                source: creationPairs,
                body: async (pair, cancellationToken) =>
                {
                    await printDocumentLocalFileStoreService.StoreDocumentFileToLocalFileStoreAsync(
                        bundleId: pair.BundleId,
                        storageAccountName: pair.CreationModel.FileInformation.StorageAccountName,
                        storageContainerName: pair.CreationModel.FileInformation.StorageContainerName,
                        filePath: pair.CreationModel.FileInformation.PrintFilePath);

                    await printSaveHandler.CreateOrGetPrintDocumentAsync(
                        pair.CreationModel.ToSaveCommand(pair.Metadata, pair.BundleId));
                });


        }
        catch (Exception ex)
        {
            Log.Error(ex, "Failed. Reverting adding documents {@Command} to the bundle..", command);


            await Parallel.ForEachAsync(
                source: creationPairs,
                body: async (pair, cancellationToken) =>
                {
                    try
                    {
                        await printBundleActionHandler.RemoveDocumentFromBundleAsync(
                            new PrintBundleRemoveDocumentCommand
                            {
                                PrintBundleId = pair.BundleId,
                                DocumentId = pair.CreationModel.DocumentId
                            });
                    }
                    catch (PrintDocumentNotFoundException)
                    {
                        // fine: nothing to remove
                    }

                });

            throw;
        }
    }

    private static PrintDocumentGroup ConvertCreationPairsToDocumentGroup(List<PrintDocumentCreationPair> creationPairs)
    {
        var primaryPair = creationPairs.SingleOrDefault(x => x.Metadata.IsPrimary);

        var secondaryPairs = new List<PrintDocumentCreationPair>();
        if (primaryPair == null)
        {
            // backwards compatible with flows that only have a single document
            primaryPair = creationPairs[0];
            secondaryPairs.AddRange(creationPairs.Skip(1));
        }
        else
        {
            secondaryPairs.AddRange(creationPairs.Where(x => !x.Metadata.IsPrimary));
        }

        var allMetadata = creationPairs.Select(x => x.Metadata).ToArray();
        primaryPair.CreationModel.PrimaryDocumentId = primaryPair.CreationModel.DocumentId;
        primaryPair.BundleHash = primaryPair.Metadata.ToHash(allMetadata);
        foreach (var secondaryPair in secondaryPairs)
        {
            secondaryPair.CreationModel.PrimaryDocumentId = primaryPair.CreationModel.DocumentId;
            secondaryPair.BundleHash = secondaryPair.Metadata.ToHash(allMetadata);
        }

        var documentGroup = new PrintDocumentGroup(primaryPair, secondaryPairs);

        return documentGroup;
    }

    private async Task<List<PrintDocumentCreationPair>> BuildDocumentCreationPairsAsync(QueueNewDocumentGroupCommand command)
    {
        var creationPairs = new List<PrintDocumentCreationPair>();
        foreach (var model in command.Documents)
        {
            var metadataInput = await DownloadMetadataAsync(model);

            if (metadataInput.MailDate == null && string.IsNullOrWhiteSpace(metadataInput.ReleaseSchedule))
                throw new NoMailDateAndScheduleException(model.DocumentId);

            var mailDate = metadataInput.MailDate == null ?
                await CalculateNextReleaseMailDateAsync(metadataInput) :
                metadataInput.MailDate.Value;

            if (command.Documents.Count == 1)
                metadataInput.IsPrimary = true;

            var metadata = metadataInput.ToMetadata(mailDate);

            if (string.IsNullOrWhiteSpace(metadata.SheetFormat))
                await TryEnrichMetadataFromNavisionAsync(metadata);

            creationPairs.Add(new PrintDocumentCreationPair(metadata: metadata, creationModel: model));
        }

        return creationPairs;
    }

    private async Task<DateTimeOffset> CalculateNextReleaseMailDateAsync(PrintDocumentCreationMetadataInput metadataInput)
    {
        var schedule = await printScheduleProvider.GetApplicableScheduleAsync(metadataInput.ReleaseSchedule) ??
            throw new NoScheduleFoundException($"No schedule found for {metadataInput.ReleaseSchedule}");

        var nextRelease = schedule.CalculateNextRelease(DateTimeOffset.Now);

        return nextRelease.ReleaseOn.Date;
    }

    private async Task QueuePrintDocumentsAsync(
        IPublishEndpoint publishEndpoint,
        PrintDocumentGroup group,
        bool isReprint = false)
    {
        await printBundleBufferHandler.QueueDocumentGroupAsync(group);

        foreach (var pair in group.Secondaries)
        {
            if (!isReprint)
            {
                var message = new PrintDocumentQueued()
                {
                    DocumentMetadata = pair.Metadata.ToPrintDocumentMetadata(
                        documentId: pair.CreationModel.DocumentId,
                        primaryDocumentId: pair.CreationModel.PrimaryDocumentId),
                    OrderMetadata = pair.CreationModel.OrderMetadata,
                    MailDate = group.Primary.Metadata.MailDate,
                    PrintBundleId = pair.BundleId,
                    Timestamp = DateTimeOffset.Now
                };
                await publishEndpoint.Publish(message);
            }

            Log.Debug("Print document {DocumentId} was queued for bundle {BundleId}", pair.CreationModel.DocumentId, pair.BundleId);
        }
    }


    private async Task<PrintDocumentCreationMetadataInput> DownloadMetadataAsync(PrintDocumentCreationModel model)
    {
        Log.Debug("Downloading metadata {FileName} for print document {DocumentId}", model.FileInformation.MetaDataFilePath, model.DocumentId);

        var storageContainer = await dataStorageRepositoryFactory.GetStorageContainerAsync(
            model.FileInformation.StorageAccountName,
            model.FileInformation.StorageContainerName);

        var fileContent = await storageContainer.GetTextAsync(model.FileInformation.MetaDataFilePath);

        var metadataInput = JsonConvert.DeserializeObject<PrintDocumentCreationMetadataInput>(
            fileContent,
            new IsoDateTimeConverter() { DateTimeFormat = "dd-MM-yyyy" },
            new StringEnumConverter()) ?? throw new JsonSerializationException($"Failed to deserialize {nameof(PrintDocumentCreationMetadataInput)}");

        return metadataInput;
    }

    private async Task TryEnrichMetadataFromNavisionAsync(PrintDocumentCreationMetadata metadata)
    {
        try
        {
            var navisionArticle = await navisionArticleService.GetNavisionArticleByIdAsync(metadata.SheetArticleCode);
            metadata.SheetArticleDescription = navisionArticle.Description;
            metadata.SheetFormat = $"{navisionArticle.Format1}x{navisionArticle.Format2}";

            if (!string.IsNullOrWhiteSpace(metadata.Packaging))
            {
                navisionArticle = await navisionArticleService.GetNavisionArticleByIdAsync(metadata.Packaging);
                metadata.PackingName = navisionArticle.Description;
            }

            metadata.PackingType = navisionArticle.FormatCode.ToEnumOrDefaultWhenNoMatch(PackingType.Overig);

            foreach (var attachment in metadata.Attachments.Where(x => !x.ExcludeForNavision))
            {
                navisionArticle = await navisionArticleService.GetNavisionArticleByIdAsync(attachment.ArticleCode);

                attachment.ArticleName = navisionArticle.Description;
                attachment.PickLocation = navisionArticle.PickLocation;
            }
        }
        catch (Exception e)
        {
            throw new NavisionMetadataEnrichmentFailedException($"Failed to enrich print metadata with navision article data. Inner exception: \"{e.GetType().Name}: {e.Message}\"");
        }
    }
}