﻿using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.Caching.Memory;
using Solude.StorageBase;
using Pondres.Omnia.Print.Contracts.Api.Bundle;
using Pondres.Omnia.Print.Contracts.Common;
using Pondres.Omnia.Print.Storage.Configuration;
using Pondres.Omnia.Print.Storage.Entities.PrintBundle;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Storage.Repositories;

public class PrintBundleBasicRepository : SinglePartitionBaseRepository<PrintBundleBasicEntity>, IPrintBundleBasicRepository
{
    protected override bool CanWrite => false;

    protected override string PartitionKeyValue => CosmosConfiguration.PrintBundlePartitionValue;

    protected override MemoryCacheEntryOptions CreateCacheOptions() =>
        new()
        {
            SlidingExpiration = TimeSpan.FromMinutes(1)
        };

    public async Task<List<PrintBundleBasicEntity>> GetFutureBundlesForHashAsync(string bundleHash, DateTimeOffset? plannedOn, string? primaryBundleId)
    {
        var query = container.GetItemLinqQueryable<PrintBundleBasicEntity>()
            .Where(x =>
                x.ReleasedOn == null &&
                x.PlannedOn >= plannedOn &&
                x.Hash == bundleHash &&
                x.PrimaryBundleId == primaryBundleId &&
                x.StateName != PrintBundleStateType.Completed.ToString()
                )
            .OrderBy(x => x.PlannedOn);

        string? continuationToken = null;

        var allEntities = new List<PrintBundleBasicEntity>();

        do
        {
            var pagedEntityList = await GetEntityListAsync(query, continuationToken, 2);

            allEntities.AddRange(pagedEntityList.Entities);

            continuationToken = pagedEntityList.ContinuationToken;

        } while (!string.IsNullOrEmpty(continuationToken));

        return allEntities;
    }
    protected override string GetCacheKey(string id, string partitionKeyValue) => $"pbundlebasic_{id}_{partitionKeyValue}";

    public PrintBundleBasicRepository(CosmosClient cosmosClient, IMemoryCache memoryCache) :
        base(cosmosClient, CosmosConfiguration.PrintDatabaseName, CosmosConfiguration.PrintBundleContainerName, memoryCache)
    {
    }

    private IOrderedQueryable<PrintBundleBasicEntity> BuildQueryByDateRangeFilter(PrintBundleDateRangeFilter filter)
    {
        var query = container.GetItemLinqQueryable<PrintBundleBasicEntity>()
            .Where(x =>
                x.Quantities.DocumentCount > 0 &&
                x.CreatedOn >= filter.CreatedOnFrom &&
                x.CreatedOn <= filter.CreatedOnTo);

        query = !string.IsNullOrWhiteSpace(filter.Customer) ? query.Where(x => x.Customer == filter.Customer) : query;
        query = filter.PrinterType is not null ? query.Where(x => x.Metadata.PrinterType == filter.PrinterType.ToString()) : query;

        return query.OrderByDescending(x => x.CreatedOn);
    }

    public async Task<List<PrintBundleBasicEntity>> GetAllForDateRangeAsync(PrintBundleDateRangeFilter filter)
    {
        var query = BuildQueryByDateRangeFilter(filter);

        string? continuationToken = null;

        var allEntities = new List<PrintBundleBasicEntity>();

        do
        {
            var pagedEntityList = await GetEntityListAsync(query, continuationToken, 100);

            allEntities.AddRange(pagedEntityList.Entities);

            continuationToken = pagedEntityList.ContinuationToken;

        } while (!string.IsNullOrEmpty(continuationToken));

        return allEntities;
    }

    public async Task<List<PrintBundleBasicEntity>> GetAllSecondaryPrintBundlesAsync(string primaryBundleId)
    {
        var query = container.GetItemLinqQueryable<PrintBundleBasicEntity>()
            .Where(x => x.PrimaryBundleId == primaryBundleId)
            .OrderBy(x => x.Id);

        string? continuationToken = null;

        var allEntities = new List<PrintBundleBasicEntity>();

        do
        {
            var pagedEntityList = await GetEntityListAsync(query, continuationToken, 2);

            allEntities.AddRange(pagedEntityList.Entities);

            continuationToken = pagedEntityList.ContinuationToken;

        } while (!string.IsNullOrEmpty(continuationToken));

        return allEntities;
    }
}
