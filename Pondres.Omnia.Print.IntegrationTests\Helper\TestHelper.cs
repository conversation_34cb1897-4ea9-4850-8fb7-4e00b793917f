﻿namespace Pondres.Omnia.OrderHub.IntegrationTests.Helper;

public static class TestHelper
{
    public static async Task WaitForAsync(
        Func<Task> forFunc,
        TimeSpan? wait = null,
        CancellationToken cancellationToken = default)
    {
        var start = DateTime.Now;

        wait ??= TestDefaults.DefaultWaitTimeSpan; 

        while (!cancellationToken.IsCancellationRequested)
        {
            try
            {
                await forFunc();
                return;
            }
            catch (Exception exception)
            {
                Console.Out.WriteLine($"Exception during waiting: {exception.Message}");

                if (start + wait < DateTime.Now)
                    throw;
                else
                    await Task.Delay(50, cancellationToken);
            }
        }
    }

    public static async Task<TResult> WaitForAsync<TResult>(
        Func<Task<TResult>> forFunc,
        TimeSpan? wait = null,
        CancellationToken cancellationToken = default)
    {
        var start = DateTime.Now;

        wait ??= TestDefaults.DefaultWaitTimeSpan; 

        while (!cancellationToken.IsCancellationRequested)
        {
            try
            {
                return await forFunc();
            }
            catch (Exception exception)
            {
                Console.Out.WriteLine($"Exception during waiting: {exception.Message}");

                if (start + wait < DateTime.Now)
                    throw;
                else
                    await Task.Delay(50, cancellationToken);
            }
        }

        // Should never reach this
        return default!;
    }
}
