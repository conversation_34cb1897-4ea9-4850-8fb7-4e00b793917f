# Docker
# Build a Docker image 
# https://docs.microsoft.com/azure/devops/pipelines/languages/docker

trigger: none

resources:
- repo: self

variables:
- name: sonarProjectKey
  value: 'pondresnl_Pondres.Omnia.Print'
- name: sonarProjectName
  value: 'Pondres.Omnia.Print'
- group: Build - Common

pool:
  vmImage: 'ubuntu-latest'

stages:
- stage: BuildAndTest
  displayName: Build and Test
  jobs:  
  - job: BuildAndTest
    displayName: Build and Test .NET projects
    steps:
    - task: UseDotNet@2
      displayName: 'Install .NET 9 SDK'
      inputs:
        packageType: 'sdk'
        version: '9.x'
        installationPath: $(Agent.ToolsDirectory)/dotnet
    - task: SonarCloudPrepare@1
      inputs:
        SonarCloud: 'SonarCloud'
        organization: 'pondresnl'
        scannerMode: 'MSBuild'
        projectKey: '$(sonarProjectKey)'
        projectName: '$(sonarProjectName)'
        extraProperties: |
          # Additional properties that will be passed to the scanner, 
          # Put one key=value per line, example:
          # sonar.exclusions=**/*.bin
          sonar.cs.opencover.reportsPaths=$(Agent.TempDirectory)/**/coverage.opencover.xml
    - task: UseDotNet@2
      inputs:
        packageType: 'sdk'
        version: '8.x'
    - task: DotNetCoreCLI@2
      displayName: Restore
      inputs:
        command: 'restore'
        projects: '**/*.sln'
        feedsToUse: 'select'
        vstsFeed: '534b90ef-0e1b-475a-8b29-f869c9ffb2fd/202483c3-5d54-4aec-a875-a4bd78523c18'
        verbosityRestore: minimal
    - task: DotNetCoreCLI@2
      displayName: Build
      inputs:
        command: 'build'
        projects: '**/*.sln'
        arguments: '--no-restore'
    - task: DotNetCoreCLI@2
      displayName: Test
      inputs:
        command: 'test'
        projects: '**/*.sln'
        arguments: '--no-build --settings coverletArgs.runsettings -- xunit.parallelizeAssembly=true'
      env:
        CosmosDbEndpoint: "$(CosmosDbEndpoint)"
    - task: SonarCloudAnalyze@1
      inputs:
        jdkversion: 'JAVA_HOME_17_X64'
    - task: SonarCloudPublish@1
      inputs:
        pollingTimeoutSec: '300'