﻿using FluentAssertions;
using Moq;
using Moq.AutoMock;
using Solude.StorageBase.Model;
using Pondres.Omnia.Print.Business.Handler;
using Pondres.Omnia.Print.Contracts.PrintDocument;
using Pondres.Omnia.Print.Storage.Entities.PrintBundle;
using Pondres.Omnia.Print.Storage.Entities.PrintDocument;
using Pondres.Omnia.Print.Storage.Repositories;
using System;
using System.Threading.Tasks;
using Xunit;

namespace Pondres.Omnia.Print.Business.UnitTests.Handler;

public class PrintDocumentSaveHandlerTests
{
    private readonly AutoMocker mocker;
    private readonly PrintDocumentSaveHandler handler;

    public PrintDocumentSaveHandlerTests()
    {
        mocker = new AutoMocker(MockBehavior.Strict);

        handler = mocker.CreateInstance<PrintDocumentSaveHandler>();
    }

    [Fact]
    public async Task SavePrintDocument_SavesPrintDocument()
    {
        // Arrange
        var now = DateTimeOffset.Now;

        var command = new PrintDocumentSaveCommand
        {
            OrderMetadata = new Contracts.Common.OrderMetadata
            {
                Customer = "Customer",
                CustomerReference = "CustomerReference",
                Flow = "Flow",
                OrderId = Guid.NewGuid(),
                Categories = new Contracts.Common.OrderCategories()
            },
            DocumentMetadata = new PrintDocumentMetadata
            {
                DocumentId = "DocumentId",
                CustomerDocumentReference = "CustomerDocumentReference"
            },
            FileInformation = new PrintDocumentFileInformation
            {
                PrintFilePath = "PrintFilePath",
                MetaDataFilePath = "MetaDataFilePath",
                StorageAccountName = "StorageAccountName",
                StorageContainerName = "StorageContainerName"
            },
            MailDate = now,
            PrintBundleId = Guid.NewGuid(),
            Timestamp = now
        };

        var expectedTimeToLive = (int)(command.MailDate.AddDays(14) - now).TotalSeconds;

        mocker.GetMock<IPrintDocumentRepository>()
            .Setup(x => x.CreateAsync(It.IsAny<PrintDocumentEntity>()))
            .ReturnsAsync((PrintDocumentEntity x) => new CosmosResponse<PrintDocumentEntity>(Guid.NewGuid().ToString(), x));

        mocker.GetMock<IPrintBundleBasicRepository>()
            .Setup(x => x.GetSingleAsync(command.PrintBundleId.ToString(), false))
            .ReturnsAsync(new CosmosResponse<PrintBundleBasicEntity>(string.Empty, new PrintBundleBasicEntity()
            {
                Id = command.PrintBundleId.ToString(),
                BatchName = "BatchName",
                CreatedOn = now,
                Customer = "Customer",
                GordNumber = "GordNumber",
            }));

        // Act
        var result = await handler.CreateOrGetPrintDocumentAsync(command);

        // Assert
        result.Id.Should().Be(command.DocumentMetadata.DocumentId);
        result.CreatedOn.Should().Be(now);
        result.OrderMetadata.Customer.Should().Be(command.OrderMetadata.Customer);
        result.OrderMetadata.CustomerReference.Should().Be(command.OrderMetadata.CustomerReference);
        result.CustomerDocumentReference.Should().Be(command.DocumentMetadata.CustomerDocumentReference);
        result.FileInformation.StorageContainerName.Should().Be(command.FileInformation.StorageContainerName);
        result.FileInformation.StorageAccountName.Should().Be(command.FileInformation.StorageAccountName);
        result.FileInformation.DocumentFilePath.Should().Be(command.FileInformation.PrintFilePath);
        result.FileInformation.MetadataFilePath.Should().Be(command.FileInformation.MetaDataFilePath);
        result.OrderMetadata.Flow.Should().Be(command.OrderMetadata.Flow);
        result.OrderMetadata.OrderId.Should().Be(command.OrderMetadata.OrderId);
        result.TimeToLiveSeconds.Should().Be(expectedTimeToLive);

        mocker.VerifyAll();
    }

    [Fact]
    public async Task SavePrintDocument_MergesMailDate_OnPastMailDate()
    {
        // Arrange
        var now = DateTimeOffset.Now;
        var pastMailDate = now.AddMonths(-1);

        var command = new PrintDocumentSaveCommand
        {
            OrderMetadata = new Contracts.Common.OrderMetadata
            {
                Customer = "Customer",
                CustomerReference = "CustomerReference",
                Flow = "Flow",
                OrderId = Guid.NewGuid(),
                Categories = new Contracts.Common.OrderCategories()
            },
            DocumentMetadata = new PrintDocumentMetadata
            {
                DocumentId = "DocumentId",
                CustomerDocumentReference = "CustomerDocumentReference"
            },
            FileInformation = new PrintDocumentFileInformation
            {
                PrintFilePath = "PrintFilePath",
                MetaDataFilePath = "MetaDataFilePath",
                StorageAccountName = "StorageAccountName",
                StorageContainerName = "StorageContainerName"
            },
            MailDate = pastMailDate,
            PrintBundleId = Guid.NewGuid(),
            Timestamp = now
        };

        var expectedTimeToLive = (int)(now.AddDays(14) - now).TotalSeconds;

        mocker.GetMock<IPrintDocumentRepository>()
            .Setup(x => x.CreateAsync(It.IsAny<PrintDocumentEntity>()))
            .ReturnsAsync((PrintDocumentEntity x) => new CosmosResponse<PrintDocumentEntity>(Guid.NewGuid().ToString(), x));

        mocker.GetMock<IPrintBundleBasicRepository>()
            .Setup(x => x.GetSingleAsync(command.PrintBundleId.ToString(), false))
            .ReturnsAsync(new CosmosResponse<PrintBundleBasicEntity>(string.Empty, new PrintBundleBasicEntity()
            {
                Id = command.PrintBundleId.ToString(),
                BatchName = "BatchName",
                CreatedOn = now,
                Customer = "Customer",
                GordNumber = "GordNumber",
            }));

        // Act
        var result = await handler.CreateOrGetPrintDocumentAsync(command);

        // Assert
        result.Id.Should().Be(command.DocumentMetadata.DocumentId);
        result.CreatedOn.Should().Be(now);
        result.OrderMetadata.Customer.Should().Be(command.OrderMetadata.Customer);
        result.OrderMetadata.CustomerReference.Should().Be(command.OrderMetadata.CustomerReference);
        result.CustomerDocumentReference.Should().Be(command.DocumentMetadata.CustomerDocumentReference);
        result.FileInformation.StorageContainerName.Should().Be(command.FileInformation.StorageContainerName);
        result.FileInformation.StorageAccountName.Should().Be(command.FileInformation.StorageAccountName);
        result.FileInformation.DocumentFilePath.Should().Be(command.FileInformation.PrintFilePath);
        result.FileInformation.MetadataFilePath.Should().Be(command.FileInformation.MetaDataFilePath);
        result.OrderMetadata.Flow.Should().Be(command.OrderMetadata.Flow);
        result.OrderMetadata.OrderId.Should().Be(command.OrderMetadata.OrderId);
        result.TimeToLiveSeconds.Should().Be(expectedTimeToLive);

        mocker.VerifyAll();
    }
}
