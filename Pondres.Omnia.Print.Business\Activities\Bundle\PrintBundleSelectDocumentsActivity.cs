﻿using MassTransit;
using Pondres.Omnia.Print.Business.Extensions;
using Pondres.Omnia.Print.Business.Service;
using Pondres.Omnia.Print.Business.StateMachine.PrintBundle.Model;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Activities.Bundle;

public class PrintBundleSelectDocumentsActivity : BaseInstanceActivity<PrintBundleState>
{
    private readonly IPrintBundleDocumentStorageService bundleDocumentStorageService;

    public PrintBundleSelectDocumentsActivity(IPrintBundleDocumentStorageService bundleDocumentStorageService)
    {
        this.bundleDocumentStorageService = bundleDocumentStorageService;
    }

    public override string ActivityName => "print-bundle-select-documents";

    protected override async Task ExecuteAsync(BehaviorContext<PrintBundleState> context)
    {
        var documentCollection = await bundleDocumentStorageService.RequeueFalloffDocumentsAndMarkAsStartedAsync(
            bundleId: context.Saga.CorrelationId,
            bundleMode: context.Saga.BundleOptions.BundleMode,
            printMode: context.Saga.PrintMode,
            sheetPageCount: context.Saga.SheetPageCount);

        context.Saga.RecalculateTotals(documentCollection.Documents);

        await context.Publish(context.Saga.ToQuantitiesUpdatedEvent());
    }
}