﻿using Pondres.Omnia.Print.Contracts.Common;
using System.Collections.Generic;
using System.Linq;

namespace Pondres.Omnia.Print.Business.Extensions;

public static class PrintDocumentCreationMetadataAttachmentInputExtensions
{
    public static List<PrintDocumentCreationMetadataAttachment> ToMetadataAttachments(this List<PrintDocumentCreationMetadataAttachmentInput> attachments) =>
        attachments.Select(x => x.ToMetadataAttachment()).ToList();

    public static PrintDocumentCreationMetadataAttachment ToMetadataAttachment(this PrintDocumentCreationMetadataAttachmentInput attachment) =>
        new()
        {
            ArticleCode = attachment.ArticleCode,
            ArticleName = attachment.ArticleName,
            PickLocation = attachment.PickLocation,
            ExcludeForNavision = attachment.ExcludeForNavision
        };
}
