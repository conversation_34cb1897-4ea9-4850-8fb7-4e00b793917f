﻿using Pondres.Omnia.Print.Business.Helper;
using Pondres.Omnia.Print.Business.StateMachine.PrintBundle.Model;
using Pondres.Omnia.Print.Contracts.PrintBatch;
using Pondres.Omnia.Print.Contracts.PrintBundle;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Pondres.Omnia.Print.Business.Extensions;

public static class PrintBundleStateExtensions
{
    public static void PopulateStateFromCreateCommand(this PrintBundleState state, PrintBundleCreateCommand command)
    {
        state.Customer = command.Customer;
        state.BundleOptions = command.BundleOptions;
        state.InitiatedBy = command.InitiatedBy;
        state.BatchName = command.BatchName;
        state.CreatedOn = DateTimeOffset.Now;
        state.SheetArticleCode = command.SheetArticleCode;
        state.PrintMode = command.PrintMode;
        state.DGCode = command.DGCode;
        state.EndLocation = command.EndLocation;
        state.PlannedOn = command.PlannedOn;
        state.SheetPageCount = command.SheetPageCount;
        state.IsPrimary = command.IsPrimary;
        state.PrimaryBundleId = command.PrimaryBundleId;
        state.IsDirectlyCompleted = command.IsDirectlyCompleted;
    }

    public static PrintBundleDelayed ToPrintBundleDelayed(this PrintBundleState state)
        => new()
        {
            CreatedOn = state.CreatedOn,
            CurrentState = state.CurrentState,
            Customer = state.Customer,
            PrintBundleId = state.CorrelationId,
            BatchName = state.BatchName,
            Timestamp = DateTimeOffset.Now
        };

    public static PrintBundleSkipped ToPrintBundleSkipped(this PrintBundleState state)
        => new()
        {
            Customer = state.Customer,
            PrintBundleId = state.CorrelationId,
            Timestamp = DateTimeOffset.Now,
            Message = "Skipped, no documents for impositioning",
            BatchName = state.BatchName,
            Status = state.CurrentState
        };

    public static PrintBundleStarted ToPrintBundleStarted(this PrintBundleState state)
        => new()
        {
            BatchName = state.BatchName,
            Customer = state.Customer,
            PrintBundleId = state.CorrelationId,
            Timestamp = DateTimeOffset.Now,
            Message = "Started",
            Status = state.CurrentState,
            DocumentCount = state.DocumentsTotalRecordCount,
            PageCount = state.DocumentsTotalPageCount
        };

    public static PrintBundleCancelResponse ToPrintBundleCancelResponse(this PrintBundleState state)
        => new()
        {
            Message = "Print bundle cancelled",
            Success = true,
            PrintBundleId = state.CorrelationId
        };

    public static PrintBundleReleaseResponse ToReleaseResponse(this PrintBundleState state)
        => new()
        {
            Success = true,
            Message = "Release successful",
            PrintBundleId = state.CorrelationId
        };

    public static PrintBundleManualContinueResponse ToPrintBundleManualContinueResponse(this PrintBundleState state)
        => new()
        {
            PrintBundleId = state.CorrelationId,
            Message = state.CurrentState,
            Success = true
        };

    public static PrintBundleManualEvent ToPrintBundleManualEvent(this PrintBundleState state, string username, string @event)
        => new()
        {
            Event = @event,
            Username = username,
            Timestamp = DateTimeOffset.Now,
            PrintBundleId = state.CorrelationId
        };

    public static PrintBundleManualEvent ToPrintBundleCompletedByCancellationMessage(this PrintBundleState state, string username)
        => state.ToPrintBundleManualEvent(username, "Print bundle cancelled");

    public static PrintBundleManualEvent ToPrintConfirmedEvent(this PrintBundleState state, string username)
        => state.ToPrintBundleManualEvent(username, "Bundle printed confirmed by print");
    public static PrintBundleManualEvent ToCompletedConfirmedEvent(this PrintBundleState state, string username)
        => state.ToPrintBundleManualEvent(username, "Bundle completed confirmed by print");

    public static PrintBundleManualEvent ToManualContinueEvent(this PrintBundleState state, string username)
        => state.ToPrintBundleManualEvent(username, "Manual continue requested");

    public static PrintBundleSetOnHold ToPrintBundleSetOnHold(this PrintBundleState state)
        => new()
        {
            CurrentState = state.CurrentState,
            Customer = state.Customer,
            BatchName = state.BatchName,
            PrintBundleId = state.CorrelationId
        };

    public static PrintBundleCancelled ToPrintBundleCancelled(this PrintBundleState state)
        => new()
        {
            Customer = state.Customer,
            PrintBundleId = state.CorrelationId,
            Timestamp = DateTimeOffset.Now,
            Message = "Cancelled",
            BatchName = state.BatchName,
            Status = state.CurrentState,
        };

    public static PrintBundleCompleted ToPrintBundleCompleted(this PrintBundleState state, string username)
        => new()
        {
            Customer = state.Customer,
            PrintBundleId = state.CorrelationId,
            Timestamp = DateTimeOffset.Now,
            CompletedBy = username,
            Message = "Completed",
            BatchName = state.BatchName,
            Status = state.CurrentState,
            SheetArticleCode = state.SheetArticleCode,
            SheetPageCount = state.SheetPageCount,
            DocumentCount = state.DocumentsTotalQuantity,
            TotalPageCount = state.DocumentsTotalPageCount
        };

    public static PrintBundlePrintConfirmationResponse ToPrintBundlePrintConfirmationResponse(this PrintBundleState state, string username)
        => new()
        {
            Confirmed = true,
            PrintBundleId = state.CorrelationId,
            ConfirmedBy = username
        };

    public static PrintBundleCompletedConfirmationResponse ToPrintBundleCompletedConfirmationResponse(this PrintBundleState state, string username)
        => new()
        {
            Confirmed = true,
            PrintBundleId = state.CorrelationId,
            ConfirmedBy = username
        };

    public static PrintBundleScannedConfirmationResponse ToPrintBundleScannedConfirmationResponse(this PrintBundleState state, string username)
        => new()
        {
            Confirmed = true,
            PrintBundleId = state.CorrelationId,
            ConfirmedBy = username
        };

    public static PrintBundleStatusResponse ToPrintBundleStatusResponse(this PrintBundleState state)
        => new()
        {
            CorrelationId = state.CorrelationId,
            CurrentState = state.CurrentState,
            Version = state.Version,
        };

    public static PrintBundleReprintRequested ToPrintBundleReprintRequested(this PrintBundleState state, string username) =>
        new()
        {
            PrintBundleId = state.CorrelationId,
            Requester = username
        };

    public static void AddTaskAndGordNumber(this PrintBundleState state, PrintBundleAssignTaskCompleted message)
    {
        state.TaskNumber = message.TaskNumber;
        state.GordNumber = message.GordNumber;
    }

    public static PrintBundleQuantitiesUpdated ToQuantitiesUpdatedEvent(this PrintBundleState state) =>
        new()
        {
            PrintBundleId = state.CorrelationId,
            Timestamp = DateTimeOffset.Now,
            DocumentsQuantity = state.DocumentsTotalQuantity,
            DocumentsPageCount = state.DocumentsTotalPageCount,
            DocumentsRecordCount = state.DocumentsTotalRecordCount
        };

    public static void RecalculateTotals(this PrintBundleState state, HashSet<PrintBundleQueuedDocument> documents)
    {
        state.DocumentsTotalQuantity = documents.Sum(x => x.DocumentMetadata.Quantity);
        state.DocumentsTotalPageCount = documents.Sum(x => x.DocumentMetadata.PageCount * x.DocumentMetadata.Quantity);
        state.DocumentsTotalRecordCount = documents.Count;
    }

    public static PrintBundleTaskAssignRequest ToBundleBatchAssignRequest(this PrintBundleState state) =>
        new()
        {
            PrintBundleId = state.CorrelationId,
            Timestamp = DateTimeOffset.Now,
            BatchName = state.BatchName,
            DGCode = state.DGCode,
            DocumentCount = state.DocumentsTotalQuantity,
            SheetCount = SheetCountCalculationHelper.CalculateSheetCount(
                printMode: state.PrintMode,
                sheetPageCount: state.SheetPageCount,
                totalPageCount: state.DocumentsTotalPageCount)
        };

}
