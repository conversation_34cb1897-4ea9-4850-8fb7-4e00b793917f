﻿using Microsoft.Azure.Cosmos;
using Pondres.Omnia.Print.Storage.Configuration;
using Solude.ApiBase.Contracts;
using System;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.PrintManager.Migrations;

public class CosmosMigrator(CosmosClient cosmosClient) : IDatabaseMigrator
{
    public async Task MigrateAsync()
    {
        var databaseResponse = await CreateDatabaseAsync();

        // Create the containers
        await CreateContainersAsync(databaseResponse);

        await MigratePrintDocumentContainerAsync();

        await MigratePrintBundleStatusHistoryContainerAsync();
    }

    private static async Task CreateContainersAsync(DatabaseResponse database)
    {
        await database.Database.CreateContainerIfNotExistsAsync(new ContainerProperties(
                CosmosConfiguration.PrintDataSheetContainerName, CosmosConfiguration.PrintDataSheetPartitionKey));

        await database.Database.CreateContainerIfNotExistsAsync(new ContainerProperties(
                CosmosConfiguration.PrintBundleContainerName, CosmosConfiguration.PrintBundlePartitionKey));

        await database.Database.CreateContainerIfNotExistsAsync(new ContainerProperties(
                CosmosConfiguration.SchedulesContainerName, CosmosConfiguration.SchedulesPartitionKey));

        await database.Database.CreateContainerIfNotExistsAsync(new ContainerProperties(
                CosmosConfiguration.PrintDocumentContainerName, CosmosConfiguration.PrintDocumentPartitionKey));

        await database.Database.CreateContainerIfNotExistsAsync(new ContainerProperties(
                CosmosConfiguration.DivisionMappingContainerName, CosmosConfiguration.DivisionMappingPartitionKey));

        await database.Database.CreateContainerIfNotExistsAsync(new ContainerProperties(
                CosmosConfiguration.PrintBundleStatusHistoryContainerName, CosmosConfiguration.PrintBundleStatusHistoryPartitionKey));

        await database.Database.CreateContainerIfNotExistsAsync(new ContainerProperties(
            CosmosConfiguration.ConfigurationContainerName, CosmosConfiguration.ConfigurationPartitionKey));
    }

    private async Task MigratePrintBundleStatusHistoryContainerAsync()
    {
        var containerResponse = await cosmosClient
            .GetContainer(
                databaseId: CosmosConfiguration.PrintDatabaseName,
                containerId: CosmosConfiguration.PrintBundleStatusHistoryContainerName)
            .ReadContainerAsync();

        containerResponse.Resource.IndexingPolicy.IndexingMode = IndexingMode.Consistent;

        containerResponse.Resource.IndexingPolicy.IncludedPaths.Clear();
        containerResponse.Resource.IndexingPolicy.IncludedPaths.Add(new IncludedPath { Path = "/*" });

        containerResponse.Resource.IndexingPolicy.ExcludedPaths.Clear();
        containerResponse.Resource.IndexingPolicy.ExcludedPaths.Add(new ExcludedPath { Path = "/\"_etag\"/?" });

        containerResponse.Resource.DefaultTimeToLive = (int)TimeSpan.FromDays(14).TotalSeconds;

        await cosmosClient
            .GetContainer(
                databaseId: CosmosConfiguration.PrintDatabaseName,
                containerId: CosmosConfiguration.PrintBundleStatusHistoryContainerName)
            .ReplaceContainerAsync(containerResponse.Resource);
    }

    private async Task MigratePrintDocumentContainerAsync()
    {
        var containerResponse = await cosmosClient.GetContainer(
                        CosmosConfiguration.PrintDatabaseName,
                        CosmosConfiguration.PrintDocumentContainerName).ReadContainerAsync();

        containerResponse.Resource.IndexingPolicy.IndexingMode = IndexingMode.Consistent;

        containerResponse.Resource.IndexingPolicy.IncludedPaths.Clear();
        containerResponse.Resource.IndexingPolicy.IncludedPaths.Add(new IncludedPath { Path = "/*" });

        containerResponse.Resource.IndexingPolicy.ExcludedPaths.Clear();
        containerResponse.Resource.IndexingPolicy.ExcludedPaths.Add(new ExcludedPath { Path = "/\"_etag\"/?" });
        containerResponse.Resource.IndexingPolicy.ExcludedPaths.Add(new ExcludedPath { Path = "/\"fileInformation\"/*" });

        // This enables time to live
        containerResponse.Resource.DefaultTimeToLive = -1;

        await cosmosClient.GetContainer(
            CosmosConfiguration.PrintDatabaseName,
            CosmosConfiguration.PrintDocumentContainerName).ReplaceContainerAsync(containerResponse.Resource);
    }

    private async Task<DatabaseResponse> CreateDatabaseAsync() =>
        await cosmosClient.CreateDatabaseIfNotExistsAsync(CosmosConfiguration.PrintDatabaseName,
            ThroughputProperties.CreateManualThroughput(400));
}