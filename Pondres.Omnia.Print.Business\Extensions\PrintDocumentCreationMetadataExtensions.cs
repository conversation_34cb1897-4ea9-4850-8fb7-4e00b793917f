﻿using Pondres.Omnia.Print.Contracts.Common;
using Pondres.Omnia.Print.Contracts.PrintDocument;

namespace Pondres.Omnia.Print.Business.Extensions;

public static class PrintDocumentCreationMetadataExtensions
{
    public static PrintDocumentMetadata ToPrintDocumentMetadata(
        this PrintDocumentCreationMetadata creationMetadata,
        string documentId,
        string? primaryDocumentId) =>
        new()
        {
            DocumentId = documentId,
            PrimaryDocumentId = primaryDocumentId,
            CustomerDocumentReference = creationMetadata.CustomerDocumentReference,
            PrinterType = creationMetadata.PrinterType,
            Quantity = creationMetadata.Quantity,
            PageCount = creationMetadata.PageCount,
            ReleaseSchedule = creationMetadata.ReleaseSchedule,
            DocumentStatus = creationMetadata.DocumentStatus
        };
}