﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Azure.Storage.Files.Shares" Version="12.22.0" />
		<PackageReference Include="FluentValidation" Version="12.0.0" />
		<PackageReference Include="MassTransit" Version="8.4.1" />
		<PackageReference Include="Polly" Version="8.5.2" />
		<PackageReference Include="StackExchange.Redis" Version="2.8.37" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Pondres.Omnia.Print.Common\Pondres.Omnia.Print.Common.csproj" />
		<ProjectReference Include="..\Pondres.Omnia.Print.Contracts\Pondres.Omnia.Print.Contracts.csproj" />
		<ProjectReference Include="..\Pondres.Omnia.Print.Integrations.Navision\Pondres.Omnia.Print.Integrations.Navision.csproj" />
		<ProjectReference Include="..\Pondres.Omnia.Print.Storage\Pondres.Omnia.Print.Storage.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Folder Include="Storage\PrintScheduleOverrides\" />
	</ItemGroup>

</Project>
