﻿using Pondres.Omnia.Print.Business.Extensions;
using Pondres.Omnia.Print.Contracts.Common;
using Pondres.Omnia.Print.Contracts.PrintBundle;
using Pondres.Omnia.Print.Storage.Entities.PrintBundle;
using Pondres.Omnia.Print.Storage.Entities.PrintBundleStatus;
using Pondres.Omnia.Print.Storage.Repositories;
using Serilog;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Service;

public class PrintBundleEventService : IPrintBundleEventService
{
    private readonly IPrintBundleDetailsRepository printBundleRepository;

    public PrintBundleEventService(IPrintBundleDetailsRepository printBundleRepository)
    {
        this.printBundleRepository = printBundleRepository;
    }

    public async Task<PrintBundleDetailsEntity> RegisterBundleCancellationAsync(PrintBundleCancelled message)
    {
        var bundleStatusDetailsEntity = message.ToStatusDetailsEntity();

        var printBundleEntity = await GetAndUpdateBundleAsync(
            message.PrintBundleId,
            printBundleEntity => CompleteBundle(printBundleEntity, bundleStatusDetailsEntity));

        return printBundleEntity;
    }

    public async Task<PrintBundleDetailsEntity> RegisterBundleCompletionAsync(PrintBundleCompleted message)
    {
        var bundleStatusDetailsEntity = message.ToStatusDetailsEntity();

        var printBundleEntity = await GetAndUpdateBundleAsync(
            message.PrintBundleId,
            printBundleEntity => CompleteBundle(printBundleEntity, bundleStatusDetailsEntity));

        return printBundleEntity;
    }

    public async Task<PrintBundleDetailsEntity> RegisterBundleSkippedAsync(PrintBundleSkipped message)
    {
        var bundleStatusDetailsEntity = message.ToStatusDetailsEntity();

        var printBundleEntity = await GetAndUpdateBundleAsync(
            message.PrintBundleId,
            printBundleEntity => CompleteBundle(printBundleEntity, bundleStatusDetailsEntity));

        return printBundleEntity;
    }

    public async Task<PrintBundleDetailsEntity> RegisterBundleStartedAsync(PrintBundleStarted message)
    {
        var bundleStatusDetailsEntity = message.ToStatusDetailsEntity();

        var printBundleEntity = await GetAndUpdateBundleAsync(
            message.PrintBundleId,
            printBundleEntity =>
            {
                printBundleEntity.ReleasedOn = message.Timestamp;
                printBundleEntity.State = PrintBundleStateType.Active;
                printBundleEntity.BatchName = message.BatchName;

                return UpdatePrintBundleStatus(printBundleEntity, bundleStatusDetailsEntity);
            });

        return printBundleEntity;
    }

    public async Task<PrintBundleDetailsEntity> RegisterManualEventAsync(PrintBundleManualEvent message)
    {
        var eventEntity = message.ToEntity();

        var updatedBundleEntity = await GetAndUpdateBundleAsync(
            message.PrintBundleId,
            printBundleEntity => RegisterManualEvent(printBundleEntity, eventEntity));

        return updatedBundleEntity;
    }

    public async Task<PrintBundleDetailsEntity> RegisterWaitingForStartEventAsync(
        Guid printBundleId,
        DateTimeOffset timestamp,
        PrintBundleStateType stateType)
    {
        var printBundleEntity = await GetAndUpdateBundleAsync(
            printBundleId,
            printBundleEntity => UpdatePrintBundleStateType(printBundleEntity, timestamp, stateType));

        return printBundleEntity;
    }

    public async Task<PrintBundleDetailsEntity> UpdateFileListAsync(PrintBundleImpositioningCompleted message) =>
        await GetAndUpdateBundleAsync(
            message.PrintBundleId,
            printBundleEntity => UpdateBundleFileList(printBundleEntity, message.ResultFileNames));

    public async Task<PrintBundleDetailsEntity> UpdateStatusAsync(PrintBundleStatusUpdate message)
    {
        var bundleStatusDetailsEntity = message.ToStatusDetailsEntity();

        var printBundleEntity = await GetAndUpdateBundleAsync(
            message.PrintBundleId,
            printBundleEntity => UpdatePrintBundleStatus(printBundleEntity, bundleStatusDetailsEntity));

        return printBundleEntity;
    }

    public async Task UpdateQuantitiesAsync(PrintBundleQuantitiesUpdated message)
    {
        await GetAndUpdateBundleAsync(
            message.PrintBundleId,
            printBundleEntity => UpdateQuantities(printBundleEntity, message));
    }

    private static bool CompleteBundle(PrintBundleDetailsEntity printBundleEntity, PrintBundleStatusDetailsEntity newStatusEntity)
    {
        if (newStatusEntity.Timestamp <= printBundleEntity.LastStatus.Timestamp)
            return false;

        printBundleEntity.State = PrintBundleStateType.Completed;
        printBundleEntity.LastStatus = newStatusEntity;

        return true;
    }

    private static bool RegisterManualEvent(PrintBundleDetailsEntity printBundleEntity, PrintBundleManualEventEntity manualEventEntity)
    {
        printBundleEntity.ManualEvents.Add(manualEventEntity);

        return true;
    }

    private static bool UpdateBundleFileList(PrintBundleDetailsEntity printBundleEntity, List<string> fileNames)
    {
        printBundleEntity.Files = fileNames;

        return true;
    }

    private static bool UpdatePrintBundleStatus(PrintBundleDetailsEntity printBundleEntity, PrintBundleStatusDetailsEntity newStatusEntity)
    {
        if (newStatusEntity.Timestamp <= printBundleEntity.LastStatus.Timestamp)
            return false;

        printBundleEntity.LastStatus = newStatusEntity;

        return true;
    }

    private static bool UpdatePrintBundleStateType(PrintBundleDetailsEntity printBundleEntity, DateTimeOffset timestamp, PrintBundleStateType stateType)
    {
        if (timestamp <= printBundleEntity.LastStatus.Timestamp)
            return false;

        printBundleEntity.State = stateType;

        return true;
    }

    private static bool UpdateQuantities(PrintBundleDetailsEntity printBundleEntity, PrintBundleQuantitiesUpdated updatedQuantities)
    {
        if (updatedQuantities.Timestamp <= printBundleEntity.Quantities.Timestamp)
            return false;

        printBundleEntity.Quantities = new PrintBundleQuantitiesEntity
        {
            DocumentCount = updatedQuantities.DocumentsRecordCount,
            PageCount = updatedQuantities.DocumentsPageCount,
            Quantity = updatedQuantities.DocumentsQuantity,
            Timestamp = updatedQuantities.Timestamp
        };

        return true;
    }

    private async Task<PrintBundleDetailsEntity> GetAndUpdateBundleAsync(Guid bundleId, Func<PrintBundleDetailsEntity, bool> updateAction)
    {
        Log.Debug("Retrieving print bundle {BundleId} from database to update state", bundleId);

        var currentPrintBundle = await printBundleRepository.GetSingleAsync(bundleId.ToString());

        if (updateAction(currentPrintBundle))
            await printBundleRepository.ReplaceConsistentAsync(currentPrintBundle, updateAction);
        else
            Log.Debug("Update function for bundle {BundleId} yielded false, not updating entity", bundleId);

        return currentPrintBundle;
    }
}