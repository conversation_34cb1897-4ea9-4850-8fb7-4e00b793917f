﻿using MassTransit;
using Pondres.Omnia.Print.Business.Constants;
using System;

namespace Pondres.Omnia.Print.Business.Consumers;

public class PrintDocumentSaveCommandConsumerDefinition : ConsumerDefinition<PrintDocumentSaveCommandConsumer>
{
    public PrintDocumentSaveCommandConsumerDefinition()
    {
        Endpoint(x => x.Name = QueueConstants.PrintDocumentSaveCommandQueueName);
    }

    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator, 
        IConsumerConfigurator<PrintDocumentSaveCommandConsumer> consumerConfigurator, IRegistrationContext context)
    {
        consumerConfigurator.UseMessageRetry(x => x.Interval(3, TimeSpan.FromMinutes(1)));
    }
}