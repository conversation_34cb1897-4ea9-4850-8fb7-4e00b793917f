﻿using MassTransit;
using System;

namespace Pondres.Omnia.Print.Business.Consumers;

public class PrintBundleEventHistoryConsumerDefinition : ConsumerDefinition<PrintBundleEventHistoryConsumer>
{
    public PrintBundleEventHistoryConsumerDefinition()
    {

    }

    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator, 
        IConsumerConfigurator<PrintBundleEventHistoryConsumer> consumerConfigurator,
        IRegistrationContext context)
    {
        endpointConfigurator.UseRateLimit(250, TimeSpan.FromSeconds(5));

        consumerConfigurator.UseMessageRetry(config => config.Interval(10, TimeSpan.FromSeconds(30)));
    }
}