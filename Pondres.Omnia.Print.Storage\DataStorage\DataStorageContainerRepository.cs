﻿using Azure.Storage.Blobs;
using Azure.Storage.Sas;
using Pondres.Omnia.Print.Storage.DataStorage.Model;
using System;
using System.IO;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Storage.DataStorage;

public class DataStorageContainerRepository : IDataStorageContainerRepository
{
    private readonly BlobServiceClient client;
    private readonly BlobContainerClient container;
    private readonly ISharedAccessUrlGenerator sharedAccessUrlGenerator;

    public DataStorageContainerRepository(
        ISharedAccessUrlGenerator sharedAccessUrlGenerator,
        BlobServiceClient client,
        string containerName)
    {
        container = client.GetBlobContainerClient(containerName.ToLower());
        this.sharedAccessUrlGenerator = sharedAccessUrlGenerator;
        this.client = client;
    }

    public async Task CreateIfNotExistsAsync()
    {
        await container.CreateIfNotExistsAsync();
    }

    public async Task DownloadToStreamAsync(Stream stream, string filePath)
    {
        var blobClient = container.GetBlobClient(filePath);
        
        await blobClient.DownloadToAsync(stream);
    }

    public async Task<TemporaryDataUri> GetSharedAccessSignatureUrlForBlobAsync(string blobName, TimeSpan expireTime)
    {
        return await sharedAccessUrlGenerator.GetSharedAccessSignatureUrlForBlobAsync(client, container, blobName, expireTime);
    }

    public async Task<TemporaryDataUri> GetSharedAccessSignatureUrlForContainerAsync(TimeSpan expireTime)
    {
        var permissions = BlobContainerSasPermissions.Read;

        return await sharedAccessUrlGenerator.GetSharedAccessSignatureForContainerAsync(client, container, permissions, expireTime);
    }

    public async Task<string> GetTextAsync(string filePath)
    {
        var blobClient = container.GetBlobClient(filePath);

        var blobContent = await blobClient.DownloadStreamingAsync();

        using var reader = new StreamReader(blobContent.Value.Content);

        return await reader.ReadToEndAsync();
    }

    public async Task UploadTextAsync(string fileContents, string filePath, bool overwrite = false)
    {
        var blobClient = container.GetBlobClient(filePath);

        using var stream = new MemoryStream(System.Text.Encoding.UTF8.GetBytes(fileContents));

        await blobClient.UploadAsync(stream, overwrite);
    }
}