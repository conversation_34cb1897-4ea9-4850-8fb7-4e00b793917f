﻿using Pondres.Omnia.Print.Storage.Entities.Navision;
using Pondres.Omnia.Print.Storage.Repositories;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Service;

public class NavisionArticleService : INavisionArticleService
{
    private readonly INavisionArticleRepository navisionArticleRepository;

    public NavisionArticleService(INavisionArticleRepository navisionArticleRepository)
    {
        this.navisionArticleRepository = navisionArticleRepository;
    }

    public async Task<NavisionArticleEntity> GetNavisionArticleByIdAsync(string id) =>
        await navisionArticleRepository.GetSingleAsync(id);
}
