## Parameters
$SUBSCRIPTION_ID   = "63e5e7f6-224b-4a5d-a921-c803db7e0fc6" #$args[0]
$RESOURCEGROUP  = "rg-quadient-prod"
$LOCATION        = "westeurope"
$STORAGE_SKU = "Standard_LRS"
$RESOURCEGROUPAKS  = "rg-omnia-prod"
$CLUSTERNAME = "aks-omnia-prod"

az account set --subscription $SUBSCRIPTION_ID

# PondresSync Shared Storage
    $STORAGE_NAME = "stpondressyncprod"
    $AKS_FILESHARE ="scaler-pondressync-storage"

    # Get storage account key
    $STORAGE_KEY=az storage account keys list --resource-group $RESOURCEGROUP --account-name $STORAGE_NAME --query "[0].value" -o tsv
    Write-Output $STORAGE_KEY

    # Echo storage account name and key
    Write-Output Storage account name: $STORAGE_NAME
    Write-Output Storage account key: $STORAGE_KEY

    # Create storage secret
    az aks get-credentials --name $CLUSTERNAME --resource-group $RESOURCEGROUPAKS
    kubectl config use-context $CLUSTERNAME 
    kubectl create secret generic pondressync-shared-storage-secret --namespace omnia --from-literal=azurestorageaccountname=$STORAGE_NAME --from-literal=azurestorageaccountkey=$STORAGE_KEY