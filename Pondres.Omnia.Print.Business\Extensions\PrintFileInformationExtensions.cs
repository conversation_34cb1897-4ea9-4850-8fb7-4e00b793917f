﻿using Pondres.Omnia.Print.Contracts.PrintDocument;
using Pondres.Omnia.Print.Storage.Entities.PrintDocument;

namespace Pondres.Omnia.Print.Business.Extensions;

public static class PrintFileInformationExtensions
{
    public static PrintFileInformationEntity ToPrintFileInformationEntity(this PrintDocumentFileInformation model) =>
        new PrintFileInformationEntity
        {
            MetadataFilePath = model.MetaDataFilePath,
            DocumentFilePath = model.PrintFilePath,
            StorageAccountName = model.StorageAccountName,
            StorageContainerName = model.StorageContainerName
        };
}