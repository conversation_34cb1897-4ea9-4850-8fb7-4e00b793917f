﻿using MassTransit;
using Pondres.Omnia.Print.Business.CommandHandler;
using Pondres.Omnia.Print.Contracts.PrintDocument;
using Serilog;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Consumers;

public class PrintDocumentRequeueCommandConsumer : IConsumer<PrintDocumentRequeueCommand>
{
    private readonly RequeueDocumentsForOrderCommandHandler requeueDocumentGroupCommandHandler;
    private readonly ILogger logger;

    public PrintDocumentRequeueCommandConsumer(RequeueDocumentsForOrderCommandHandler requeueDocumentGroupCommandHandler, ILogger logger)
    {
        this.requeueDocumentGroupCommandHandler = requeueDocumentGroupCommandHandler;
        this.logger = logger;
    }

    public async Task Consume(ConsumeContext<PrintDocumentRequeueCommand> context)
    {
        logger.Information("PrintDocumentRequeueCommandConsumer received PrintDocumentRequeueCommand for Order {OrderId}", context.Message.OrderId);

        await requeueDocumentGroupCommandHandler.HandleAsync(new RequeueDocumentsForOrderCommand(context, context.Message.OrderId, context.Message.Customer));
    }
}
