﻿using MassTransit;
using Microsoft.Extensions.Options;
using Pondres.Omnia.Print.Business.Activities.Bundle;
using Pondres.Omnia.Print.Business.Extensions;
using Pondres.Omnia.Print.Business.Service;
using Pondres.Omnia.Print.Business.StateMachine.PrintBundle.Model;
using Pondres.Omnia.Print.Contracts.PrintBundle;
using Pondres.Omnia.Print.Integrations.Navision.Model.Common;
using System;
#pragma warning disable CS8602 // Dereference of a possibly null reference.
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

namespace Pondres.Omnia.Print.Business.StateMachine.PrintBundle;

public class PrintBundleStateMachine : MassTransitStateMachine<PrintBundleState>
{
    #region States

    public State Cancelled { get; set; }
    public State Skipped { get; set; }
    public State WaitingForBatch { get; set; }
    public State BatchCreationFailure { get; set; }
    public State Impositioning { get; set; }
    public State ImpositioningOnHold { get; set; }
    public State Printed { get; set; }
    public State WaitingForPrint { get; set; }
    public State WaitingForStart { get; set; }
    public State Created { get; set; }
    public State Started { get; set; }
    public State Completed { get; set; }
    public State WaitingForScan { get; set; }
    public State Scanned { get; set; }

    #endregion States

    #region Events

    public Schedule<PrintBundleState, PrintBundleCompletionTimeoutExpired> CompletionTimeout { get; set; }
    public Schedule<PrintBundleState, PrintBundleStartTimeoutExpired> StartTimeout { get; set; }
    public Event<PrintBundleCreateCommand> CreateCommandReceived { get; set; }
    public Event<PrintBundleAssignTaskCompleted> AssignToBatchCompleted { get; set; }
    public Event<PrintBundleAssignTaskFailure> AssignToBatchFailed { get; set; }
    public Event<PrintBundleImpositioningCompleted> ImpositioningCompleted { get; set; }
    public Event<PrintBundleImpositioningFailed> ImpositioningFailed { get; set; }
    public Event<PrintBundleManualContinueRequest> ManualContinueRequestReceived { get; set; }
    public Event<PrintBundleReleaseRequest> ReleaseRequestReceived { get; set; }
    public Event<PrintBundleCancelRequest> PrintBundleCancelRequestReceived { get; set; }
    public Event<PrintBundlePrintConfirmationRequest> PrintBundlePrintConfirmationRequestReceived { get; set; }
    public Event<PrintBundleStatusRequest> StatusRequested { get; set; }
    public Event<PrintBundleRemoveDocumentRequest> PrintBundleRemoveDocumentRequestReceived { get; set; }
    public Event<PrintBundleCompletedConfirmationRequest> PrintBundleCompletedConfirmationRequestReceived { get; set; }

    public Event<PrintBundleScannedConfirmationRequest> PrintBundleScannedConfirmationRequestReceived { get; set; }

    #endregion Events

    private readonly TimeSpan completionTimeoutDelay = TimeSpan.FromMinutes(15);

    private readonly TimeSpan startTimeoutDelay = TimeSpan.FromMinutes(3);

    public PrintBundleStateMachine()
    {
        SetupEvents();
        SetupRequests();
        SetupSchedules();

        InstanceState(x => x.CurrentState);

        Initially(
            When(CreateCommandReceived)
                .Then(x => x.Saga.PopulateStateFromCreateCommand(x.Message))
                .If(x => x.Saga.PlannedOn.HasValue, y => y
                    .Schedule(StartTimeout, context => new PrintBundleStartTimeoutExpired { PrintBundleId = context.Saga.CorrelationId })
                    .Schedule(CompletionTimeout, context => new PrintBundleCompletionTimeoutExpired { PrintBundleId = context.Saga.CorrelationId })
                )
                .TransitionTo(WaitingForStart)
                .Activity(x => x.OfInstanceType<PrintBundleUpdateStatusActivity>()));

        During(WaitingForStart,
            When(StartTimeout.Received)
                .Then(x => x.Saga.ReleasedOn = x.Saga.PlannedOn)
                .SelectDocumentsAndStartProcessing(this),

            When(PrintBundleRemoveDocumentRequestReceived)
                .Activity(x => x.OfType<PrintBundleRemovePrintDocumentActivity>()),

            When(ReleaseRequestReceived)
                .Activity(x => x.OfType<PrintBundleReleaseActivity>())
                .Respond(context => context.Saga.ToReleaseResponse())
                .IfElse(x => !x.Saga.IsPrimary,
                    x => x
                        .TransitionTo(Started)
                        .Activity(x => x.OfInstanceType<PrintBundleStartActivity>())
                        .Activity(x => x.OfType<PrintBundleCopyInformationPrimaryBundleActivity>())
                        .StartImpositioning(this),
                    x => x.SelectDocumentsAndStartProcessing(this)));

        During(BatchCreationFailure,
            When(ManualContinueRequestReceived)
                .Publish(context => context.Saga.ToManualContinueEvent(context.Message.Username))
                .TransitionTo(WaitingForBatch)
                .Publish(context => context.Saga.ToBundleBatchAssignRequest())
                .Activity(x => x.OfInstanceType<PrintBundleUpdateStatusActivity>())
                .Respond(context => context.Saga.ToPrintBundleManualContinueResponse()));

        During(WaitingForBatch,
            When(AssignToBatchCompleted)
                .Then(context => context.Saga.AddTaskAndGordNumber(context.Message))
                .Activity(x => x.OfInstanceType<PrintBundleSortDocumentsActivity>())
                .Activity(x => x.OfInstanceType<PrintBundleStartSecondaryBundlesActivity>())
                .StartImpositioning(this)
                .Activity(x => x.OfInstanceType<PrintBundleUpdateStatusActivity>()),

            When(AssignToBatchFailed)
                .TransitionTo(BatchCreationFailure)
                .Publish(context => context.Saga.ToPrintBundleSetOnHold())
                .Activity(x => x.OfInstanceType<PrintBundleUpdateStatusActivity>()));

        During(Impositioning,
            When(ImpositioningCompleted)
                .Unschedule(CompletionTimeout)
                .TransitionTo(WaitingForPrint)
                .Activity(x => x.OfInstanceType<PrintBundlePublishDocumentStateEventsActivity>())
                .Activity(x => x.OfInstanceType<PrintBundleUpdateStatusActivity>()),

            When(ImpositioningFailed)
                .TransitionTo(ImpositioningOnHold)
                .Activity(x => x.OfInstanceType<PrintBundleUpdateStatusActivity>())
                .Publish(context => context.Saga.ToPrintBundleSetOnHold()),

            When(ManualContinueRequestReceived)
                .If(x => x.Message.Force,
                    yes => yes
                    .Publish(context => context.Saga.ToManualContinueEvent(context.Message.Username))
                    .StartImpositioning(this)
                    .Respond(context => context.Saga.ToPrintBundleManualContinueResponse())));

        During(WaitingForPrint,

            Ignore(ReleaseRequestReceived),
            Ignore(ImpositioningFailed),

            When(PrintBundlePrintConfirmationRequestReceived)
                .Respond(context => context.Saga.ToPrintBundlePrintConfirmationResponse(context.Message.Username))
                .TransitionTo(Printed)
                .Publish(context => context.Saga.ToPrintConfirmedEvent(context.Message.Username))
                .Activity(x => x.OfInstanceType<PrintBundleUpdateStatusActivity>())
                .Activity(x => x.OfInstanceType<PrintBundlePublishDocumentStateEventsActivity>())
                .IfElse(IsDirectlyCompleted,
                    directlyCompleted =>
                        directlyCompleted.ConfirmAndCompleteBundle(this, data => data.Username),
                    notDirectlyCompleted =>
                        notDirectlyCompleted.TransitionTo(WaitingForScan)
                            .Activity(x => x.OfInstanceType<PrintBundleUpdateStatusActivity>())));

        During(WaitingForScan,

            Ignore(PrintBundlePrintConfirmationRequestReceived), // Sometimes people double click this

            When(PrintBundleScannedConfirmationRequestReceived)
                .TransitionTo(Scanned)
                .Activity(x => x.OfInstanceType<PrintBundleUpdateStatusActivity>())
                .Respond(context => context.Saga.ToPrintBundleScannedConfirmationResponse(context.Message.Username)));

        During(Scanned,

            Ignore(PrintBundleScannedConfirmationRequestReceived),

            When(PrintBundleCompletedConfirmationRequestReceived)
                .Respond(context => context.Saga.ToPrintBundleCompletedConfirmationResponse(context.Message.Username))
                .ConfirmAndCompleteBundle(this, data => data.Username));

        During(ImpositioningOnHold,
            When(ManualContinueRequestReceived)
                .Publish(context => context.Saga.ToManualContinueEvent(context.Message.Username))
                .StartImpositioning(this)
                .Respond(context => context.Saga.ToPrintBundleManualContinueResponse()),

            When(PrintBundleRemoveDocumentRequestReceived)
                .Activity(x => x.OfType<PrintBundleRemovePrintDocumentActivity>())
                .StartImpositioning(this));

        DuringAny(
            When(PrintBundleCancelRequestReceived)
                .If(context => context.Message.Reprint,
                    reprint => reprint.Publish(context => context.Saga.ToPrintBundleReprintRequested(context.Message.Username)))
                .TransitionTo(Cancelled)
                .Activity(x => x.OfInstanceType<PrintBundleUpdateStatusActivity>())
                .Publish(context => context.Saga.ToPrintBundleCompletedByCancellationMessage(context.Message.Username))
                .Publish(context => context.Saga.ToPrintBundleCancelled())
                .Respond(context => context.Saga.ToPrintBundleCancelResponse())
                .FinishBundle(),
            When(CompletionTimeout.Received)
                .Publish(context => context.Saga.ToPrintBundleDelayed()),
            When(StatusRequested)
                .Respond(context => context.Saga.ToPrintBundleStatusResponse()));

        SetCompletedWhenFinalized();
    }

    public static bool HasDocumentsToImposition<TMessage>(BehaviorContext<PrintBundleState, TMessage> context) where TMessage : class =>
        context.Saga.DocumentsTotalQuantity > 0;

    public static bool IsDirectlyCompleted(BehaviorContext<PrintBundleState, PrintBundlePrintConfirmationRequest> context)
    {
        return context.Saga.IsDirectlyCompleted;
    }

    private void SetupCancelRequest()
    {
        Event(() => PrintBundleCancelRequestReceived, x =>
        {
            x.CorrelateById(y => y.Message.PrintBundleId);

            x.OnMissingInstance(m => m.ExecuteAsync(async context =>
            {
                if (context.RequestId.HasValue)
                {
                    await context.RespondAsync<PrintBundleNotFoundResponse>(new { context.Message.PrintBundleId });
                }
            }));
        });
    }

    private void SetupEvents()
    {
        Event(() => CreateCommandReceived, x => x.CorrelateById(m => m.Message.PrintBundleId));
        Event(() => AssignToBatchCompleted, x => x.CorrelateById(m => m.Message.PrintBundleId));
        Event(() => AssignToBatchFailed, x => x.CorrelateById(m => m.Message.PrintBundleId));
        Event(() => ImpositioningCompleted, x => x.CorrelateById(m => m.Message.PrintBundleId));
        Event(() => ImpositioningFailed, x => x.CorrelateById(m => m.Message.PrintBundleId));
    }

    private void SetupReleaseRequest()
    {
        Event(() => ReleaseRequestReceived, x =>
        {
            x.CorrelateById(y => y.Message.PrintBundleId);

            x.OnMissingInstance(m => m.ExecuteAsync(async context =>
            {
                if (context.RequestId.HasValue)
                {
                    await context.RespondAsync<PrintBundleNotFoundResponse>(new { context.Message.PrintBundleId });
                }
            }));
        });
    }

    private void SetupManualContinueRequest()
    {
        Event(() => ManualContinueRequestReceived, x =>
        {
            x.CorrelateById(y => y.Message.PrintBundleId);

            x.OnMissingInstance(m => m.ExecuteAsync(async context =>
            {
                if (context.RequestId.HasValue)
                {
                    await context.RespondAsync<PrintBundleNotFoundResponse>(new { context.Message.PrintBundleId });
                }
            }));
        });
    }

    private void SetupPrintConfirmationRequest()
    {
        Event(() => PrintBundlePrintConfirmationRequestReceived, x =>
        {
            x.CorrelateById(y => y.Message.PrintBundleId);

            x.OnMissingInstance(m => m.ExecuteAsync(async context =>
            {
                if (context.RequestId.HasValue)
                {
                    await context.RespondAsync<PrintBundleNotFoundResponse>(new { context.Message.PrintBundleId });
                }
            }));
        });
    }

    private void SetupCompletedConfirmationRequest()
    {
        Event(() => PrintBundleCompletedConfirmationRequestReceived, x =>
        {
            x.CorrelateById(y => y.Message.PrintBundleId);

            x.OnMissingInstance(m => m.ExecuteAsync(async context =>
            {
                if (context.RequestId.HasValue)
                {
                    await context.RespondAsync<PrintBundleNotFoundResponse>(new { context.Message.PrintBundleId });
                }
            }));
        });
    }

    private void SetupScannedConfirmationRequest()
    {
        Event(() => PrintBundleScannedConfirmationRequestReceived, x =>
        {
            x.CorrelateById(y => y.Message.PrintBundleId);

            x.OnMissingInstance(m => m.ExecuteAsync(async context =>
            {
                if (context.RequestId.HasValue)
                {
                    await context.RespondAsync<PrintBundleNotFoundResponse>(new { context.Message.PrintBundleId });
                }
            }));
        });
    }

    private void SetupRequests()
    {
        SetupStatusRequest();
        SetupManualContinueRequest();
        SetupPrintConfirmationRequest();
        SetupCancelRequest();
        SetupRemoveDocumentRequest();
        SetupCompletedConfirmationRequest();
        SetupReleaseRequest();
        SetupScannedConfirmationRequest();
    }

    private void SetupSchedules()
    {
        Schedule(() => StartTimeout, instance => instance.StartTimeoutTokenId, x =>
        {
            x.DelayProvider = state =>
            {
                if (state.Saga.PlannedOn == null)
                    throw new InvalidOperationException("Bundle must have a schedule.");

                // get the time remaining until the scheduled message should fire
                var delay = state.Saga.PlannedOn.Value + startTimeoutDelay - DateTimeOffset.UtcNow;

                return delay;
            };
            x.Received = y => y.CorrelateById(context => context.Message.PrintBundleId);
        });

        Schedule(() => CompletionTimeout, instance => instance.CompletionTimeoutTokenId, x =>
        {
            x.DelayProvider = state =>
            {
                if (state.Saga.PlannedOn == null)
                    return TimeSpan.Zero;

                // get the time remaining until the scheduled message should fire
                var delay = state.Saga.PlannedOn.Value + completionTimeoutDelay - DateTimeOffset.UtcNow;

                return delay;
            };
            x.Received = y => y.CorrelateById(context => context.Message.PrintBundleId);
        });
    }

    private void SetupStatusRequest()
    {
        Event(() => StatusRequested, x =>
        {
            x.CorrelateById(y => y.Message.PrintBundleId);

            x.OnMissingInstance(m => m.ExecuteAsync(async context =>
            {
                if (context.RequestId.HasValue)
                {
                    await context.RespondAsync<PrintBundleNotFoundResponse>(new { context.Message.PrintBundleId });
                }
            }));
        });
    }

    private void SetupRemoveDocumentRequest()
    {
        Event(() => PrintBundleRemoveDocumentRequestReceived, x =>
        {
            x.CorrelateById(y => y.Message.PrintBundleId);

            x.OnMissingInstance(m => m.ExecuteAsync(async context =>
            {
                if (context.RequestId.HasValue)
                {
                    await context.RespondAsync<PrintBundleNotFoundResponse>(new { context.Message.PrintBundleId });
                }
            }));
        });
    }
}