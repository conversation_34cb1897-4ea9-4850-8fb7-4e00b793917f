﻿using Newtonsoft.Json;
using System;

namespace Pondres.Omnia.Print.Storage.Entities.PrintDocument;

public class OrderMetadataEntity
{
    [JsonProperty("customer")] public string Customer { get; set; } = string.Empty;

    [JsonProperty("customerReference")] public string CustomerReference { get; set; } = string.Empty;

    [JsonProperty("flow")] public string Flow { get; set; } = string.Empty;

    [JsonProperty("orderId")]
    public Guid OrderId { get; set; }

    [JsonProperty("orderCategoriesEntity")]
    public OrderCategoriesEntity Categories { get; set; } = new();
}