﻿using Pondres.Omnia.Print.Contracts.Api.Enums;
using Pondres.Omnia.Print.Storage.Entities.Mapping;
using Pondres.Omnia.Print.Storage.Entities.Print;
using Pondres.Omnia.Print.Storage.Repositories;
using Serilog;
using Solude.ApiBase.Contracts;
using Solude.StorageBase.Exceptions;
using System;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.PrintManager.DataSeed;

public class TestDataSeed(
    IPrintSheetMappingRepository printSheetMappingRepository,
    IDivisionMappingRepository divisionMappingRepository) : IDataSeed
{
    public async Task SeedAsync()
    {
        await UpsertPrintSheetMappingAsync();

        await SeedMappingDataAsync();
    }

    private async Task UpsertPrintSheetMappingAsync()
    {
        var dataSheet = new PrintSheetMappingEntity
        {
            SheetArticleCode = "FakeDataSheetCode",
            PrinterType = PrinterType.FC.ToString(),
            SheetFormat = "FakeFormat"
        };

        await printSheetMappingRepository.CreateOrUpdateAsync(dataSheet);
    }

    public async Task SeedMappingDataAsync()
    {
        var dataSheet = new DivisionMappingEntity
        {
            Id = "KLA0102",
            Division = "HAL"
        };

        try
        {
            await divisionMappingRepository.CreateAsync(dataSheet);
        }
        catch (EntityAlreadyExistsException)
        {
            Log.Information("division mapping {SeedId} - {SeedDivision} already exist in mapping container.", dataSheet.Id, dataSheet.Division);
        }
        catch (Exception ex)
        {
            Log.Error(ex, "error occurred while saving division mapping {SeedId} - {SeedDivision}.", dataSheet.Id, dataSheet.Division);
        }
    }
}