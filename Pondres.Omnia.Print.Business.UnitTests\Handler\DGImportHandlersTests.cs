﻿using Microsoft.Extensions.Options;
using Moq;
using Moq.AutoMock;
using Pondres.Omnia.Print.Business.Handler.Navision;
using Pondres.Omnia.Print.Business.Helper;
using Pondres.Omnia.Print.Integrations.Navision.Model;
using Pondres.Omnia.Print.Integrations.Navision.Service;
using Pondres.Omnia.Print.Storage.Entities.Print;
using Pondres.Omnia.Print.Storage.Entities.PrintBundle;
using Pondres.Omnia.Print.Storage.Entities.PrintBundleStatus;
using Pondres.Omnia.Print.Storage.Repositories;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Xunit;

namespace Pondres.Omnia.Print.Business.UnitTests.Handler;
public class DGImportHandlersTests
{
    private readonly AutoMocker mocker;

    private readonly Mock<IPrintBundleDetailsRepository> printBundleDetailsRepository;
    private readonly Mock<IPrintBundleStatusHistoryRepository> printBundleStatusHistoryRepository;
    private readonly Mock<INavisionIntegrationService> navisionIntegrationService;

    private readonly DGImportHandler handler;
    private readonly NavisionIntegrationOptions navisionOptions = new() { ExcludedCustomers = new string[] { "K00021" } };

    public DGImportHandlersTests()
    {
        mocker = new AutoMocker(MockBehavior.Strict);

        mocker.GetMock<IOptions<NavisionIntegrationOptions>>().Setup(x => x.Value).Returns(navisionOptions);

        handler = mocker.CreateInstance<DGImportHandler>();

        printBundleDetailsRepository = mocker.GetMock<IPrintBundleDetailsRepository>();
        printBundleStatusHistoryRepository = mocker.GetMock<IPrintBundleStatusHistoryRepository>();
        navisionIntegrationService = mocker.GetMock<INavisionIntegrationService>();
    }

    [Fact]
    public async Task LastExcludedLocationDGImportRun_Passed_ShouldSendToNavision()
    {
        // Arrange
        var lastRun = DateTimeOffset.Now.AddDays(-1);
        var now = DateTimeOffset.Now;

        var bundleStatusEntity = new PrintBundleStatusDetailsEntity
        {
            IsInFailedState = false,
            Message = "WaitingForPrint",
            Status = "WaitingForPrint",
            Timestamp = now.AddHours(-12),
            WaitingForInput = false
        };

        var bundlesSinceLastRun = new List<PrintBundleStatusHistoryEntity>
        {
            new PrintBundleStatusHistoryEntity
            {
                BundleId = Guid.NewGuid().ToString(),
                Details = bundleStatusEntity,
                CreatedOn = now.AddHours(-1)
            }
        };

        var bundle = new PrintBundleDetailsEntity
        {
            BatchName = PrintBatchHelper.CreateBatchName(now),
            Metadata = new PrintDocumentMetadataEntity
            {
                DGCode = "OMNIA_KLA0000",
                PrintMode = "Duplex",
                SheetArticleCode = "ART123",
                SheetPageCount = 1,
                EndLocation = "FULFILMENTSPECIAAL",
                DocumentFormat = "DOCUMENTFORMAT"
            },
            Quantities = new PrintBundleQuantitiesEntity
            {
                DocumentCount = 2,
                PageCount = 1,
                Quantity = 1
            },
            LastStatus = bundleStatusEntity,
            GordNumber = "",
            TaskNumber = 0
        };

        var sheetCount = SheetCountCalculationHelper.CalculateSheetCount(
                        bundle.Metadata.PrintMode,
                        bundle.Metadata.SheetPageCount,
                        bundle.Quantities.PageCount);

        printBundleStatusHistoryRepository
            .Setup(x => x.GetBundlesWithStatusWithinTimeRangeAsync(It.IsAny<DateTimeOffset>(), It.IsAny<DateTimeOffset>(), It.IsAny<string>()))
            .ReturnsAsync(bundlesSinceLastRun);

        printBundleDetailsRepository
            .Setup(x => x.GetBundlesWithExcludedLocationAsync(It.IsAny<IEnumerable<string>>(), It.IsAny<string[]>(), It.IsAny<IEnumerable<string>>()))
            .ReturnsAsync([bundle]);

        // Act
        await handler.GetExcludedLocationBundlesForTimeRangeAsync(lastRun, DateTimeOffset.Now);

        // Assert
        mocker.VerifyAll();
    }
}
