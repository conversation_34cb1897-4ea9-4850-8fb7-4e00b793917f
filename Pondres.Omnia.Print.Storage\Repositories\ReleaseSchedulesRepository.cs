﻿using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.Caching.Memory;
using Solude.StorageBase;
using Pondres.Omnia.Print.Storage.Configuration;
using Pondres.Omnia.Print.Storage.Entities.Schedule;
using System;

namespace Pondres.Omnia.Print.Storage.Repositories;

public class ReleaseSchedulesRepository : SinglePartitionBaseRepository<ReleaseSchedulesEntity>, IReleaseSchedulesRepository
{
    protected override bool CanWrite => true;
    protected override string PartitionKeyValue => CosmosConfiguration.ReleaseSchedulesPartitionValue;
    protected override MemoryCacheEntryOptions CreateCacheOptions() =>
        new()
        {
            SlidingExpiration = TimeSpan.FromMinutes(1)
        };

    public ReleaseSchedulesRepository(CosmosClient cosmosClient, IMemoryCache memoryCache) : 
        base(cosmosClient, CosmosConfiguration.PrintDatabaseName, CosmosConfiguration.SchedulesContainerName, memoryCache)
    { }

    protected override string GetCacheKey(string id, string partitionKeyValue) => $"SchedulesEntity_{id}_{partitionKeyValue}";
}
