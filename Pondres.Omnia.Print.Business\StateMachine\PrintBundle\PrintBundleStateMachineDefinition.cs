﻿using MassTransit;
using Pondres.Omnia.Print.Business.StateMachine.PrintBundle.Model;

namespace Pondres.Omnia.Print.Business.StateMachine.PrintBundle;

public class PrintBundleStateMachineDefinition : SagaDefinition<PrintBundleState>
{
    public PrintBundleStateMachineDefinition()
    {
    }

    protected override void ConfigureSaga(IReceiveEndpointConfigurator endpointConfigurator, ISagaConfigurator<PrintBundleState> sagaConfigurator, IRegistrationContext context)
    {
        endpointConfigurator.UseMessageRetry(r => r.Intervals(500, 5000, 10000));
        endpointConfigurator.UseInMemoryOutbox(context, configure => configure.ConcurrentMessageDelivery = true);
    }
}