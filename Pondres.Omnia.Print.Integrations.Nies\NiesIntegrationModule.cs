﻿using Microsoft.Extensions.DependencyInjection;
using Pondres.Omnia.Print.Common;
using Pondres.Omnia.Print.Integrations.Nies.Handler;
using Pondres.Omnia.Print.Integrations.Nies.Service;

namespace Pondres.Omnia.Print.Integrations.Nies;

public static class NiesIntegrationModule
{
    public static IServiceCollection AddNiesIntegrationModule(this IServiceCollection services, PrintAppSettings appSettings)
    {
        services.AddSingleton<IPrintBundleExportHandler, PrintBundleExportHandler>();
        services.AddTransient<INiesExportService>(s => new NiesExportService(appSettings.NiesEndpointAddress));
        services.AddTransient<IMappingService, MappingService>();

        return services;
    }

}
