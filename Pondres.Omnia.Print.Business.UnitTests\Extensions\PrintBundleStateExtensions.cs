﻿using Pondres.Omnia.Print.Business.StateMachine.PrintBundle.Model;
using Pondres.Omnia.Print.Contracts.PrintBundle;
using System.Collections.Generic;
using System.Linq;

namespace Pondres.Omnia.Print.Business.UnitTests.Extensions;

public static class PrintBundleStateExtensions
{
    public static void AddDocumentStatistics(this PrintBundleState state, List<PrintBundleQueuedDocument> documents)
    {
        state.DocumentsTotalQuantity = documents.Sum(x => x.DocumentMetadata.Quantity);
        state.DocumentsTotalPageCount = documents.Sum(x => x.DocumentMetadata.PageCount * x.DocumentMetadata.Quantity);
        state.DocumentsTotalRecordCount = documents.Count;
    }
}
