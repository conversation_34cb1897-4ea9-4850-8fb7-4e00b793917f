﻿using Newtonsoft.Json;
using Pondres.Omnia.Print.Business.Exceptions;
using Pondres.Omnia.Print.Business.Model;
using StackExchange.Redis;
using System;
using System.Text.Json;
using System.Threading.Tasks;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace Pondres.Omnia.Print.Business.Helper;

public class RedisCacheHelper : IRedisCacheHelper
{
    private readonly IDatabase redisCache;
    private readonly TimeSpan lockDuration = TimeSpan.FromSeconds(30);

    private readonly JsonSerializerOptions jsonSerializerOptions = new(JsonSerializerDefaults.Web);

    public RedisCacheHelper(IConnectionMultiplexer redisCache)
    {
        this.redisCache = redisCache.GetDatabase();
    }

    public async Task<bool> GetPrintBatchNotificationSentAsync(string batchName, string customer)
    {
        var key = GetPrintBatchNotificationSentCacheKey(batchName, customer);

        var value = await redisCache.StringGetAsync(key);

        if (!value.IsNull && value.HasValue)
        {
            var mailSent = JsonSerializer.Deserialize<bool>(value!, jsonSerializerOptions);
            return mailSent;
        }
        else
        {
            return false;
        }
    }

    public async Task SetPrintBatchNotificationSentAsync(string batchName, string customer, TimeSpan expiration)
    {
        var key = GetPrintBatchNotificationSentCacheKey(batchName, customer);
        var value = JsonSerializer.Serialize(true, jsonSerializerOptions);

        var setCacheValue = await redisCache.StringSetAsync(key, value, expiration);

        if (!setCacheValue)
        {
            throw new RedisCacheException(GetPrintBatchNotificationSentCacheKey(batchName, customer), value);
        }
    }

    public async Task<GordAndTaskNumber?> GetGordAndTaskNumberAsync(string DGCode)
    {
        var key = GetGordAndTaskNumberCacheKey(DGCode);

        var value = await redisCache.StringGetAsync(key);

        if (!value.IsNull && value.HasValue)
        {
            var gordAndTaskNumber = JsonSerializer.Deserialize<GordAndTaskNumber>(value!, jsonSerializerOptions);
            return gordAndTaskNumber;
        }
        else
        {
            return null;
        }
    }

    public async Task SetGordAndTaskNumberAsync(string DGCode, string gordNumber, int taskNumber, TimeSpan expiration)
    {
        var key = GetGordAndTaskNumberCacheKey(DGCode);
        var value = JsonSerializer.Serialize(new GordAndTaskNumber { GordNumber = gordNumber, TaskNumber = taskNumber }, jsonSerializerOptions);

        var setCacheValue = await redisCache.StringSetAsync(key, value, expiration);

        if (!setCacheValue)
        {
            throw new RedisCacheException(GetGordAndTaskNumberCacheKey(DGCode), value);
        }
    }

    public async Task<TValue?> GetAsync<TValue>(string key, TValue? defaultValue = null) where TValue : class
    {
        var cacheStringValue = await redisCache.StringGetAsync(key);

        return !cacheStringValue.IsNull && cacheStringValue.HasValue
            ? JsonSerializer.Deserialize<TValue>(cacheStringValue!, jsonSerializerOptions)
            : defaultValue;
    }

    public async Task<TValue?> GetOrSetAsync<TValue>(string key, TValue? defaultValue = null) where TValue : class
    {
        var cacheStringValue = await redisCache.StringGetAsync(key);

        if (!cacheStringValue.IsNull && cacheStringValue.HasValue)
        {
            return JsonSerializer.Deserialize<TValue>(cacheStringValue!, jsonSerializerOptions);
        }
        else
        {
            var value = JsonSerializer.Serialize(defaultValue, jsonSerializerOptions);
            await redisCache.StringSetAsync(key, value);
            return defaultValue;
        }
    }

    public async Task<RedisLock<TValue>> LockAndGetAsync<TValue>(string key, TValue defaultValue) where TValue : class, new()
    {
        var redisLock = new RedisLock<TValue>(redisCache, key, lockDuration);
        await redisLock.LockAsync();

        var cacheStringValue = await redisCache.StringGetAsync(key);

        redisLock.Value = !cacheStringValue.IsNull && cacheStringValue.HasValue
            ? JsonSerializer.Deserialize<TValue>(cacheStringValue!, jsonSerializerOptions) ?? throw new JsonSerializationException($"Failed to deserialize {nameof(TValue)}")
            : defaultValue;

        return redisLock;
    }

    public async Task SaveAsync<TValue>(RedisLock<TValue> redisLock) where TValue : class, new()
    {
        var value = JsonSerializer.Serialize(redisLock.Value, jsonSerializerOptions);

        if (redisLock.Expired)
            throw new RedisException("Lock was expired");

        await redisCache.StringSetAsync(redisLock.Key, value);
    }

    public async Task SaveAsync<TValue>(string key, TValue defaultValue) where TValue : class
    {
        var value = JsonSerializer.Serialize(defaultValue, jsonSerializerOptions);

        await redisCache.StringSetAsync(key, value);
    }

    private static string GetPrintBatchNotificationSentCacheKey(string batchName, string customer) =>
        $"PrintService_printBatch_notification_sent_for_{batchName}_{customer}";

    private static string GetGordAndTaskNumberCacheKey(string DGCode) =>
        $"PrintService_printBatch_gord_and_tasknumber_for_{DGCode}";

    public void Expire(string key, TimeSpan expiration) => redisCache.KeyExpire(key, expiration);
}
