﻿using Pondres.Omnia.Print.Integrations.Navision.Model.Common;
namespace Pondres.Omnia.Print.Integrations.Navision.Provider;

public class StaticNavisionDataProvider : INavisionDataProvider
{
    private readonly NavisionGordAndTaskNumberResponse response;

    public StaticNavisionDataProvider(string gordNumber, int? taskNumber)
    {
        response = new NavisionGordAndTaskNumberResponse
        {
            GORDNumber = gordNumber,
            TaskNumber = taskNumber
        };
    }

    public Task<NavisionGordAndTaskNumberResponse> GetGordAndTaskNumberAsync(string DGCode) =>
        Task.FromResult(response);
}
