﻿using Pondres.Omnia.Print.Contracts.Common;
using Pondres.Omnia.Print.Storage.Entities.PrintDocument;

namespace Pondres.Omnia.Print.Business.Extensions;

public static class OrderMetadataEntityExtensions
{
    public static OrderMetadata ToOrderMetadata(this OrderMetadataEntity entity) =>
        new()
        {
            Flow = entity.Flow,
            Customer = entity.Customer,
            CustomerReference = entity.CustomerReference,
            OrderId = entity.OrderId,
            Categories = new OrderCategories
            {
                One = entity.Categories.One,
                Two = entity.Categories.Two,
                Three = entity.Categories.Three
            }
        };
}