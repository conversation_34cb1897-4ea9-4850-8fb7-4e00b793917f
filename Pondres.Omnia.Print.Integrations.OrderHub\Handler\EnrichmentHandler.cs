﻿using MassTransit;
using Newtonsoft.Json;
using Pondres.Omnia.OrderHub.Contracts.OrderTask;
using Pondres.Omnia.Print.Business.Service;
using Serilog;
using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Integrations.OrderHub.Handler;

public abstract class EnrichmentHandler<TEnrichmentData> : IEnrichmentHandler where TEnrichmentData : class
{
    private readonly IDownloadService downloadService;

    protected EnrichmentHandler(IDownloadService downloadService)
    {
        this.downloadService = downloadService;
    }

    public async Task HandleEnrichmentRequestAsync(IPublishEndpoint publishEndpoint, OrderTaskBatchStartCommand message)
    {
        for (var itemIndex = 0; itemIndex < message.Input.Data.FileGroups.Count; itemIndex++)
        {
            var sourceFile = message.Input.Data.FileGroups[itemIndex].Items.Single(x => x.Type == OrderTaskBatchSourceDataType.Data);

            try
            {
                var messageDataJson = await DownloadMessageDataAsync(sourceFile.File.TemporaryUri);

                var enrichmentData = JsonConvert.DeserializeObject<TEnrichmentData>(messageDataJson) ?? throw new JsonException($"Failed to convert enrichment data to {typeof(TEnrichmentData).Name}");
                await EnrichDataAsync(enrichmentData);

                await PublishEnrichmentTaskSuccessfulAsync(
                    publishEndpoint,
                    message.Metadata.TaskId,
                    message.Metadata.BatchId,
                    sourceFile.File.FileName,
                    enrichmentData,
                    message.Input.Data.FileGroups.Count,
                    itemIndex);
            }
            catch (Exception exception)
            {
                await PublishEnrichmentTaskFailedAsync(
                    publishEndpoint,
                    message.Metadata.TaskId,
                    message.Metadata.BatchId,
                    exception,
                    message.Input.Data.FileGroups.Count,
                    itemIndex);
            }
        }
    }

    protected abstract Task EnrichDataAsync(TEnrichmentData enrichmentData);

    private async Task<string> DownloadMessageDataAsync(string jsonUri)
    {
        Log.Debug("Downloading message data");

        var requestUri = new Uri(jsonUri);
        return await downloadService.GetTextAsync(requestUri);
    }

    private static async Task PublishEnrichmentTaskFailedAsync(
        IPublishEndpoint publishEndpoint,
        Guid taskId,
        string batchId,
        Exception exception,
        int totalItemCount,
        int itemIndex)
    {
        Log.Debug("Publishing enrich failed message");

        await publishEndpoint.Publish(new OrderTaskBatchItemFailed
        {
            Message = exception.Message,
            BatchId = batchId,
            TaskId = taskId,
            TotalItemCount = totalItemCount,
            Index = itemIndex
        });
    }

    private static async Task PublishEnrichmentTaskSuccessfulAsync(
        IPublishEndpoint publishEndpoint,
        Guid taskId,
        string batchId,
        string fileName,
        TEnrichmentData enrichmentData,
        int totalItemCount,
        int itemIndex)
    {
        Log.Debug("Publishing enrich successful message");

        await publishEndpoint.Publish(new OrderTaskBatchItemCompleted
        {
            BatchId = batchId,
            TaskId = taskId,
            ItemData = new OrderTaskBatchItemData()
            {
                Data = enrichmentData,
                Extension = Path.GetExtension(fileName),
            },
            Message = "Enrichment successful",
            TotalItemCount = totalItemCount,
            Index = itemIndex
        });
    }
}