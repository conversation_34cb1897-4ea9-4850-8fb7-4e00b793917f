﻿using Newtonsoft.Json;
using Solude.StorageBase.Model;
using Pondres.Omnia.Print.Storage.Configuration;
using System.Collections.Generic;

namespace Pondres.Omnia.Print.Storage.Entities.Schedule;

public class ReleaseSchedulesEntity : CosmosEntity
{
    [JsonProperty("id")] public override string Id { get; set; } = string.Empty;
    [JsonProperty("type")] public string Type { get; set; } = string.Empty;
    [JsonProperty("schedule")]
    public List<PrintScheduleEntity> Schedule { get; set; } = [];
    [JsonProperty("active")]
    public bool Active { get; set; }
    public override string PartitionKeyValue => Type;
    public override string Version { get; set; } = CosmosConfiguration.ScheduleVersion;
}
