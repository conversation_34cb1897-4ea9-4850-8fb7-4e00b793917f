﻿using Solude.StorageBase;
using Pondres.Omnia.Print.Storage.Entities.PrintBundleStatus;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Storage.Repositories;

public interface IPrintBundleStatusHistoryRepository : IPartitionedBaseRepository<PrintBundleStatusHistoryEntity>
{
    Task<List<PrintBundleStatusHistoryEntity>> GetAllByBundleIdAsync(string bundleId);
    Task<List<PrintBundleStatusHistoryEntity>> GetBundlesWithStatusWithinTimeRangeAsync(DateTimeOffset timeRangeStart, DateTimeOffset timeRangeEnd, string status);
}
