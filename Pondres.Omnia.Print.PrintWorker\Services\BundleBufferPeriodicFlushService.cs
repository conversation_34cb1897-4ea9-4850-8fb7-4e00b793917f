﻿using Microsoft.Extensions.Hosting;
using System.Threading.Tasks;
using System.Threading;
using Microsoft.Extensions.Options;
using Pondres.Omnia.Print.Business.Handler.PrintBundleBuffer;

namespace Pondres.Omnia.Print.PrintWorker.Services;

public class BundleBufferPeriodicFlushService : BackgroundService
{
    private readonly PrintBundleBufferOptions bufferOptions;
    private readonly IPrintBundleBufferHandler bundleBufferHandler;

    public BundleBufferPeriodicFlushService(
        IOptions<PrintBundleBufferOptions> options,
        IPrintBundleBufferHandler bundleBufferHandler)
    {
        bufferOptions = options.Value;
        this.bundleBufferHandler = bundleBufferHandler;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        using var timer = new PeriodicTimer(bufferOptions.BufferFlushInterval);

        while (!stoppingToken.IsCancellationRequested)
        {
            // Wait for the next timer event
            await timer.WaitForNextTickAsync(stoppingToken);

            // Flush the buffer
            await bundleBufferHandler.FlushAllBuffersAsync();
        }
    }
}
