﻿using Pondres.Omnia.Print.Contracts.Api.Enums;
using Pondres.Omnia.Print.Contracts.Common;
using System;

namespace Pondres.Omnia.Print.Business.Extensions;

public static class PrintDocumentCreationMetadataInputExtensions
{
    public static PrintDocumentCreationMetadata ToMetadata(this PrintDocumentCreationMetadataInput metadataInput, DateTimeOffset mailDate) =>
        new()
        {
            Attachments = metadataInput.Attachments.ToMetadataAttachments(),
            SheetArticleCode = metadataInput.SheetArticleCode,
            SheetArticleDescription = metadataInput.SheetArticleDescription,
            Carrier = metadataInput.Carrier,
            CustomerDocumentReference = metadataInput.CustomerDocumentReference,
            DocumentStatus = metadataInput.DocumentStatus,
            DGCode = metadataInput.DGCode,
            DocumentFormat = metadataInput.DocumentFormat,
            EndLocation = metadataInput.EndLocation,
            Laminate = metadataInput.Laminate,
            MailDate = mailDate,
            Packaging = metadataInput.Packaging,
            PackingName = metadataInput.PackingName,
            PackingType = metadataInput.PackingType ?? PackingType.Overig,
            PageCount = metadataInput.PageCount,
            PostalDestination = metadataInput.PostalDestination,
            PrinterType = metadataInput.PrinterType,
            PrintMode = metadataInput.PrintMode,
            Quantity = metadataInput.Quantity,
            SheetPageCount = metadataInput.SheetPageCount,
            SheetFormat = metadataInput.SheetFormat,
            ReleaseSchedule = metadataInput.ReleaseSchedule,
            IsPrimary = metadataInput.IsPrimary,
            PrintBundleSort = metadataInput.PrintBundleSort
        };
}
