﻿using FluentAssertions.Execution;
using FluentAssertions.Primitives;
using FluentAssertions;
using MassTransit;
using System.Linq.Expressions;

namespace Pondres.Omnia.Print.IntegrationTests.Observers;
public class TestPublishObserver<TExpectedMessageType> : IPublishObserver where TExpectedMessageType : class
{
    public List<TExpectedMessageType> PublishedMessages { get; private set; } = [];
    public List<Exception> Exceptions { get; private set; } = [];

    public Task PostPublish<T>(PublishContext<T> context) where T : class
    {
        if (context.Message is TExpectedMessageType expectedMessage)
        {
            PublishedMessages.Add(expectedMessage);
        }

        return Task.CompletedTask;
    }

    public Task PrePublish<T>(PublishContext<T> context) where T : class
    {
        return Task.CompletedTask;
    }

    public Task PublishFault<T>(PublishContext<T> context, Exception exception) where T : class
    {
        Exceptions.Add(exception);
        return Task.CompletedTask;
    }

    public TestPublishObserverAssertions<TExpectedMessageType> Should()
    {
        return new TestPublishObserverAssertions<TExpectedMessageType>(this);
    }
}
public class TestPublishObserverAssertions<TMessage> :
    ReferenceTypeAssertions<TestPublishObserver<TMessage>, TestPublishObserverAssertions<TMessage>> where TMessage : class
{
    public TestPublishObserverAssertions(TestPublishObserver<TMessage> instance)
        : base(instance)
    {
    }

    protected override string Identifier => "publisher";

    public AndConstraint<TestPublishObserverAssertions<TMessage>> NotHaveSeenMessage(
        Expression<Func<TMessage, bool>> messageExpression, string because = "", params object[] becauseArgs)
    {
        var messageMatchFunc = messageExpression.Compile();

        Execute.Assertion
            .BecauseOf(because, becauseArgs)
            .ForCondition(Subject.PublishedMessages.SingleOrDefault(messageMatchFunc) == null)
            .FailWith("Expected message was not published where {0}{reason}, but the condition was not met by:"
                        + Environment.NewLine + Environment.NewLine + "{1}.",
                messageExpression, Subject);

        return new AndConstraint<TestPublishObserverAssertions<TMessage>>(this);
    }

    public AndConstraint<TestPublishObserverAssertions<TMessage>> HaveSeenMessage(
        Expression<Func<TMessage, bool>> messageExpression, string because = "", params object[] becauseArgs)
    {
        var messageMatchFunc = messageExpression.Compile();

        Execute.Assertion
            .BecauseOf(because, becauseArgs)
            .ForCondition(Subject.PublishedMessages.SingleOrDefault(messageMatchFunc) != null)
            .FailWith("Expected message was not published where {0}{reason}, but the condition was not met by:"
                        + Environment.NewLine + Environment.NewLine + "{1}.",
                messageExpression, Subject);

        return new AndConstraint<TestPublishObserverAssertions<TMessage>>(this);
    }
}

