﻿using Newtonsoft.Json;
using Pondres.Omnia.Print.Storage.Entities.PrintBundleStatus;
using System.Collections.Generic;

namespace Pondres.Omnia.Print.Storage.Entities.PrintBundle;

public class PrintBundleDetailsEntity : PrintBundleBasicEntity
{
    [JsonProperty("manualEvents")]
    public List<PrintBundleManualEventEntity> ManualEvents { get; set; } = [];

    [JsonProperty("lastStatus")] public PrintBundleStatusDetailsEntity LastStatus { get; set; } = new();

    [JsonProperty("printFilesStorageAccountName")]
    public string PrintFilesStorageAccountName { get; set; } = string.Empty;

    [JsonProperty("printFilesContainerName")]
    public string PrintFilesContainerName { get; set; } = string.Empty;
}