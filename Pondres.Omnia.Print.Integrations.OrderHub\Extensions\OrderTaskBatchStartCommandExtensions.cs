﻿using Pondres.Omnia.OrderHub.Contracts.OrderTask;
using Pondres.Omnia.Print.Common.Model;
using Pondres.Omnia.Print.Contracts.Common;
using Pondres.Omnia.Print.Contracts.PrintDocument;

namespace Pondres.Omnia.Print.Integrations.OrderHub.Extensions;

public static class OrderTaskBatchStartCommandExtensions
{
    public static PrintDocumentCreationModel ToPrintDocumentCreationModel(
        this OrderTaskBatchStartCommand command,
        OrderTaskBatchSourceDataFileGroupItem printFileInformation,
        OrderTaskBatchSourceDataFileGroupItem printMetadataInformation,
        int itemIndex)
    {
        var documentId = $"{command.Metadata.TaskId}_{command.Metadata.BatchId}_{itemIndex}";
        return new(
            documentId: documentId,
            primaryDocumentId: null, // We dont know the primary yet when we get the task, we will define it later
            orderMetadata: ToOrderMetadata(command),
            fileInformation: command.ToPrintDocumentFileInformation(printFileInformation, printMetadataInformation)
        );
    }

    private static OrderMetadata ToOrderMetadata(OrderTaskBatchStartCommand command) =>
        new()
        {
            Customer = command.Metadata.Customer,
            Flow = command.Metadata.Flow,
            CustomerReference = command.Metadata.CustomerReference,
            OrderId = command.Metadata.OrderId,
            Categories = command.ToOrderCategories()
        };

    private static OrderCategories ToOrderCategories(this OrderTaskBatchStartCommand command) =>
        new()
        {
            One = command.Metadata.OrderCategories.One,
            Two = command.Metadata.OrderCategories.Two,
            Three = command.Metadata.OrderCategories.Three
        };

    public static PrintDocumentFileInformation ToPrintDocumentFileInformation(
        this OrderTaskBatchStartCommand command,
        OrderTaskBatchSourceDataFileGroupItem printFileInformation,
        OrderTaskBatchSourceDataFileGroupItem printMetadataInformation) =>
        new()
        {
            MetaDataFilePath = printMetadataInformation.File.FilePath,
            PrintFilePath = printFileInformation.File.FilePath,
            StorageAccountName = command.Input.Data.DataStorageAccountName,
            StorageContainerName = command.Input.Data.DataContainerName
        };
}
