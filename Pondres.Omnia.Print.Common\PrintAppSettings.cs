﻿using Solude.Packages.Common;

namespace Pondres.Omnia.Print.Common;

public class PrintAppSettings : AppSettings
{
    public string OrderServiceAuthToken { get; set; } = string.Empty;
    public string TestCustomerStorageAccount { get; set; } = string.Empty;
    public bool CosmosDisableSSL { get; set; }
    public string AzuriteConnectionString { get; set; } = string.Empty;
    public string RedisConnectionString { get; set; } = string.Empty;
    public string FileStoragePath { get; set; } = string.Empty;
    public string NavisionXMLInfoLogicAppConnectionString { get; set; } = string.Empty;
    public bool NiesEnabled { get; set; }
    public string NiesEndpointAddress { get; set; } = string.Empty;
    public string PrintServiceAuthToken { get; set; } = string.Empty;
    public string ExcludedCustomers { get; set; } = string.Empty;
    public int ExcludedLocationPeriodicDGImportRunTime { get; set; }
    public string PrintFolderPath { get; set; } = string.Empty;
    public string ScalerMountFolderPrefixPath { get; set; } = string.Empty;
    public string CustomersExcludedFromDirectComplete { get; set; } = string.Empty;
}