replicaCount: 1

namespace: solude

image:
  repository: "#|ContainerRegistry|#.azurecr.io/omnia-print-printworker"
  pullPolicy: IfNotPresent
  tag: "#|Build.BuildNumber|#"

imagePullSecrets:
  - name: acr-pondres-secret

nameOverride: "print-worker" # name of application
fullnameOverride: "print-worker" # name of application

config:
  ASPNETCORE_ENVIRONMENT: "#|ASPNETCORE_ENVIRONMENT|#"
  RELEASE_NAME: "#|Release.ReleaseName|##|Release.AttemptNumber|#"
  VaultName: "kv-sol-print-#|OmniaEnvironment|#"
  TZ: "Europe/Amsterdam"
  FileStoragePath: "" # TODO
  ExcludedCustomers: "#|EXCLUDED_CUSTOMERS|#"
  ExcludedLocationPeriodicDGImportRunTime: "#|EXCLUDED_LOCATION_PERIODIC_DGIMPORT_RUNTIME|#"
  PrintFolderPath: "" # TODO
  CustomersExcludedFromDirectComplete: "#|CUSTOMERS_EXCLUDED_FROM_DIRECTCOMPLETE|#"

secrets:
  NavisionXMLInfoLogicAppConnectionString: "" # TODO

podAnnotations: {}

podLabels:
  azure.workload.identity/use: "true"

service:
  containerPort: 8080

serviceAccount:
  enabled: true
  name: "#|ServiceAccountName|#"

probes:
  initialDelaySeconds: 0
  periodSeconds: 10
  timeoutSeconds: 2
  successThreshold: 1
  failureThreshold: 5
  http:
    livenessPath: /health/live
    readinessPath: /health/ready
    scheme: HTTP

hostAliases: []
#  - ip: IP_ADDRESS_1
#    hostnames:
#      - HOST_NAME_1

resources:
  limits:
    cpu: 1000m
    memory: 768Mi
  requests:
    cpu: 250m
    memory: 768Mi

volumes:
  - name: tmp
    emptyDir: {}
#  - name: inspire-scaler-shared-storage # TODO
#    azureFile:
#      secretName: quadient-shared-storage-secret
#      shareName: scaler-shared-storage
#      readOnly: false

volumeMounts:
  - name: tmp
    mountPath: /tmp
#  - name: inspire-scaler-shared-storage # TODO
#    mountPath: "/mnt/scaler"

nodeSelector: {}

tolerations: []

affinity: {}
