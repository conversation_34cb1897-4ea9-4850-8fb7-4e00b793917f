﻿using System;

namespace Pondres.Omnia.Print.Business.Extensions;

public static class StringExtensions
{
    public static bool IsOneOfEnumValues<TEnum>(this string stringValue, params TEnum[] enumValue) where TEnum : struct
        => Array.Exists(enumValue, x => x.ToString() == stringValue);

    public static bool IsEnumValue<TEnum>(this string stringValue, TEnum enumValue) where TEnum : struct
        => stringValue == enumValue.ToString();

    public static TEnum ToEnumOrDefault<TEnum>(this string enumName, TEnum defaultValue) where TEnum : struct
        => string.IsNullOrEmpty(enumName) ? defaultValue : Enum.Parse<TEnum>(enumName, true);

    public static TEnum ToEnumOrDefaultWhenNoMatch<TEnum>(this string enumName, TEnum defaultValue) where TEnum : struct
    {
        try
        {
            return Enum.Parse<TEnum>(enumName, true);
        }
        catch (Exception)
        {
            return defaultValue;
        }
    }
}
