﻿using MassTransit;
using Pondres.Omnia.Print.Business.Service;
using Pondres.Omnia.Print.Business.StateMachine.PrintBundle.Model;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Activities.Bundle;

public class PrintBundleExpireDocumentsActivity : BaseInstanceActivity<PrintBundleState>
{
    private readonly IPrintBundleDocumentStorageService bundleDocumentStorageService;

    public PrintBundleExpireDocumentsActivity(IPrintBundleDocumentStorageService bundleDocumentStorageService)
    {
        this.bundleDocumentStorageService = bundleDocumentStorageService;
    }

    public override string ActivityName => "print-bundle-select-documents";

    protected override Task ExecuteAsync(BehaviorContext<PrintBundleState> context)
    {
        bundleDocumentStorageService.ExpireDocumentsForBundle(context.Saga.CorrelationId);

        return Task.CompletedTask;
    }
}