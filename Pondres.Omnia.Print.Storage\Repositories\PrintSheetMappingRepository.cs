﻿using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.Caching.Memory;
using Newtonsoft.Json;
using Solude.StorageBase.Exceptions;
using Solude.StorageBase.Model;
using Pondres.Omnia.Print.Storage.Configuration;
using Pondres.Omnia.Print.Storage.Entities.Print;
using System;
using System.Net;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Storage.Repositories;

public class PrintSheetMappingRepository : IPrintSheetMappingRepository
{
    private readonly Container container;
    private readonly IMemoryCache memoryCache;

    private readonly string partitionKey = "sheetmapping";

    public PrintSheetMappingRepository(CosmosClient cosmosClient, IMemoryCache memoryCache)
    {
        container = cosmosClient.GetContainer(
            CosmosConfiguration.PrintDatabaseName,
            CosmosConfiguration.PrintDataSheetContainerName);

        this.memoryCache = memoryCache;
    }

    public async Task CreateOrUpdateAsync(PrintSheetMappingEntity dataSheet)
    {
        try
        {
            _ = await container.ReadItemAsync<PrintSheetMappingEntity>(dataSheet.SheetArticleCode, new PartitionKey(partitionKey));

            var response = await container.ReplaceItemAsync(dataSheet, dataSheet.SheetArticleCode, new PartitionKey(partitionKey));

            var entity = new CosmosResponse<PrintSheetMappingEntity>(response.ETag, response.Resource);

            UpdateCache(entity);
        }
        catch (CosmosException ex) when (ex.StatusCode == HttpStatusCode.NotFound)
        {
            await container.CreateItemAsync(dataSheet, new PartitionKey(partitionKey));
        }
    }

    public async Task<CosmosResponse<PrintSheetMappingEntity>> GetSingleCachedAsync(string sheetArticleCode)
    {
        return memoryCache.TryGetValue(GetCacheKey(sheetArticleCode), out string? printDataSheetJson)
            ? JsonConvert.DeserializeObject<CosmosResponse<PrintSheetMappingEntity>>(printDataSheetJson!)!
            : await GetEntityFromDatabaseAsync(sheetArticleCode);
    }

    private static string GetCacheKey(string sheetArticleCode) => $"print_data_sheet_{sheetArticleCode}";

    private async Task<CosmosResponse<PrintSheetMappingEntity>> GetEntityFromDatabaseAsync(string sheetArticleCode)
    {
        try
        {
            // Read the item to see if it exists.
            var response = await container.ReadItemAsync<PrintSheetMappingEntity>(sheetArticleCode, new PartitionKey(partitionKey));

            var entity = new CosmosResponse<PrintSheetMappingEntity>(response.ETag, response.Resource);

            UpdateCache(entity);

            return entity;
        }
        catch (CosmosException ex) when (ex.StatusCode == HttpStatusCode.NotFound)
        {
            throw new EntityNotFoundException(typeof(PrintSheetMappingEntity), sheetArticleCode, string.Empty, ex);
        }
    }

    private void UpdateCache(CosmosResponse<PrintSheetMappingEntity> printDataSheet)
    {
        memoryCache.Set(
            GetCacheKey(printDataSheet.Resource.SheetArticleCode),
            JsonConvert.SerializeObject(printDataSheet),
            new MemoryCacheEntryOptions
            {
                AbsoluteExpiration = DateTimeOffset.Now + TimeSpan.FromSeconds(30)
            });
    }
}