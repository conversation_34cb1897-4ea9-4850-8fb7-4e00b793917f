﻿using System.Collections.Generic;

namespace Pondres.Omnia.Print.Business.Model;

public record PrintDocumentGroup(PrintDocumentCreationPair Primary, List<PrintDocumentCreationPair> Secondaries)
{
    public List<PrintDocumentCreationPair> All
    {
        get
        {
            var all = new List<PrintDocumentCreationPair>() { Primary };
            all.AddRange(Secondaries);
            return all;
        }
    }
};
