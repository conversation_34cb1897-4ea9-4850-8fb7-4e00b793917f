﻿using Pondres.Omnia.Print.Business.Model;
using System;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Helper;

public interface IRedisCacheHelper
{
    Task<bool> GetPrintBatchNotificationSentAsync(string batchName, string customer);
    Task SetPrintBatchNotificationSentAsync(string batchName, string customer, TimeSpan expiration);
    Task<GordAndTaskNumber?> GetGordAndTaskNumberAsync(string DGCode);
    Task SetGordAndTaskNumberAsync(string DGCode, string gordNumber, int taskNumber, TimeSpan expiration);
    Task<RedisLock<TValue>> LockAndGetAsync<TValue>(string key, TValue defaultValue) where TValue : class, new();
    void Expire(string key, TimeSpan expiration);
    Task SaveAsync<TValue>(RedisLock<TValue> redisLock) where TValue : class, new();
    Task SaveAsync<TValue>(string key, TValue defaultValue) where TValue : class;
    Task<TValue?> GetAsync<TValue>(string key, TValue? defaultValue = null) where TValue : class;
    Task<TValue?> GetOrSetAsync<TValue>(string key, TValue? defaultValue = null) where TValue : class;
}
