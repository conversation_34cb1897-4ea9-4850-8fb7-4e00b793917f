﻿using MassTransit;
using Pondres.Omnia.OrderHub.Contracts.OrderTask;
using Pondres.Omnia.Print.Business.CommandHandler;
using Pondres.Omnia.Print.Common.Model;
using Pondres.Omnia.Print.Integrations.OrderHub.Extensions;
using Serilog;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Integrations.OrderHub.Consumer;

public class OrderTaskPrintDocumentSubmissionConsumer : IConsumer<OrderTaskBatchStartCommand>
{
    private readonly QueueNewDocumentGroupCommandHandler queueNewDocumentGroupCommandHandler;
    private readonly ILogger logger;

    public OrderTaskPrintDocumentSubmissionConsumer(QueueNewDocumentGroupCommandHandler queueNewDocumentGroupCommandHandler, ILogger logger)
    {
        this.queueNewDocumentGroupCommandHandler = queueNewDocumentGroupCommandHandler;
        this.logger = logger;
    }

    public async Task Consume(ConsumeContext<OrderTaskBatchStartCommand> context)
    {
        logger.Information("OrderTaskPrintDocumentSubmissionConsumer received OrderTaskBatchStartCommand message for Order {OrderId}", context.Message.Metadata.OrderId);

        var totalSourceFileGroupCount = context.Message.Input.Data.FileGroups.Count;
        for (var itemIndex = 0; itemIndex < totalSourceFileGroupCount; itemIndex++)
        {
            try
            {
                var sourceFileGroup = context.Message.Input.Data.FileGroups[itemIndex].Items;
                var printFileInformation = sourceFileGroup.Single(x => x.Type == OrderTaskBatchSourceDataType.DocumentFile);
                var printMetadataInformation = sourceFileGroup.Single(x => x.Type == OrderTaskBatchSourceDataType.DocumentMetadata);

                var creationModel = context.Message.ToPrintDocumentCreationModel(printFileInformation, printMetadataInformation, itemIndex);
                creationModel.PrimaryDocumentId = creationModel.DocumentId;

                var command = new QueueNewDocumentGroupCommand(context, [creationModel]);
                await queueNewDocumentGroupCommandHandler.HandleAsync(command);

                await PublishTaskSuccessfulAsync(context, context.Message.Metadata.TaskId, context.Message.Metadata.BatchId, creationModel, totalSourceFileGroupCount, itemIndex);
            }
            catch (Exception exception)
            {
                await PublishTaskFailedAsync(context, context.Message.Metadata.TaskId, context.Message.Metadata.BatchId, exception, totalSourceFileGroupCount, itemIndex);
            }
        }
    }

    private static async Task PublishTaskFailedAsync(
        IPublishEndpoint publishEndpoint,
        Guid taskId,
        string batchId,
        Exception exception,
        int totalItemCount,
        int itemIndex)
    {
        Log.Debug("Publishing OrderTaskBatchItemFailed message");

        await publishEndpoint.Publish(new OrderTaskBatchItemFailed
        {
            Message = exception.Message,
            BatchId = batchId,
            TaskId = taskId,
            TotalItemCount = totalItemCount,
            Index = itemIndex
        });
    }

    private static async Task PublishTaskSuccessfulAsync(
        IPublishEndpoint publishEndpoint,
        Guid taskId,
        string batchId,
        PrintDocumentCreationModel printCreateCommand,
        int totalItemCount,
        int itemIndex)
    {
        Log.Debug("Publishing OrderTaskBatchItemCompleted message");

        await publishEndpoint.Publish(new OrderTaskBatchItemCompleted
        {
            BatchId = batchId,
            TaskId = taskId,
            ItemData = new OrderTaskBatchItemData()
            {
                Data = printCreateCommand,
                Extension = "json"
            },
            Message = "Print order submitted",
            TotalItemCount = totalItemCount,
            Index = itemIndex
        }
        );
    }
}
