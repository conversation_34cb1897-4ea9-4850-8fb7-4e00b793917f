﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Pondres.Omnia.Print.PrintWorker", "Pondres.Omnia.Print.PrintWorker\Pondres.Omnia.Print.PrintWorker.csproj", "{EFA82745-9299-44A6-8BF0-5CAE4FCEF3DB}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Pondres.Omnia.Print.PrintManager", "Pondres.Omnia.Print.PrintManager\Pondres.Omnia.Print.PrintManager.csproj", "{283A6C0A-7739-4EED-95BE-C26F517B13D9}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Pondres.Omnia.Print.Business", "Pondres.Omnia.Print.Business\Pondres.Omnia.Print.Business.csproj", "{834AB96D-B7B0-437D-84F5-8761E987F5AE}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Pondres.Omnia.Print.Contracts", "Pondres.Omnia.Print.Contracts\Pondres.Omnia.Print.Contracts.csproj", "{9E827044-AE88-4060-8FED-94FE6D0A39BA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Pondres.Omnia.Print.Storage", "Pondres.Omnia.Print.Storage\Pondres.Omnia.Print.Storage.csproj", "{17750FFF-5690-4426-A677-A8DFDDD91A89}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Pondres.Omnia.Print.Common", "Pondres.Omnia.Print.Common\Pondres.Omnia.Print.Common.csproj", "{37FCA90D-8384-4872-9D26-9E7B2F57CBBA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Pondres.Omnia.Print.Business.UnitTests", "Pondres.Omnia.Print.Business.UnitTests\Pondres.Omnia.Print.Business.UnitTests.csproj", "{6FEF22BB-4FD1-45E4-9C82-0F19C5B858E1}"
EndProject
Project("{E53339B2-1760-4266-BCC7-CA923CBCF16C}") = "docker-compose", "docker-compose.dcproj", "{A546B52D-21B6-43FB-A744-B55275425086}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Pondres.Omnia.Print.Integrations.OrderHub", "Pondres.Omnia.Print.Integrations.OrderHub\Pondres.Omnia.Print.Integrations.OrderHub.csproj", "{D760702C-11AE-4DE1-9EB6-CD7BA6011055}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Integrations", "Integrations", "{4CB894C9-E3D1-4A3F-BC34-9B9A43AECD7D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Pondres.Omnia.Print.Integrations.OrderHub.UnitTests", "Pondres.Omnia.Print.Integrations.OrderHub.UnitTests\Pondres.Omnia.Print.Integrations.OrderHub.UnitTests.csproj", "{6775D734-F129-4864-83CF-D2AF7A6263BE}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Pondres.Omnia.Print.Integrations.Warehouse", "Pondres.Omnia.Print.Integrations.Warehouse\Pondres.Omnia.Print.Integrations.Warehouse.csproj", "{D3F10AB2-514E-4264-8905-87CDB678CD1C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Pondres.Omnia.Print.Integrations.Nies", "Pondres.Omnia.Print.Integrations.Nies\Pondres.Omnia.Print.Integrations.Nies.csproj", "{3FFED7D8-4402-4DCC-90A3-440DF6197F1C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Pondres.Omnia.Print.Integrations.Navision", "Pondres.Omnia.Print.Integrations.Navision\Pondres.Omnia.Print.Integrations.Navision.csproj", "{E3C58B9A-16E1-46A8-9C45-74896976F008}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{C749D1B3-B74F-4CDA-A650-5A26885CFD2B}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Pondres.Omnia.Print.IntegrationTests", "Pondres.Omnia.Print.IntegrationTests\Pondres.Omnia.Print.IntegrationTests.csproj", "{3ED5BCE9-110E-4D6D-8E2F-024C1F21BAFF}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{EFA82745-9299-44A6-8BF0-5CAE4FCEF3DB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EFA82745-9299-44A6-8BF0-5CAE4FCEF3DB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EFA82745-9299-44A6-8BF0-5CAE4FCEF3DB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EFA82745-9299-44A6-8BF0-5CAE4FCEF3DB}.Release|Any CPU.Build.0 = Release|Any CPU
		{283A6C0A-7739-4EED-95BE-C26F517B13D9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{283A6C0A-7739-4EED-95BE-C26F517B13D9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{283A6C0A-7739-4EED-95BE-C26F517B13D9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{283A6C0A-7739-4EED-95BE-C26F517B13D9}.Release|Any CPU.Build.0 = Release|Any CPU
		{834AB96D-B7B0-437D-84F5-8761E987F5AE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{834AB96D-B7B0-437D-84F5-8761E987F5AE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{834AB96D-B7B0-437D-84F5-8761E987F5AE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{834AB96D-B7B0-437D-84F5-8761E987F5AE}.Release|Any CPU.Build.0 = Release|Any CPU
		{9E827044-AE88-4060-8FED-94FE6D0A39BA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9E827044-AE88-4060-8FED-94FE6D0A39BA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9E827044-AE88-4060-8FED-94FE6D0A39BA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9E827044-AE88-4060-8FED-94FE6D0A39BA}.Release|Any CPU.Build.0 = Release|Any CPU
		{17750FFF-5690-4426-A677-A8DFDDD91A89}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{17750FFF-5690-4426-A677-A8DFDDD91A89}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{17750FFF-5690-4426-A677-A8DFDDD91A89}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{17750FFF-5690-4426-A677-A8DFDDD91A89}.Release|Any CPU.Build.0 = Release|Any CPU
		{37FCA90D-8384-4872-9D26-9E7B2F57CBBA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{37FCA90D-8384-4872-9D26-9E7B2F57CBBA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{37FCA90D-8384-4872-9D26-9E7B2F57CBBA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{37FCA90D-8384-4872-9D26-9E7B2F57CBBA}.Release|Any CPU.Build.0 = Release|Any CPU
		{6FEF22BB-4FD1-45E4-9C82-0F19C5B858E1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6FEF22BB-4FD1-45E4-9C82-0F19C5B858E1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6FEF22BB-4FD1-45E4-9C82-0F19C5B858E1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6FEF22BB-4FD1-45E4-9C82-0F19C5B858E1}.Release|Any CPU.Build.0 = Release|Any CPU
		{A546B52D-21B6-43FB-A744-B55275425086}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A546B52D-21B6-43FB-A744-B55275425086}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A546B52D-21B6-43FB-A744-B55275425086}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A546B52D-21B6-43FB-A744-B55275425086}.Release|Any CPU.Build.0 = Release|Any CPU
		{D760702C-11AE-4DE1-9EB6-CD7BA6011055}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D760702C-11AE-4DE1-9EB6-CD7BA6011055}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D760702C-11AE-4DE1-9EB6-CD7BA6011055}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D760702C-11AE-4DE1-9EB6-CD7BA6011055}.Release|Any CPU.Build.0 = Release|Any CPU
		{6775D734-F129-4864-83CF-D2AF7A6263BE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6775D734-F129-4864-83CF-D2AF7A6263BE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6775D734-F129-4864-83CF-D2AF7A6263BE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6775D734-F129-4864-83CF-D2AF7A6263BE}.Release|Any CPU.Build.0 = Release|Any CPU
		{D3F10AB2-514E-4264-8905-87CDB678CD1C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D3F10AB2-514E-4264-8905-87CDB678CD1C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D3F10AB2-514E-4264-8905-87CDB678CD1C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D3F10AB2-514E-4264-8905-87CDB678CD1C}.Release|Any CPU.Build.0 = Release|Any CPU
		{3FFED7D8-4402-4DCC-90A3-440DF6197F1C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3FFED7D8-4402-4DCC-90A3-440DF6197F1C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3FFED7D8-4402-4DCC-90A3-440DF6197F1C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3FFED7D8-4402-4DCC-90A3-440DF6197F1C}.Release|Any CPU.Build.0 = Release|Any CPU
		{E3C58B9A-16E1-46A8-9C45-74896976F008}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E3C58B9A-16E1-46A8-9C45-74896976F008}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E3C58B9A-16E1-46A8-9C45-74896976F008}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E3C58B9A-16E1-46A8-9C45-74896976F008}.Release|Any CPU.Build.0 = Release|Any CPU
		{3ED5BCE9-110E-4D6D-8E2F-024C1F21BAFF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3ED5BCE9-110E-4D6D-8E2F-024C1F21BAFF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3ED5BCE9-110E-4D6D-8E2F-024C1F21BAFF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3ED5BCE9-110E-4D6D-8E2F-024C1F21BAFF}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{D760702C-11AE-4DE1-9EB6-CD7BA6011055} = {4CB894C9-E3D1-4A3F-BC34-9B9A43AECD7D}
		{6775D734-F129-4864-83CF-D2AF7A6263BE} = {4CB894C9-E3D1-4A3F-BC34-9B9A43AECD7D}
		{D3F10AB2-514E-4264-8905-87CDB678CD1C} = {4CB894C9-E3D1-4A3F-BC34-9B9A43AECD7D}
		{3FFED7D8-4402-4DCC-90A3-440DF6197F1C} = {4CB894C9-E3D1-4A3F-BC34-9B9A43AECD7D}
		{E3C58B9A-16E1-46A8-9C45-74896976F008} = {4CB894C9-E3D1-4A3F-BC34-9B9A43AECD7D}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {B843D0EB-DDF0-4645-9CE0-0031AFE55463}
	EndGlobalSection
EndGlobal
