﻿using MassTransit;
using Pondres.Omnia.Print.Business.Extensions;
using Pondres.Omnia.Print.Business.Service;
using Pondres.Omnia.Print.Contracts.PrintBundle;
using Pondres.Omnia.Print.Storage.DataStorage;
using Pondres.Omnia.Print.Storage.DataStorage.Model;
using Pondres.Omnia.Print.Storage.Repositories;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Handler;

public class PrintBundleImpositioningHandler : IPrintBundleImpositioningHandler
{
    private readonly IDataStorageRepositoryFactory dataStorageRepositoryFactory;
    private readonly IPrintBundleDocumentStorageService bundleDocumentStorageService;
    private readonly IPrintBundleDetailsRepository printBundleRepository;

    // Longer than ideal. Possible BIG BATCHES and scaler times out.
    private readonly TimeSpan tempUriExpireTimeSpan = TimeSpan.FromHours(3);

    public PrintBundleImpositioningHandler(
        IPrintBundleDetailsRepository printBundleRepository,
        IDataStorageRepositoryFactory dataStorageRepositoryFactory,
        IPrintBundleDocumentStorageService bundleDocumentStorageService)
    {
        this.printBundleRepository = printBundleRepository;
        this.dataStorageRepositoryFactory = dataStorageRepositoryFactory;
        this.bundleDocumentStorageService = bundleDocumentStorageService;
    }

    public async Task SendBundleToImpositioningAsync(
        ISendEndpoint sendEndpoint,
        string customer,
        Guid printBundleId,
        string? gordNumber,
        int taskNumber)
    {
        var printBundleEntity = await printBundleRepository.GetSingleAsync(printBundleId.ToString());

        var storageContainer = await dataStorageRepositoryFactory.GetStorageContainerAsync(
            printBundleEntity.Resource.PrintFilesStorageAccountName,
            printBundleEntity.Resource.PrintFilesContainerName);

        var documents = await bundleDocumentStorageService.GetDocumentsForBundleAsync(printBundleId);

        var batchFileUri = await CreateAndGetBatchFileAsync(storageContainer, printBundleId, documents);

        var containerUri = await storageContainer.GetSharedAccessSignatureUrlForContainerAsync(tempUriExpireTimeSpan);

        var startImpositioningCommand = new PrintBundleStartImpositioningCommand
        {
            BatchFileUri = batchFileUri.DataUri,
            BatchFileUriExpiresOn = batchFileUri.ExpiresOn,
            FileContainerUri = containerUri.DataUri,
            FileContainerUriExpiresOn = containerUri.ExpiresOn,
            PrintBundleId = printBundleId,
            BatchName = printBundleEntity.Resource.BatchName,
            Customer = customer,
            GordNumber = gordNumber,
            TaskNumber = taskNumber,
            PrintMetadata = printBundleEntity.Resource.Metadata.ToPrintDocumentCreationMetadata()
        };

        await sendEndpoint.Send(startImpositioningCommand);
    }

    private static string BuildFileList(HashSet<PrintBundleQueuedDocument> documents)
    {
        var stringBuilder = new StringBuilder();
        foreach (var document in documents)
        {
            stringBuilder.AppendLine($"{document.PrintFilePath};{document.DocumentMetadata.Quantity};{document.SequenceId};{document.DocumentMetadata.CustomerDocumentReference}");
        }

        return stringBuilder.ToString();
    }

    private static string CreateFileNameForBundle(Guid printBundleId) => $"printbundles/{printBundleId}";

    private async Task<TemporaryDataUri> CreateAndGetBatchFileAsync(
        IDataStorageContainerRepository storageContainer,
        Guid printBundleId,
        HashSet<PrintBundleQueuedDocument> documents)
    {
        var fileListString = BuildFileList(documents);

        var bundleFileBatchName = CreateFileNameForBundle(printBundleId);

        await storageContainer.UploadTextAsync(fileListString, bundleFileBatchName, overwrite: true);

        var temporaryUri = await storageContainer.GetSharedAccessSignatureUrlForBlobAsync(bundleFileBatchName, tempUriExpireTimeSpan);

        return temporaryUri;
    }
}