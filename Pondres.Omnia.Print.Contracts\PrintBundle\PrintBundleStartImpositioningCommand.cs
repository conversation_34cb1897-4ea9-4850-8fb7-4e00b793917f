﻿using Pondres.Omnia.Print.Contracts.Common;
using System;

namespace Pondres.Omnia.Print.Contracts.PrintBundle
{
    public class PrintBundleStartImpositioningCommand
    {
        public string BatchFileUri { get; set; } = string.Empty;
        public DateTimeOffset BatchFileUriExpiresOn { get; set; }
        public string BatchName { get; set; } = string.Empty;
        public string Customer { get; set; } = string.Empty;
        public string FileContainerUri { get; set; } = string.Empty;
        public DateTimeOffset FileContainerUriExpiresOn { get; set; }
        public Guid PrintBundleId { get; set; }
        public string? GordNumber { get; set; }
        public int TaskNumber { get; set; }
        public PrintDocumentCreationMetadata PrintMetadata { get; set; } = new();
    }
}