{"SwaggerEnabled": true, "FileStoragePath": true, "NavisionXMLInfoLogicAppConnectionString": "", "NiesEnabled": false, "NiesEndpointAddress": "", "PrintServiceAuthToken": "dev", "TestCustomerStorageAccount": "", "ExcludedCustomers": "KLA00022, KLA00023", "MessageBus": "rabbitmq", "RabbitConnectionString": "amqp://guest:guest@localhost", "CosmosDbEndpoint": "AccountEndpoint=https://localhost:8081/;AccountKey=C2y6yDjf5/R+ob0N8A7Cgv30VRDJIWEHLM+4QDU5DE2nQ9nDuVTqobD4b8mGGyPMbIZnqyMsEcaGQy67XIw/Jw==", "RedisConnectionString": "localhost", "AppInsightsConnectionString": "<dev>", "CustomersExcludedFromDirectComplete": "K00021", "ScalerMountFolderPrefixPath": "tempfiles"}