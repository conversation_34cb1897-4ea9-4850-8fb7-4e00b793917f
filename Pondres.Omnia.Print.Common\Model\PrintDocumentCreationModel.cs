﻿using Pondres.Omnia.Print.Contracts.Common;
using Pondres.Omnia.Print.Contracts.PrintDocument;

namespace Pondres.Omnia.Print.Common.Model;

public class PrintDocumentCreationModel
{
    public PrintDocumentCreationModel(string documentId, string? primaryDocumentId, PrintDocumentFileInformation fileInformation, OrderMetadata orderMetadata)
    {
        DocumentId = documentId;
        PrimaryDocumentId = primaryDocumentId;
        FileInformation = fileInformation;
        OrderMetadata = orderMetadata;
    }

    public string DocumentId { get; set; }
    public string? PrimaryDocumentId { get; set; }
    public PrintDocumentFileInformation FileInformation { get; set; }
    public OrderMetadata OrderMetadata { get; set; }
}