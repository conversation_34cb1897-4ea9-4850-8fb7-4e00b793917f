﻿using FluentAssertions;
using MassTransit;
using Pondres.Omnia.OrderHub.IntegrationTests.Tests;
using Pondres.Omnia.Print.IntegrationTests.Fixtures;
using Pondres.Omnia.Print.IntegrationTests.Fixtures.Api;
using Pondres.Omnia.Print.IntegrationTests.Fixtures.Integration;
using Pondres.Omnia.Print.IntegrationTests.PrintClient;
using System.Globalization;

namespace Pondres.Omnia.Print.IntegrationTests.Tests.Messaging;

[Collection("IntegrationTests")]
public class PrintBundleMultiDocumentIntegrationTests : BaseIntegrationTest
{
    public PrintBundleMultiDocumentIntegrationTests(IntegrationTestFixture fixture) : base(fixture)
    {
    }

    [Fact]
    public async Task MultiDocument_RemoveDocument_RemovesDocumentFromSingleBundle()
    {
        // Arrange
        var customer = GenerateCustomer();
        var mailDate = DateTimeOffset.Now;

        var metadataCollection = new List<Contracts.Common.PrintDocumentCreationMetadata>
        {
            OrderHubIntegrationFixture.CreatePrintDocumentMetadata(isPrimary: true, mailDate : mailDate),
            OrderHubIntegrationFixture.CreatePrintDocumentMetadata(isPrimary: false, mailDate : mailDate),
            OrderHubIntegrationFixture.CreatePrintDocumentMetadata(isPrimary: false, mailDate : mailDate)
        };

        var documents = await SubmitMultiPrintDocumentOrderAsync(customer, metadataCollection);

        var primaryDocument = documents.Single(x => x.IsPrimary);

        var primaryBundleId = documents.Single(x => x.IsPrimary).LastBundle.BundleId;

        await fixture.ManagerApp.BundleApi.WaitForBundleToBeInState(primaryBundleId, MapPrintBundleState.WaitingForStart);
        await fixture.ManagerApp.BundleApi.ReleaseBundleAsync(primaryBundleId, PrintBundleMode.EmptyGroup);
        await fixture.ManagerApp.BundleApi.WaitForBundleToBeInState(primaryBundleId, MapPrintBundleState.Impositioning);
        await fixture.ScalerIntegration.PublishImpositioningFailedReplyAsync(primaryBundleId, customer);
        await fixture.ManagerApp.BundleApi.WaitForBundleToBeInState(primaryBundleId, MapPrintBundleState.ImpositioningOnHold);

        // Act
        var response = await fixture.ManagerApp.BundleApi.RemoveDocumentFromBundleAsync(primaryBundleId, primaryDocument.Id);

        response.Should().NotBeNull();
        response.RemovedDocumentId.Should().Be(primaryDocument.Id);
        response.PrintBundleId.Should().Be(primaryBundleId);
    }

    [Fact]
    public async Task MultiDocumentBundle_ScheduledRelease_OnlySchedulesPrimaryBundle()
    {
        var customer = GenerateCustomer();
        var mailDate = DateTimeOffset.Now;

        var expectedTime = DateTime.Now.AddMinutes(2).ToString("HH:mm");

        var schedule = ReleaseScheduleApiFixture.CreateReleaseSchedule(scheduleTime: expectedTime);
        await fixture.ManagerApp.ReleaseScheduleApi.CreateReleaseScheduleAsync(schedule);

        OrderHubIntegrationFixture.CreatePrintDocumentMetadata(releaseScheduleId: schedule.Id);

        var metadataCollection = new List<Contracts.Common.PrintDocumentCreationMetadata>
        {
            OrderHubIntegrationFixture.CreatePrintDocumentMetadata(isPrimary: true, releaseScheduleId: schedule.Id),
            OrderHubIntegrationFixture.CreatePrintDocumentMetadata(isPrimary: false, mailDate: mailDate, releaseScheduleId: null),
            OrderHubIntegrationFixture.CreatePrintDocumentMetadata(isPrimary: false, mailDate: mailDate, releaseScheduleId: null)
        };

        var bus = fixture.ResolveWorkerService<IBus>();
        var sendObserver = bus.CreateSendObserver<Contracts.PrintBundle.PrintBundleStartTimeoutExpired>();

        // Act
        var documents = await SubmitMultiPrintDocumentOrderAsync(customer, metadataCollection);

        // Assert
        var bundles = new List<PrintBundleDetails>();
        foreach (var document in documents)
        {
            var bundle = await fixture.ManagerApp.BundleApi.WaitForBundleToBeInState(document.LastBundle.BundleId, MapPrintBundleState.WaitingForStart);
            bundles.Add(bundle!);
        }

        var primaryBundle = bundles.SingleOrDefault(x => x.Metadata.IsPrimary);

        primaryBundle.Should().NotBeNull();
        primaryBundle!.PlannedOn.Should().Be(DateTime.Today.Add(TimeSpan.Parse(expectedTime, CultureInfo.CurrentCulture)));

        sendObserver.Should().HaveSeenMessage(x => x.PrintBundleId.ToString() == primaryBundle.Id);

        var secondaryBundles = bundles.Where(x => !x.Metadata.IsPrimary).ToList();

        foreach (var secondaryBundle in secondaryBundles)
        {
            sendObserver.Should().NotHaveSeenMessage(x => x.PrintBundleId.ToString() == secondaryBundle.Id);
        }
    }

    // Resulting bundles and their secondary bundles cant all get cut off at the same point,
    // resulting in suboptimal result in real life since not all sheetpage count's are the same,
    // it also does not happen much that bundles with secondaries are part of a regular/scheduled flow where this optimization is needed.
    [Fact]
    public async Task MultiDocumentBundle_ScheduledRelease_Fails()
    {
        var customer = GenerateCustomer();
        var mailDate = DateTimeOffset.Now;

        var expectedTime = DateTime.Now.AddMinutes(2).ToString("HH:mm");

        var schedule = ReleaseScheduleApiFixture.CreateReleaseSchedule(scheduleTime: expectedTime, type: PrintBundleMode.MaxFullSheets);
        await fixture.ManagerApp.ReleaseScheduleApi.CreateReleaseScheduleAsync(schedule);

        OrderHubIntegrationFixture.CreatePrintDocumentMetadata(releaseScheduleId: schedule.Id);

        var metadataCollection = new List<Contracts.Common.PrintDocumentCreationMetadata>
        {
            OrderHubIntegrationFixture.CreatePrintDocumentMetadata(isPrimary: true, releaseScheduleId: schedule.Id),
            OrderHubIntegrationFixture.CreatePrintDocumentMetadata(isPrimary: false, mailDate: mailDate, releaseScheduleId: null),
            OrderHubIntegrationFixture.CreatePrintDocumentMetadata(isPrimary: false, mailDate: mailDate, releaseScheduleId: null)
        };

        var bus = fixture.ResolveWorkerService<IBus>();
        var sendObserver = bus.CreateSendObserver<Contracts.PrintBundle.PrintBundleStartTimeoutExpired>();

        // Act
        var taskStarted = await fixture.OrderHubIntegration.SubmitMultiPrintDocumentOrder(customer, metadataCollection, expectsSuccess: false);

        // Assert
    }

    [Fact]
    public async Task MultiDocumentBundle_ManualRelease_CompletesAllBundles()
    {
        var customer = GenerateCustomer();
        var mailDate = DateTimeOffset.Now;

        var metadataCollection = new List<Contracts.Common.PrintDocumentCreationMetadata>
        {
            OrderHubIntegrationFixture.CreatePrintDocumentMetadata(isPrimary: true, mailDate: mailDate),
            OrderHubIntegrationFixture.CreatePrintDocumentMetadata(isPrimary: false, mailDate : mailDate),
            OrderHubIntegrationFixture.CreatePrintDocumentMetadata(isPrimary: false, mailDate : mailDate)
        };

        var documents = await SubmitMultiPrintDocumentOrderAsync(customer, metadataCollection);

        foreach (var document in documents)
        {
            await fixture.ManagerApp.BundleApi.WaitForBundleToBeInState(document.LastBundle.BundleId, MapPrintBundleState.WaitingForStart);
        }

        var primaryBundleId = documents.Single(x => x.IsPrimary).LastBundle.BundleId;
        var secondaryBundleIds = documents.Where(x => !x.IsPrimary).Select(x => x.LastBundle.BundleId).ToList();

        // Act
        await fixture.ManagerApp.BundleApi.ReleaseBundleAsync(primaryBundleId, PrintBundleMode.EmptyGroup, releaseTime: DateTimeOffset.Now.AddHours(1));

        // Assert
        await fixture.ManagerApp.BundleApi.WaitForBundleToBeInState(primaryBundleId, MapPrintBundleState.Impositioning);
        await fixture.ScalerIntegration.PublishImpositioningCompletedReplyAsync(primaryBundleId, customer);
        await fixture.ManagerApp.BundleApi.WaitForBundleToBeInState(primaryBundleId, MapPrintBundleState.WaitingForPrint);
        await fixture.ManagerApp.BundleApi.ConfirmBundleByPrintAsync(primaryBundleId);
        await fixture.ManagerApp.BundleApi.WaitForBundleToBeInState(primaryBundleId, MapPrintBundleState.Completed);

        var primaryBundleDetails = await fixture.ManagerApp.BundleApi.GetDetailsAsync(primaryBundleId);
        primaryBundleDetails!.BatchName.Should().NotBeNullOrEmpty();

        foreach (var secondaryBundleId in secondaryBundleIds)
        {
            await fixture.ManagerApp.BundleApi.WaitForBundleToBeInState(secondaryBundleId, MapPrintBundleState.Impositioning);
            await fixture.ScalerIntegration.PublishImpositioningCompletedReplyAsync(secondaryBundleId, customer);
            await fixture.ManagerApp.BundleApi.WaitForBundleToBeInState(secondaryBundleId, MapPrintBundleState.WaitingForPrint);
            await fixture.ManagerApp.BundleApi.ConfirmBundleByPrintAsync(secondaryBundleId);
            await fixture.ManagerApp.BundleApi.WaitForBundleToBeInState(secondaryBundleId, MapPrintBundleState.Completed);

            var secondaryBundleDetails = await fixture.ManagerApp.BundleApi.GetDetailsAsync(secondaryBundleId);
            secondaryBundleDetails!.BatchName.Should().Be(primaryBundleDetails.BatchName);
        }
    }

    // Resulting bundles and their secondary bundles cant all get cut off at the same point,
    // resulting in suboptimal result in real life since not all sheetpage count's are the same,
    // it also does not happen much that bundles with secondaries are part of a regular/scheduled flow where this optimization is needed.
    [Fact]
    public async Task MultiDocumentBundle_ManualRelease_MaxFullSheets_NotSupportedException()
    {
        var customer = GenerateCustomer();
        var mailDate = DateTimeOffset.Now;

        var metadataCollection = new List<Contracts.Common.PrintDocumentCreationMetadata>
        {
            OrderHubIntegrationFixture.CreatePrintDocumentMetadata(isPrimary: true, mailDate: mailDate),
            OrderHubIntegrationFixture.CreatePrintDocumentMetadata(isPrimary: false, mailDate : mailDate),
            OrderHubIntegrationFixture.CreatePrintDocumentMetadata(isPrimary: false, mailDate : mailDate)
        };

        var documents = await SubmitMultiPrintDocumentOrderAsync(customer, metadataCollection);

        foreach (var document in documents)
        {
            await fixture.ManagerApp.BundleApi.WaitForBundleToBeInState(document.LastBundle.BundleId, MapPrintBundleState.WaitingForStart);
        }

        var primaryBundleId = documents.Single(x => x.IsPrimary).LastBundle.BundleId;
        var secondaryBundleIds = documents.Where(x => !x.IsPrimary).Select(x => x.LastBundle.BundleId).ToList();

        // Act
        var action = FluentActions.Awaiting(
            () => fixture.ManagerApp.BundleApi.ReleaseBundleAsync(primaryBundleId, PrintBundleMode.MaxFullSheets, releaseTime: DateTimeOffset.Now.AddHours(1)));

        // Assert
        await action.Should().ThrowAsync<NotSupportedException>();
    }
}
