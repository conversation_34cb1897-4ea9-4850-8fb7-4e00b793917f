﻿using MassTransit;
using Pondres.Omnia.Print.Business.Exceptions;
using Pondres.Omnia.Print.Business.Service;
using Pondres.Omnia.Print.Contracts.Api.Bundle;
using Pondres.Omnia.Print.Contracts.Common;
using Pondres.Omnia.Print.Contracts.PrintBundle;
using System;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Handler;

public class PrintBundleActionHandler(
    IPrintBundleService printBundleService,
    IBus bus) : IPrintBundleActionHandler
{
    public async Task<PrintBundleReleaseResponse> ReleaseBundleAsync(PrintBundleReleaseRequest request)
    {
        // Check tests for reason for this. Basically real life limitation.
        if (request.BundleOptions?.BundleMode == PrintBundleMode.MaxFullSheets)
        {
            var bundle = await printBundleService.GetBundleDetailsAsync(request.PrintBundleId);

            if (bundle.SecondaryBundles.Count > 0)
                throw new NotSupportedException("PrintBundleMode MaxFullSheets cannot be used on bundles that have secondary bundles!");
        }

        var requestClient = bus.CreateRequestClient<PrintBundleReleaseRequest>();

        var (continueResponse, notFoundResponse) =
            await requestClient.GetResponse<PrintBundleReleaseResponse, PrintBundleNotFoundResponse>(request);

        if (continueResponse.IsCompletedSuccessfully)
        {
            var result = await continueResponse;
            return result.Message;
        }
        else
        {
            var result = await notFoundResponse;
            throw new PrintBundleNotFoundException(result.Message.PrintBundleId.ToString());
        }
    }

    public async Task<PrintBundleCancelResponse> CancelBundleAsync(PrintBundleCancelCommand command)
    {
        var requestClient = bus.CreateRequestClient<PrintBundleCancelRequest>();

        var (continueResponse, notFoundResponse) =
            await requestClient.GetResponse<PrintBundleCancelResponse, PrintBundleNotFoundResponse>(
            new PrintBundleCancelRequest
            {
                PrintBundleId = command.PrintBundleId,
                Reprint = command.Reprint,
                Force = command.Force,
                Username = command.Username
            });

        if (continueResponse.IsCompletedSuccessfully)
        {
            var result = await continueResponse;

            return result.Message;
        }
        else
        {
            var result = await notFoundResponse;

            throw new PrintBundleNotFoundException(result.Message.PrintBundleId.ToString());
        }
    }

    public async Task<PrintBundleManualContinueResponse> ContinueBundleAsync(PrintBundleContinueCommand command)
    {
        var requestClient = bus.CreateRequestClient<PrintBundleManualContinueRequest>();

        var (continueResponse, notFoundResponse) =
            await requestClient.GetResponse<PrintBundleManualContinueResponse, PrintBundleNotFoundResponse>(
            new PrintBundleManualContinueRequest
            {
                PrintBundleId = command.PrintBundleId,
                Force = command.Force,
                Username = command.Username
            });

        if (continueResponse.IsCompletedSuccessfully)
        {
            var result = await continueResponse;

            return result.Message;
        }
        else
        {
            var result = await notFoundResponse;

            throw new PrintBundleNotFoundException(result.Message.PrintBundleId.ToString());
        }
    }

    public async Task<PrintBundlePrintConfirmationResponse> PrintConfirmBundleAsync(PrintBundlePrintConfirmCommand command)
    {
        var requestClient = bus.CreateRequestClient<PrintBundlePrintConfirmationRequest>();

        var (confirmationResponse, notFoundResponse) =
            await requestClient.GetResponse<PrintBundlePrintConfirmationResponse, PrintBundleNotFoundResponse>(
            new PrintBundlePrintConfirmationRequest
            {
                PrintBundleId = command.PrintBundleId,
                Username = command.Username
            });

        if (confirmationResponse.IsCompletedSuccessfully)
        {
            var result = await confirmationResponse;

            return result.Message;
        }
        else
        {
            var result = await notFoundResponse;

            throw new PrintBundleNotFoundException(result.Message.PrintBundleId.ToString());
        }
    }

    public async Task<PrintBundleCompletedConfirmationResponse> CompletedConfirmBundleAsync(PrintBundleCompletedConfirmCommand command)
    {
        var requestClient = bus.CreateRequestClient<PrintBundleCompletedConfirmationRequest>();

        var (confirmationResponse, notFoundResponse) =
            await requestClient.GetResponse<PrintBundleCompletedConfirmationResponse, PrintBundleNotFoundResponse>(
            new PrintBundleCompletedConfirmationRequest
            {
                PrintBundleId = command.PrintBundleId,
                Username = command.Username
            });

        if (confirmationResponse.IsCompletedSuccessfully)
        {
            var result = await confirmationResponse;

            return result.Message;
        }
        else
        {
            var result = await notFoundResponse;

            throw new PrintBundleNotFoundException(result.Message.PrintBundleId.ToString());
        }
    }

    public async Task<PrintBundleScannedConfirmationResponse> ScannedConfirmBundleAsync(PrintBundleScannedConfirmCommand command)
    {
        var requestClient = bus.CreateRequestClient<PrintBundleScannedConfirmationRequest>();

        var (confirmationResponse, notFoundResponse) =
            await requestClient.GetResponse<PrintBundleScannedConfirmationResponse, PrintBundleNotFoundResponse>(
                new PrintBundleScannedConfirmationRequest
                {
                    PrintBundleId = command.PrintBundleId,
                    Username = command.Username
                });

        if (confirmationResponse.IsCompletedSuccessfully)
        {
            var result = await confirmationResponse;

            return result.Message;
        }
        else
        {
            var result = await notFoundResponse;

            throw new PrintBundleNotFoundException(result.Message.PrintBundleId.ToString());
        }
    }

    public async Task<PrintBundleStatusResponse> RawStatusAsync(Guid printBundleId)
    {
        var requestClient = bus.CreateRequestClient<PrintBundleStatusRequest>();

        var (statusResponse, notFoundResponse) = await requestClient.GetResponse<PrintBundleStatusResponse, PrintBundleNotFoundResponse>(
            new PrintBundleStatusRequest
            {
                PrintBundleId = printBundleId
            });

        if (statusResponse.IsCompletedSuccessfully)
        {
            var result = await statusResponse;

            return result.Message;
        }
        else
        {
            var result = await notFoundResponse;

            throw new PrintBundleNotFoundException(result.Message.PrintBundleId.ToString());
        }
    }

    public async Task<PrintBundleDocumentRemovedResponse> RemoveDocumentFromBundleAsync(PrintBundleRemoveDocumentCommand command)
    {
        var requestClient = bus.CreateRequestClient<PrintBundleRemoveDocumentRequest>();

        var (documentRemovedResponse, notFoundResponse, documentNotFoundResponse) = await requestClient
            .GetResponse<PrintBundleDocumentRemovedResponse, PrintBundleNotFoundResponse, PrintBundleDocumentNotFoundResponse>(
                new PrintBundleRemoveDocumentRequest(PrintBundleId: command.PrintBundleId, DocumentId: command.DocumentId));

        if (documentRemovedResponse.IsCompletedSuccessfully)
        {
            var result = await documentRemovedResponse;

            return result.Message;
        }
        else if (notFoundResponse.IsCompletedSuccessfully)
        {
            var result = await notFoundResponse;

            throw new PrintBundleNotFoundException(result.Message.PrintBundleId.ToString());
        }
        else
        {
            var result = await documentNotFoundResponse;

            throw new PrintDocumentNotFoundException(
                printDocument: result.Message.DocumentId,
                printBundle: result.Message.PrintBundleId.ToString());
        }
    }
}
