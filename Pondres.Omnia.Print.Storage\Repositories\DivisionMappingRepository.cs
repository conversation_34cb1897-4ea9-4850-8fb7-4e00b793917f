﻿using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.Caching.Memory;
using Solude.StorageBase;
using Pondres.Omnia.Print.Storage.Configuration;
using Pondres.Omnia.Print.Storage.Entities.Mapping;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Storage.Repositories;

public class DivisionMappingRepository : SinglePartitionBaseRepository<DivisionMappingEntity>, IDivisionMappingRepository
{
    protected override bool CanWrite => true;

    protected override string PartitionKeyValue => CosmosConfiguration.DivisionMappingPartitionValue;

    protected override MemoryCacheEntryOptions CreateCacheOptions() =>
        new MemoryCacheEntryOptions
        {
            SlidingExpiration = TimeSpan.FromMinutes(1)
        };

    public DivisionMappingRepository(CosmosClient cosmosClient, IMemoryCache memoryCache) :
        base(cosmosClient, CosmosConfiguration.PrintDatabaseName, CosmosConfiguration.DivisionMappingContainerName, memoryCache)
    {
    }

    protected override string GetCacheKey(string id, string partitionKeyValue) => $"division_mapping_{id}_{partitionKeyValue}";

    public async Task<List<DivisionMappingEntity>> GetAllForCustomersAsync(IEnumerable<string> customers)
    {

        var allEntities = new List<DivisionMappingEntity>();

        string? continuationToken = null;

        var query = container.GetItemLinqQueryable<DivisionMappingEntity>()
                       .Where(mapping => customers.Contains(mapping.Id))
                       .OrderBy(mapping => mapping.Id);

        do
        {
            var pagedEntityList = await GetEntityListAsync(query, continuationToken, 100);

            allEntities.AddRange(pagedEntityList.Entities);

            continuationToken = pagedEntityList.ContinuationToken;

        } while (!string.IsNullOrEmpty(continuationToken));

        return allEntities;
    }

}
