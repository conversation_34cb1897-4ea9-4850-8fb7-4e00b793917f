## Parameters
$SUBSCRIPTION_ID = "cfff0104-9aa6-4296-afc9-b72794ec572b"
$RESOURCEGROUP  = "rg-quadient-staging"
$LOCATION        = "westeurope"
$STORAGE_SKU = "Standard_LRS"
$RESOURCEGROUPAKS  = "rg-omnia-staging"
$CLUSTERNAME = "aks-omnia-staging"

az account set --subscription $SUBSCRIPTION_ID

# PondresSync Shared Storage
    $STORAGE_NAME = "stpondressyncstaging"
    $AKS_FILESHARE ="scaler-pondressync-storage"

    # Get storage account key
    $STORAGE_KEY=az storage account keys list --resource-group $RESOURCEGROUP --account-name $STORAGE_NAME --query "[0].value" -o tsv
    Write-Output $STORAGE_KEY

    # Echo storage account name and key
    Write-Output Storage account name: $STORAGE_NAME
    Write-Output Storage account key: $STORAGE_KEY

    # Create storage secret
    az aks get-credentials --name $CLUSTERNAME --resource-group $RESOURCEGR<PERSON>UPAKS
    kubectl config use-context $CLUSTERNAME 
    kubectl create secret generic pondressync-shared-storage-secret --namespace omnia --from-literal=azurestorageaccountname=$STORAGE_NAME --from-literal=azurestorageaccountkey=$STORAGE_KEY