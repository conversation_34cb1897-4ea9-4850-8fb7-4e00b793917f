﻿using Pondres.Omnia.Print.Contracts.Common;
using System;

namespace Pondres.Omnia.Print.Contracts.Api.Document
{
    public class PrintDocumentListItem
    {
        public string Id { get; set; } = string.Empty;

        public OrderMetadata OrderMetadata { get; set; } = new();

        public string? CustomerDocumentReference { get; set; }

        public bool IsPrimary { get; set; }

        public DateTimeOffset CreatedOn { get; set; }

        public DateTimeOffset LastUpdatedOn { get; set; }

        public PrintDocumentBundle? LastBundle { get; set; }
    }
}
