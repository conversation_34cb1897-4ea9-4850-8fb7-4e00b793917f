﻿using MassTransit;
using Pondres.Omnia.Print.Business.Handler.PrintBatch;
using Pondres.Omnia.Print.Contracts.PrintBatch;
using Serilog;
using System.Threading.Tasks;

namespace Pondres.Omnia.Print.Business.Consumers;

public class PrintBundleBatchAssignConsumer :
    IConsumer<PrintBundleTaskAssignRequest>
{
    private readonly IPrintBatchOrchestrationHandler printBatchOrchestrationHandler;
    private readonly ILogger logger;

    public PrintBundleBatchAssignConsumer(IPrintBatchOrchestrationHandler printBatchOrchestrationHandler, ILogger logger)
    {
        this.printBatchOrchestrationHandler = printBatchOrchestrationHandler;
        this.logger = logger;
    }

    public async Task Consume(ConsumeContext<PrintBundleTaskAssignRequest> context)
    {
        logger.Information("PrintBundleBatchAssignConsumer received PrintBundleTaskAssignRequest for PrintBundle {PrintBundleId}", context.Message.PrintBundleId);
        await printBatchOrchestrationHandler.AddDataToBatchAsync(context, context.Message);
    }
}
