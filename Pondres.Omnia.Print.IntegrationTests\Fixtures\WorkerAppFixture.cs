﻿using FluentAssertions;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.AspNetCore.TestHost;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Pondres.Omnia.OrderHub.IntegrationTests.Helper;
using Pondres.Omnia.Print.Business.Handler.PrintBundleBuffer;
using Solude.ApiBase.Contracts;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Pondres.Omnia.Print.IntegrationTests.Fixtures;

public class WorkerAppFixture : WebApplicationFactory<PrintWorker.Program>
{
    private readonly string cosmosDbEndpoint;

    public HttpClient Client { get; private set; } = default!;

    public ContainersFixture Containers { get; }

    public WorkerAppFixture(
        string cosmosDbEndpoint,
        ContainersFixture containers)
    {
        Containers = containers;

        this.cosmosDbEndpoint = cosmosDbEndpoint;
    }

    public TService ResolveService<TService>() where TService : notnull
    {
        var scope = Services.CreateScope();

        return scope.ServiceProvider.GetRequiredService<TService>();
    }

    public async Task InitializeAsync()
    {
        Client = CreateClient();
        Client.DefaultRequestHeaders.Add("x-token", "dev");

        var jsonOptions = new JsonSerializerOptions(JsonSerializerDefaults.Web);
        jsonOptions.Converters.Add(new JsonStringEnumConverter());

        var startupTasks = ResolveService<IEnumerable<IStartupTask>>();
        foreach (var startupTask in startupTasks)
        {
            await startupTask.ExecuteAsync();
        }

        await TestHelper.WaitForAsync(async () =>
        {
            Console.Out.WriteLine($"Checking if the service is ready");
            var readyResult = await Client.GetAsync("health/ready");
            readyResult.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
        });
    }

    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        builder.ConfigureAppConfiguration((context, builder) =>
        {
            var configuration = new Dictionary<string, string?>
            {
                // Overwrite configuration with connectionstrings to the created services
                { "DataSeedDisabled", "true" },
                { "NavisionXMLInfoLogicAppConnectionString", "debug" },
                { "AzuriteConnectionString", Containers.Azurite.GetConnectionString() },
                { "RabbitConnectionString", Containers.RabbitMq.GetConnectionString() },
                { "RedisConnectionString", Containers.Redis.GetConnectionString() },
                { "CosmosDbEndpoint", cosmosDbEndpoint }
            };

            builder.AddInMemoryCollection(configuration);
        });
        builder.ConfigureTestServices(services =>
        {
            // configure services here
            services.Configure<PrintBundleBufferOptions>(options =>
            {
                options.BufferLimit = 1;
            });
        });

        builder.UseEnvironment("Development");
    }
}
