﻿namespace Pondres.Omnia.Print.Storage.Configuration;

public static class CosmosConfiguration
{
    public const string PrintDatabaseName = "Print";
    public const string PrintDataSheetContainerName = "DataSheet";
    public const string PrintDataSheetPartitionKey = "/type";

    public const string PrintBundleContainerName = "PrintBundle";
    public const string PrintBundlePartitionKey = "/type";
    public const string PrintBundlePartitionValue = "printbundle";
    public const string PrintBundleVersion = "1.2";

    public const string PrintSheetMappingPartitionValue = "sheetmapping";
    public const string PrintSheetMappingVersion = "1.0";

    public const string PrintDocumentContainerName = "PrintDocument";
    public const string PrintDocumentPartitionKey = "/type";
    public const string PrintDocumentPartitionValue = "printdocument";
    public const string PrintDocumentVersion = "1.1";

    public const string DivisionMappingContainerName = "DivisionMapping";
    public const string DivisionMappingPartitionKey = "/type";
    public const string DivisionMappingPartitionValue = "divisionmapping";
    public const string DivisionMappingVersion = "1.0";

    public const string PrintBundleStatusHistoryContainerName = "PrintBundleStatusHistory";
    public const string PrintBundleStatusHistoryPartitionKey = "/bundleId";
    public const string PrintBundleStatusHistoryVersion = "1.0";

    public const string ConfigurationContainerName = "Configuration";
    public const string ConfigurationPartitionKey = "/type";
    public const string ConfigurationPartitionValue = "DGCode";
    public const string ConfigurationVersion = "1.1";

    public const string SchedulesContainerName = "Schedules";
    public const string SchedulesPartitionKey = "/type";
    public const string ReleaseSchedulesPartitionValue = "releaseSchedule";
    public const string OverrideSchedulesPartitionValue = "overrideReleaseSchedule";
    public const string ScheduleVersion = "1.0";

    public const string NavisionArticleContainerName = "NavisionArticle";
    public const string NavisionArticlePartitionValue = "navisionarticle";
    public const string NavisionArticleVersion = "1.0";
}