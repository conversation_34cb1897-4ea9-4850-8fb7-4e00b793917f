﻿using System;
using System.Collections.Generic;

namespace Pondres.Omnia.Print.Contracts.Api.Document
{
    public class PrintDocumentBatchListFilter
    {
        public string? ContinuationToken { get; set; }

        public int MaxPageSize { get; set; }

        public List<Guid> OrderIds { get; set; } = new();

        public List<string> References { get; set; } = new();

        public List<string> OrderReferences { get; set; } = new();
        public List<string> Barcodes { get; set; } = new();
        public List<string> GordNumbers { get; set; } = new();
        public List<int?> TaskNumbers { get; set; } = new();
        public List<int> SequenceIds { get; set; } = new();
    }
}